# 图片文件命名规范和目录结构

## 📁 图片目录结构

```
miniprogram/images/
├── 品牌标识/
│   ├── 慧心制药-logo.png              # 主logo
│   ├── 慧心制药-logo-白色.png          # 白色版本logo
│   └── 慧心制药-图标.png              # 小图标
├── 轮播图/
│   ├── 轮播图-春季养生.jpg            # 春季养生主题
│   ├── 轮播图-中医文化.jpg            # 中医文化主题
│   ├── 轮播图-产品推荐.jpg            # 产品推荐主题
│   └── 轮播图-健康生活.jpg            # 健康生活主题
├── 中药材/
│   ├── 中药材-人参.jpg               # 人参图片
│   ├── 中药材-枸杞子.jpg             # 枸杞子图片
│   ├── 中药材-当归.jpg               # 当归图片
│   ├── 中药材-黄芪.jpg               # 黄芪图片
│   ├── 中药材-甘草.jpg               # 甘草图片
│   └── 中药材-占位符.png             # 通用占位符
├── 文创产品/
│   ├── 文创-茶具套装.jpg             # 中医养生茶具套装
│   ├── 文创-本草纲目.jpg             # 本草纲目典藏版
│   ├── 文创-艾灸套装.jpg             # 艾灸养生套装
│   ├── 文创-香薰炉.jpg               # 中药香薰炉
│   ├── 文创-穴位图.jpg               # 中医经络穴位图
│   ├── 文创-药膳食谱.jpg             # 养生药膳食谱
│   └── 文创-占位符.png               # 通用占位符
├── 养生文章/
│   ├── 文章-春季养生.jpg             # 春季养生文章配图
│   ├── 文章-食疗智慧.jpg             # 中医食疗文章配图
│   ├── 文章-茶饮推荐.jpg             # 四季茶饮文章配图
│   ├── 文章-五脏调理.jpg             # 五脏调理文章配图
│   └── 文章-占位符.png               # 通用占位符
├── 界面图标/
│   ├── 图标-中药材.png               # 中药材页面图标
│   ├── 图标-文创产品.png             # 文创产品页面图标
│   ├── 图标-养生文章.png             # 养生文章页面图标
│   ├── 图标-购物车.png               # 购物车图标
│   ├── 图标-个人中心.png             # 个人中心图标
│   ├── 图标-设置.png                 # 设置图标
│   ├── 图标-搜索.png                 # 搜索图标
│   ├── 图标-收藏.png                 # 收藏图标
│   ├── 图标-分享.png                 # 分享图标
│   └── 图标-返回.png                 # 返回图标
├── 空状态图片/
│   ├── 空状态-中药材.png             # 中药材列表为空
│   ├── 空状态-文创产品.png           # 产品列表为空
│   ├── 空状态-养生文章.png           # 文章列表为空
│   ├── 空状态-购物车.png             # 购物车为空
│   ├── 空状态-搜索结果.png           # 搜索无结果
│   └── 空状态-网络错误.png           # 网络连接错误
├── 用户头像/
│   ├── 头像-默认.png                 # 默认用户头像
│   ├── 头像-管理员.png               # 管理员头像
│   └── 头像-占位符.png               # 头像占位符
└── 背景图片/
    ├── 背景-登录页面.jpg             # 登录页面背景
    ├── 背景-中医风格.jpg             # 中医风格背景
    └── 背景-纹理.jpg                 # 纹理背景
```

## 🔄 文件名映射表

### 原英文命名 → 新中文命名

#### 品牌相关
```
logo.png → 慧心制药-logo.png
logo-white.png → 慧心制药-logo-白色.png
icon.png → 慧心制药-图标.png
```

#### 轮播图
```
banner1.jpg → 轮播图-春季养生.jpg
banner2.jpg → 轮播图-中医文化.jpg
banner3.jpg → 轮播图-产品推荐.jpg
banner4.jpg → 轮播图-健康生活.jpg
```

#### 中药材图片
```
medicine1.jpg → 中药材-人参.jpg
medicine2.jpg → 中药材-枸杞子.jpg
medicine3.jpg → 中药材-当归.jpg
medicine4.jpg → 中药材-黄芪.jpg
medicine5.jpg → 中药材-甘草.jpg
empty-medicine.png → 中药材-占位符.png
```

#### 文创产品图片
```
product1.jpg → 文创-茶具套装.jpg
product2.jpg → 文创-本草纲目.jpg
product3.jpg → 文创-艾灸套装.jpg
product4.jpg → 文创-香薰炉.jpg
product5.jpg → 文创-穴位图.jpg
product6.jpg → 文创-药膳食谱.jpg
empty-product.png → 文创-占位符.png
```

#### 养生文章图片
```
article1.jpg → 文章-春季养生.jpg
article2.jpg → 文章-食疗智慧.jpg
article3.jpg → 文章-茶饮推荐.jpg
article4.jpg → 文章-五脏调理.jpg
empty-article.png → 文章-占位符.png
```

#### 界面图标
```
medicine-icon.png → 图标-中药材.png
product-icon.png → 图标-文创产品.png
article-icon.png → 图标-养生文章.png
cart-icon.png → 图标-购物车.png
user-icon.png → 图标-个人中心.png
settings-icon.png → 图标-设置.png
search-icon.png → 图标-搜索.png
favorite-icon.png → 图标-收藏.png
share-icon.png → 图标-分享.png
back-icon.png → 图标-返回.png
```

#### 其他图片
```
default-avatar.png → 头像-默认.png
admin-avatar.png → 头像-管理员.png
login-bg.jpg → 背景-登录页面.jpg
empty-cart.png → 空状态-购物车.png
no-network.png → 空状态-网络错误.png
```

## 📝 数据库路径更新

### 更新后的图片路径格式

#### 中药材数据
```json
{
  "name": "人参",
  "imageUrl": "/images/中药材/中药材-人参.jpg"
}
```

#### 文创产品数据
```json
{
  "name": "中医养生茶具套装",
  "imageUrl": "/images/文创产品/文创-茶具套装.jpg"
}
```

#### 养生文章数据
```json
{
  "title": "春季养生：顺应自然，调养身心",
  "imageUrl": "/images/养生文章/文章-春季养生.jpg"
}
```

## 🛠️ 实施步骤

### 第一步：创建目录结构
```bash
# 在 miniprogram/images/ 下创建子目录
mkdir -p miniprogram/images/品牌标识
mkdir -p miniprogram/images/轮播图
mkdir -p miniprogram/images/中药材
mkdir -p miniprogram/images/文创产品
mkdir -p miniprogram/images/养生文章
mkdir -p miniprogram/images/界面图标
mkdir -p miniprogram/images/空状态图片
mkdir -p miniprogram/images/用户头像
mkdir -p miniprogram/images/背景图片
```

### 第二步：准备图片文件
根据上述命名规范准备相应的图片文件，可以使用：
- 真实的中药材、产品、文章图片
- 高质量的占位符图片
- 符合中医风格的设计图片

### 第三步：更新数据库路径
更新 JSON Lines 文件中的图片路径：

```javascript
// 中药材数据示例
{"name": "人参", "imageUrl": "/images/中药材/中药材-人参.jpg", ...}
{"name": "枸杞子", "imageUrl": "/images/中药材/中药材-枸杞子.jpg", ...}
```

### 第四步：更新代码中的图片引用
```javascript
// 更新首页轮播图
bannerList: [
  {
    imageUrl: "/images/轮播图/轮播图-春季养生.jpg",
    title: "春季养生",
    description: "顺应自然，调养身心"
  }
]

// 更新logo引用
<image class="logo" src="/images/品牌标识/慧心制药-logo.png" mode="aspectFit"></image>
```

## 🎨 图片设计建议

### 中药材图片
- **尺寸**: 400x400px
- **格式**: JPG
- **风格**: 清晰的产品图，白色或浅色背景
- **内容**: 展示药材的真实形态

### 文创产品图片
- **尺寸**: 400x400px
- **格式**: JPG
- **风格**: 产品展示图，突出质感
- **内容**: 产品的使用场景或细节

### 养生文章图片
- **尺寸**: 600x400px
- **格式**: JPG
- **风格**: 温馨的生活场景或中医元素
- **内容**: 与文章主题相关的配图

### 界面图标
- **尺寸**: 64x64px
- **格式**: PNG（支持透明）
- **风格**: 简洁的线条图标
- **颜色**: 符合应用主题色彩

## 📋 文件检查清单

### 必需文件
- [ ] 慧心制药-logo.png
- [ ] 中药材-人参.jpg
- [ ] 中药材-枸杞子.jpg
- [ ] 中药材-当归.jpg
- [ ] 中药材-黄芪.jpg
- [ ] 中药材-甘草.jpg
- [ ] 文创-茶具套装.jpg
- [ ] 文创-本草纲目.jpg
- [ ] 文创-艾灸套装.jpg
- [ ] 文创-香薰炉.jpg
- [ ] 文创-穴位图.jpg
- [ ] 文创-药膳食谱.jpg
- [ ] 文章-春季养生.jpg
- [ ] 文章-食疗智慧.jpg
- [ ] 文章-茶饮推荐.jpg
- [ ] 文章-五脏调理.jpg

### 可选文件
- [ ] 轮播图系列
- [ ] 界面图标系列
- [ ] 空状态图片系列
- [ ] 背景图片系列

---

**优势**: 中文命名更直观，便于管理和维护
**注意**: 确保文件名不包含特殊字符，兼容各种系统

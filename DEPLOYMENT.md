# 慧心制药小程序部署指南

本文档详细介绍如何部署"慧心制药"微信小程序。

## 前置条件

1. **微信开发者账号**
   - 注册微信小程序账号
   - 获取小程序的 AppID

2. **开发工具**
   - 下载并安装微信开发者工具
   - 版本要求：支持云开发的版本

3. **云开发服务**
   - 开通微信云开发服务
   - 创建云开发环境

## 部署步骤

### 第一步：项目导入

1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目目录：`/path/to/ywj微信小程序终版`
4. 输入项目名称：`慧心制药`
5. 输入您的小程序 AppID
6. 点击"导入"

### 第二步：配置云开发环境

1. 在微信开发者工具中点击"云开发"按钮
2. 开通云开发服务（如果还未开通）
3. 创建云开发环境，记录环境ID
4. 修改 `miniprogram/app.js` 文件：
   ```javascript
   this.globalData = {
     env: "your-env-id" // 替换为您的环境ID
   };
   ```

### 第三步：部署云函数

1. 在微信开发者工具中，右键点击 `cloudfunctions/quickstartFunctions` 目录
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成（可能需要几分钟）
4. 在云开发控制台中确认云函数部署成功

### 第四步：初始化数据库

1. 打开云开发控制台
2. 进入"数据库"页面
3. 创建以下集合：
   - `users` - 用户表
   - `medicines` - 中药材表
   - `products` - 文创产品表
   - `articles` - 养生文章表

4. 导入示例数据：
   - 参考 `database-init.js` 文件中的示例数据
   - 可以手动添加，也可以通过导入JSON文件的方式

### 第五步：配置云存储

1. 在云开发控制台进入"存储"页面
2. 创建以下目录结构：
   ```
   /medicines/     # 中药材图片
   /products/      # 文创产品图片
   /articles/      # 文章封面图片
   /banners/       # 轮播图图片
   ```

3. 上传默认图片资源（可选）

### 第六步：设置数据库权限

1. 在云开发控制台的数据库页面
2. 为每个集合设置合适的权限：
   - `users`: 仅创建者可读写
   - `medicines`: 所有用户可读，仅管理员可写
   - `products`: 所有用户可读，仅管理员可写
   - `articles`: 所有用户可读，仅管理员可写

### 第七步：测试功能

1. 在微信开发者工具中点击"编译"
2. 测试以下功能：
   - 用户登录（微信登录、游客模式、管理员登录）
   - 数据展示（首页、中药材、文创产品、养生文章）
   - 管理功能（仅管理员）
   - 搜索和筛选功能

### 第八步：上传代码

1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 上传成功后，在微信公众平台提交审核

## 配置说明

### 环境变量配置

在 `miniprogram/app.js` 中需要配置的参数：

```javascript
this.globalData = {
  env: "your-cloud-env-id", // 云开发环境ID
  userInfo: null,
  isAdmin: false
};
```

### 默认管理员账号

系统预设了一个管理员账号，用于初始管理：

- 用户名：`admin`
- 密码：`admin123`

**重要：** 部署到生产环境前，请务必修改默认密码！

### 图片资源配置

项目中使用了以下图片资源，需要准备相应的图片文件：

#### TabBar图标
- `home.png` / `home-active.png` - 首页图标
- `medicine.png` / `medicine-active.png` - 中药材图标
- `product.png` / `product-active.png` - 文创产品图标
- `article.png` / `article-active.png` - 养生文章图标
- `profile.png` / `profile-active.png` - 个人中心图标

#### 其他图片
- `logo.png` - 应用Logo
- `default-avatar.png` - 默认头像
- `banner1.jpg`, `banner2.jpg`, `banner3.jpg` - 轮播图
- 各种功能图标和示例图片

## 常见问题

### 1. 云函数部署失败

**问题：** 云函数上传时报错
**解决：** 
- 检查网络连接
- 确认云开发环境已正确开通
- 重试上传操作

### 2. 数据库连接失败

**问题：** 小程序无法连接数据库
**解决：**
- 检查环境ID是否正确配置
- 确认数据库集合已创建
- 检查数据库权限设置

### 3. 图片显示异常

**问题：** 图片无法正常显示
**解决：**
- 检查图片路径是否正确
- 确认图片文件已上传到云存储
- 检查图片URL格式

### 4. 管理员登录失败

**问题：** 无法使用管理员账号登录
**解决：**
- 确认users集合中已添加管理员数据
- 检查用户名和密码是否正确
- 确认role字段设置为"admin"

## 性能优化建议

1. **图片优化**
   - 使用适当的图片格式和大小
   - 启用云存储CDN加速

2. **数据库优化**
   - 为常用查询字段创建索引
   - 合理设置数据分页大小

3. **代码优化**
   - 使用小程序分包加载
   - 优化页面渲染性能

## 安全注意事项

1. **密码安全**
   - 修改默认管理员密码
   - 实施密码加密存储

2. **数据安全**
   - 设置合适的数据库权限
   - 定期备份重要数据

3. **接口安全**
   - 验证用户权限
   - 防止恶意请求

## 维护和更新

1. **定期备份**
   - 备份数据库数据
   - 备份云存储文件

2. **监控和日志**
   - 监控云函数调用情况
   - 查看错误日志

3. **版本更新**
   - 测试新功能
   - 逐步发布更新

## 技术支持

如果在部署过程中遇到问题，可以：

1. 查看微信开发者文档
2. 在微信开发者社区寻求帮助
3. 联系项目开发团队

---

**注意：** 本项目仅供学习和参考使用，请根据实际需求进行调整和优化。

# 慧心制药数据库 JSON 文件

## 📊 数据概览

本目录包含"慧心制药"小程序的所有测试数据，已转换为 **JSON Lines** 格式，可直接导入微信云数据库。

### 🔍 格式说明
- **JSON Lines 格式**：每行一个完整的 JSON 对象
- **微信云数据库兼容**：符合云数据库导入要求
- **UTF-8 编码**：确保中文字符正确显示

## 📁 文件列表

| 文件名 | 集合名 | 记录数 | 说明 |
|--------|--------|--------|------|
| `users.json` | users | 2 | 用户数据（管理员+测试用户） |
| `medicines.json` | medicines | 5 | 中药材数据 |
| `products.json` | products | 6 | 文创产品数据 |
| `articles.json` | articles | 4 | 养生文章数据 |
| `orders.json` | orders | 3 | 订单数据（示例） |

## 🔑 测试账号

### 管理员账号
- **用户名**: `admin`
- **密码**: `admin123`
- **权限**: 管理员权限，可访问所有功能

### 测试用户
- **用户名**: `test`
- **密码**: `test123`
- **权限**: 普通用户权限

## 📋 数据详情

### 🧑‍💼 用户数据 (users.json)
- 管理员账号：admin
- 测试用户账号：test
- 包含完整的用户信息和权限设置

### 💊 中药材数据 (medicines.json)
- **人参** - 补气药，12.5元/克，库存500克
- **枸杞子** - 补血药，8.8元/克，库存1000克
- **当归** - 补血药，15.6元/克，库存300克
- **黄芪** - 补气药，9.2元/克，库存800克
- **甘草** - 补气药，6.5元/克，库存1200克

### 🎁 文创产品数据 (products.json)
- **中医养生茶具套装** - 168元，库存50件，热门商品
- **本草纲目典藏版** - 58元，库存100件
- **艾灸养生套装** - 128元，库存30件，热门商品
- **中药香薰炉** - 88元，库存25件
- **中医经络穴位图** - 35元，库存80件
- **养生药膳食谱** - 42元，库存60件

### 📖 养生文章数据 (articles.json)
- **春季养生：顺应自然，调养身心** - 春季养生指南
- **中医食疗：药食同源的智慧** - 食疗养生知识
- **四季养生茶饮推荐** - 茶饮养生方法
- **中医养生的五脏调理法** - 中医理论知识

### 📦 订单数据 (orders.json)
- **待付款订单** - 人参50g，总价25元
- **已付款订单** - 茶具套装，总价163元（使用优惠券）
- **已发货订单** - 枸杞子+本草纲目，总价66.8元

## 🚀 快速导入

### 方法1：云开发控制台（推荐）
1. 打开微信开发者工具 → 云开发
2. 进入数据库页面
3. 依次创建集合：users, medicines, products, articles, orders
4. 为每个集合导入对应的 JSON Lines 文件
5. **重要**：选择文件格式为 "JSON Lines"

### 方法2：使用数据导入工具
1. 部署简化版云函数 `simpleFunction`
2. 使用小程序中的"数据导入工具"
3. 一键导入所有测试数据

## ⚙️ 数据库配置

### 权限设置
```json
{
  "read": true,
  "write": true
}
```

### 推荐索引
- `users.username` - 用户登录查询
- `medicines.name` - 药材名称搜索
- `products.name` - 产品名称搜索
- `articles.title` - 文章标题搜索
- `orders.userId` - 用户订单查询

## 🔍 数据验证

导入完成后，可以通过以下方式验证：

### 1. 记录数量检查
- users: 2条记录
- medicines: 5条记录
- products: 6条记录
- articles: 4条记录
- orders: 3条记录

### 2. 功能测试
- [ ] 管理员登录成功
- [ ] 中药材页面显示5种药材
- [ ] 文创产品页面显示6种产品
- [ ] 养生文章页面显示4篇文章
- [ ] 购物车功能正常
- [ ] 订单系统正常

### 3. 数据完整性
- [ ] 所有必填字段都有值
- [ ] 时间格式正确
- [ ] 价格和数量为数字类型
- [ ] 图片路径格式正确

## 📝 注意事项

1. **图片资源**: JSON中的图片路径需要替换为实际的图片地址
2. **时间字段**: 可以根据需要调整为当前时间
3. **用户ID**: orders.json中的userId需要与实际用户ID对应
4. **价格精度**: 建议使用字符串存储价格，避免浮点数精度问题

## 🔄 数据更新

如需更新数据：
1. 修改对应的 JSON 文件
2. 在云开发控制台删除旧数据
3. 重新导入新的 JSON 文件

## 📞 技术支持

如果在数据导入过程中遇到问题：
1. 检查 JSON 文件格式是否正确
2. 确认数据库权限设置
3. 查看云开发控制台的错误日志
4. 使用小程序中的系统诊断工具

---

**最后更新**: 2024-03-15
**数据版本**: v1.0
**兼容性**: 微信云开发

# 数据库集合不存在错误解决指南

## 🚨 错误信息
```
errCode: -502005 database collection not exists
errMsg: [ResourceNotFound] Db or Table not exist
```

## 🔍 问题原因

这个错误表明小程序尝试访问一个不存在的数据库集合。根据错误日志，首页在加载统计数据时查询了以下集合：
- `medicines` - 中药材集合
- `products` - 文创产品集合  
- `articles` - 养生文章集合
- `users` - 用户集合

## ✅ 解决方案

### 方案1：使用数据导入工具（推荐）

#### 第一步：确认数据已导入
1. **打开云开发控制台**
2. **进入数据库页面**
3. **检查是否存在以下集合**：
   - users
   - medicines
   - products
   - articles
   - orders

#### 第二步：如果集合不存在，重新导入
1. **创建缺失的集合**
2. **使用 JSON Lines 格式文件导入数据**
3. **确认导入成功**

### 方案2：使用云函数初始化

#### 第一步：使用系统诊断工具
1. **在登录页面点击"系统诊断"**
2. **运行完整诊断**
3. **查看数据库状态**

#### 第二步：初始化数据
1. **点击"强制初始化用户"**
2. **等待初始化完成**
3. **验证数据创建成功**

### 方案3：手动创建集合

#### 在云开发控制台手动创建：

1. **users 集合**
   ```json
   {"username": "admin", "password": "admin123", "nickName": "系统管理员", "role": "admin", "createTime": "2024-03-15T10:00:00.000Z"}
   ```

2. **medicines 集合**
   ```json
   {"name": "人参", "category": "补气药", "price": 12.5, "stock": 500, "viewCount": 1250}
   ```

3. **products 集合**
   ```json
   {"name": "中医养生茶具套装", "category": "茶具", "price": 168, "stock": 50, "sales": 125}
   ```

4. **articles 集合**
   ```json
   {"title": "春季养生", "category": "春季养生", "author": "张医师", "views": 1250}
   ```

## 🔧 已修复的问题

### 代码优化
我已经修复了首页的数据加载逻辑：

#### ✅ 错误处理优化
- **逐个查询集合**：避免某个集合不存在导致全部失败
- **优雅降级**：集合不存在时使用默认数据
- **详细日志**：记录每个集合的查询状态

#### ✅ 图片问题解决
- **占位符图片**：使用 Base64 编码的 SVG 占位符
- **避免 404 错误**：不再依赖本地图片文件
- **更好的用户体验**：即使图片加载失败也能正常显示

#### ✅ 数据库状态检查
- **自动检测**：页面加载时自动检查数据库状态
- **状态工具**：提供专门的数据库状态检查工具
- **用户提示**：在需要时提示用户导入数据

## 🚀 验证修复效果

### 第一步：重新加载小程序
1. **重新编译小程序**
2. **打开首页**
3. **查看控制台日志**

### 第二步：检查功能
- [ ] 首页不再报错
- [ ] 统计数据正常显示（即使为0）
- [ ] 推荐数据使用占位符显示
- [ ] 图片不再报 500 错误

### 第三步：导入数据（如需要）
- [ ] 使用数据导入工具
- [ ] 或使用系统诊断工具初始化
- [ ] 验证数据正确显示

## 📋 数据导入检查清单

### 导入前检查
- [ ] 云开发环境已开通
- [ ] 数据库权限设置正确
- [ ] JSON Lines 文件格式正确

### 导入过程
- [ ] 创建所需集合
- [ ] 选择正确的文件格式（JSON Lines）
- [ ] 确认导入成功

### 导入后验证
- [ ] 检查记录数量
- [ ] 测试应用功能
- [ ] 验证数据完整性

## 🎯 预期结果

修复后，您应该看到：

### ✅ 正常情况（有数据）
- 首页正常加载
- 统计数据显示实际数量
- 推荐内容显示真实数据
- 图片正常显示

### ✅ 空数据库情况
- 首页正常加载，不报错
- 统计数据显示为 0
- 推荐内容显示占位符数据
- 控制台提示需要导入数据

## 🔍 故障排查

### 如果仍有问题：

#### 1. 检查云函数部署
```bash
# 确认 simpleFunction 已正确部署
右键云函数 → 查看部署状态
```

#### 2. 检查数据库权限
```json
{
  "read": true,
  "write": true
}
```

#### 3. 使用系统诊断工具
```bash
登录页面 → 系统诊断 → 运行完整诊断
```

#### 4. 查看详细日志
```bash
云开发控制台 → 云函数 → 调用日志
```

## 📞 技术支持

如果问题仍然存在，请提供：
1. **错误截图**
2. **云函数日志**
3. **数据库集合列表**
4. **系统诊断结果**

---

**状态**: 已修复
**版本**: v1.2
**更新时间**: 2024-03-15

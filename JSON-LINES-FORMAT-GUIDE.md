# JSON Lines 格式说明

## 🔍 什么是 JSON Lines 格式

JSON Lines（也称为 JSONL 或 NDJSON）是一种文本格式，其中每一行都是一个有效的 JSON 对象。

### 📋 格式对比

#### ❌ 标准 JSON 数组格式（不支持）
```json
[
  {"name": "人参", "price": 12.5},
  {"name": "枸杞子", "price": 8.8}
]
```

#### ✅ JSON Lines 格式（云数据库要求）
```json
{"name": "人参", "price": 12.5}
{"name": "枸杞子", "price": 8.8}
```

## 🎯 为什么使用 JSON Lines

### 微信云数据库要求
- **导入格式**：必须使用 JSON Lines 格式
- **处理效率**：逐行处理，内存占用小
- **错误恢复**：单行错误不影响其他数据

### 技术优势
- **流式处理**：适合大数据量导入
- **容错性强**：单条记录错误不影响整体
- **标准格式**：广泛支持的数据交换格式

## 📁 已转换的文件

### 文件列表
```
database-json/
├── users.json          # 2行，每行一个用户对象
├── medicines.json      # 5行，每行一个中药材对象
├── products.json       # 6行，每行一个产品对象
├── articles.json       # 4行，每行一个文章对象
└── orders.json         # 3行，每行一个订单对象
```

### 文件示例

#### users.json
```json
{"username": "admin", "password": "admin123", "nickName": "系统管理员", "role": "admin"}
{"username": "test", "password": "test123", "nickName": "测试用户", "role": "user"}
```

#### medicines.json
```json
{"name": "人参", "category": "补气药", "price": 12.5, "stock": 500}
{"name": "枸杞子", "category": "补血药", "price": 8.8, "stock": 1000}
```

## 🚀 导入步骤

### 第一步：打开云开发控制台
1. 在微信开发者工具中点击"云开发"
2. 进入"数据库"页面

### 第二步：创建集合
依次创建以下集合：
- `users` - 用户表
- `medicines` - 中药材表
- `products` - 文创产品表
- `articles` - 养生文章表
- `orders` - 订单表

### 第三步：导入数据
对每个集合执行以下操作：

1. **点击集合名称**进入集合页面
2. **点击"导入"按钮**
3. **选择对应的 JSON 文件**
4. **重要：选择文件格式为 "JSON Lines"**
5. **设置冲突处理为 "跳过"**
6. **点击"确定"开始导入**

### 第四步：验证导入
检查每个集合的记录数量：
- users: 2条记录
- medicines: 5条记录
- products: 6条记录
- articles: 4条记录
- orders: 3条记录

## ⚠️ 常见问题

### 问题1：导入失败，提示格式错误
**原因**：选择了错误的文件格式
**解决**：确保选择 "JSON Lines" 格式，不是 "JSON"

### 问题2：中文字符显示乱码
**原因**：文件编码问题
**解决**：确保文件使用 UTF-8 编码

### 问题3：部分数据导入失败
**原因**：单行 JSON 格式错误
**解决**：检查文件中每行是否为有效的 JSON 对象

### 问题4：导入后数据不完整
**原因**：文件末尾可能有空行
**解决**：删除文件末尾的空行

## 🔧 格式验证

### 验证工具
可以使用以下方法验证 JSON Lines 格式：

#### 在线验证
```bash
# 每行应该是有效的 JSON
{"valid": "json", "object": true}
{"another": "valid", "json": "object"}
```

#### 命令行验证（如果有 jq 工具）
```bash
# 验证每行是否为有效 JSON
cat users.json | while read line; do echo "$line" | jq .; done
```

### 格式要求
- ✅ 每行必须是完整的 JSON 对象
- ✅ 每行以换行符结尾
- ✅ 不能有 JSON 数组的方括号 `[]`
- ✅ 不能有对象间的逗号分隔
- ✅ 使用 UTF-8 编码

## 📊 数据统计

### 总体数据量
- **总记录数**：20条
- **总文件大小**：约 15KB
- **字符编码**：UTF-8
- **格式标准**：JSON Lines (RFC 7464)

### 各集合详情
| 集合名 | 记录数 | 主要字段 | 文件大小 |
|--------|--------|----------|----------|
| users | 2 | username, role, nickName | ~500B |
| medicines | 5 | name, category, price, stock | ~3KB |
| products | 6 | name, category, price, sales | ~4KB |
| articles | 4 | title, author, content | ~6KB |
| orders | 3 | userId, status, items, address | ~2KB |

## 🎉 导入成功标志

导入完成后，您应该看到：
- ✅ 所有集合创建成功
- ✅ 记录数量正确
- ✅ 中文字符显示正常
- ✅ 数据结构完整
- ✅ 可以正常查询和使用

## 📞 技术支持

如果在导入过程中遇到问题：

1. **检查文件格式**：确认是 JSON Lines 格式
2. **验证文件编码**：确保使用 UTF-8 编码
3. **查看错误日志**：在云开发控制台查看详细错误
4. **逐个导入**：先导入小文件测试

---

**格式标准**：JSON Lines (RFC 7464)
**编码要求**：UTF-8 without BOM
**兼容性**：微信云开发数据库

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片占位符生成器 - 慧心制药</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #8B4513;
            text-align: center;
            margin-bottom: 30px;
        }
        .generator-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .generator-section h2 {
            color: #2E8B57;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        button {
            background-color: #2E8B57;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #228B22;
        }
        .preview {
            margin-top: 20px;
            text-align: center;
        }
        .preview img {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .download-link {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 16px;
            background-color: #8B4513;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .batch-section {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .image-item img {
            max-width: 100%;
            height: auto;
        }
        .image-item h4 {
            margin: 10px 0 5px 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 慧心制药图片占位符生成器</h1>
        
        <!-- 自定义生成器 -->
        <div class="generator-section">
            <h2>自定义占位符生成</h2>
            <div class="form-group">
                <label>文字内容：</label>
                <input type="text" id="customText" value="慧心制药" placeholder="输入显示文字">
            </div>
            <div class="form-group">
                <label>图片宽度：</label>
                <input type="number" id="customWidth" value="400" min="50" max="1000">
            </div>
            <div class="form-group">
                <label>图片高度：</label>
                <input type="number" id="customHeight" value="400" min="50" max="1000">
            </div>
            <div class="form-group">
                <label>背景颜色：</label>
                <input type="color" id="customBgColor" value="#f5f5f5">
            </div>
            <div class="form-group">
                <label>文字颜色：</label>
                <input type="color" id="customTextColor" value="#999999">
            </div>
            <button onclick="generateCustom()">生成占位符</button>
            <div class="preview" id="customPreview"></div>
        </div>

        <!-- 批量生成器 -->
        <div class="generator-section">
            <h2>批量生成预设占位符</h2>
            <button onclick="generateBatch()">生成所有占位符</button>
            
            <div class="batch-section">
                <h3>📱 中药材占位符 (400x400px)</h3>
                <div class="image-grid" id="medicineGrid"></div>
            </div>
            
            <div class="batch-section">
                <h3>🎁 文创产品占位符 (400x400px)</h3>
                <div class="image-grid" id="productGrid"></div>
            </div>
            
            <div class="batch-section">
                <h3>📖 养生文章占位符 (600x400px)</h3>
                <div class="image-grid" id="articleGrid"></div>
            </div>
            
            <div class="batch-section">
                <h3>🔧 界面图标占位符 (64x64px)</h3>
                <div class="image-grid" id="iconGrid"></div>
            </div>
        </div>
    </div>

    <script>
        // 生成SVG占位符
        function generateSVG(text, width, height, bgColor, textColor) {
            const fontSize = Math.min(width, height) / 8;
            const svg = `
                <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                    <rect width="${width}" height="${height}" fill="${bgColor}"/>
                    <text x="${width/2}" y="${height/2 + fontSize/3}" 
                          font-family="Microsoft YaHei, Arial, sans-serif" 
                          font-size="${fontSize}" 
                          fill="${textColor}" 
                          text-anchor="middle">${text}</text>
                </svg>
            `;
            return 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svg)));
        }

        // 生成自定义占位符
        function generateCustom() {
            const text = document.getElementById('customText').value || '慧心制药';
            const width = parseInt(document.getElementById('customWidth').value) || 400;
            const height = parseInt(document.getElementById('customHeight').value) || 400;
            const bgColor = document.getElementById('customBgColor').value || '#f5f5f5';
            const textColor = document.getElementById('customTextColor').value || '#999999';
            
            const dataUrl = generateSVG(text, width, height, bgColor, textColor);
            
            const preview = document.getElementById('customPreview');
            preview.innerHTML = `
                <img src="${dataUrl}" alt="${text}" style="max-width: 300px;">
                <br>
                <a href="${dataUrl}" download="占位符-${text}-${width}x${height}.svg" class="download-link">
                    下载 ${text} (${width}x${height})
                </a>
            `;
        }

        // 批量生成预设占位符
        function generateBatch() {
            // 中药材占位符
            const medicines = ['人参', '枸杞子', '当归', '黄芪', '甘草'];
            generateImageGrid('medicineGrid', medicines, 400, 400, '中药材-', '#f0f8f0', '#2E8B57');
            
            // 文创产品占位符
            const products = ['茶具套装', '本草纲目', '艾灸套装', '香薰炉', '穴位图', '药膳食谱'];
            generateImageGrid('productGrid', products, 400, 400, '文创-', '#f8f5f0', '#8B4513');
            
            // 养生文章占位符
            const articles = ['春季养生', '食疗智慧', '茶饮推荐', '五脏调理'];
            generateImageGrid('articleGrid', articles, 600, 400, '文章-', '#f5f8f0', '#228B22');
            
            // 界面图标占位符
            const icons = ['中药材', '文创产品', '养生文章', '购物车', '个人中心', '设置', '搜索', '收藏', '分享', '返回'];
            generateImageGrid('iconGrid', icons, 64, 64, '图标-', '#f8f8f8', '#666666');
        }

        // 生成图片网格
        function generateImageGrid(containerId, items, width, height, prefix, bgColor, textColor) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            items.forEach(item => {
                const dataUrl = generateSVG(item, width, height, bgColor, textColor);
                const div = document.createElement('div');
                div.className = 'image-item';
                div.innerHTML = `
                    <img src="${dataUrl}" alt="${item}">
                    <h4>${prefix}${item}</h4>
                    <a href="${dataUrl}" download="${prefix}${item}-${width}x${height}.svg" class="download-link">
                        下载 SVG
                    </a>
                `;
                container.appendChild(div);
            });
        }

        // 页面加载时生成示例
        window.onload = function() {
            generateCustom();
        };
    </script>
</body>
</html>

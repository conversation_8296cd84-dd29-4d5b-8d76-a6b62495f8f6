/**app.wxss**/
/* 全局样式 - 中药主题配色 */
page {
  background-color: #F8F5F0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.container {
  min-height: 100vh;
  background-color: #F8F5F0;
  padding: 20rpx;
  box-sizing: border-box;
}

button {
  background: initial;
}

button:focus{
  outline: 0;
}

button::after{
  border: none;
}

/* 主色调 */
.primary-color {
  color: #A0522D;
}

.secondary-color {
  color: #228B22;
}

.bg-primary {
  background-color: #A0522D;
}

.bg-secondary {
  background-color: #228B22;
}

.bg-light {
  background-color: #F8F5F0;
}

/* 通用按钮样式 */
.btn {
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  text-align: center;
  margin: 10rpx;
  border: none;
}

.btn-primary {
  background-color: #A0522D;
  color: white;
}

.btn-secondary {
  background-color: #228B22;
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid #A0522D;
  color: #A0522D;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.1);
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #A0522D;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

/* 输入框样式 */
.input {
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  margin: 10rpx 0;
}

.input:focus {
  border-color: #A0522D;
}

/* 列表样式 */
.list-item {
  background-color: white;
  border-radius: 15rpx;
  padding: 25rpx;
  margin: 15rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(160, 82, 45, 0.08);
  display: flex;
  align-items: center;
}

/* 图片样式 */
.image-round {
  border-radius: 50%;
}

.image-card {
  border-radius: 15rpx;
}

/* 文本样式 */
.text-center {
  text-align: center;
}

.text-primary {
  color: #A0522D;
}

.text-secondary {
  color: #228B22;
}

.text-muted {
  color: #999;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* 间距样式 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.ml-10 { margin-left: 10rpx; }
.mr-10 { margin-right: 10rpx; }

.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }

/* 购物车动画特效 */
.cart-animation {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
}

.cart-ball {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #A0522D;
  opacity: 0.8;
  animation: cart-fly 1s ease-in-out forwards;
}

@keyframes cart-fly {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  100% {
    transform: scale(0.3);
    opacity: 0;
  }
}

.cart-success-animation {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  pointer-events: none;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #A0522D;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: success-bounce 0.6s ease-out forwards;
}

.success-icon::after {
  content: '✓';
  color: white;
  font-size: 60rpx;
  font-weight: bold;
}

@keyframes success-bounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.cart-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background-color: #E74C3C;
  color: white;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  animation: badge-bounce 0.3s ease-out;
}

@keyframes badge-bounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

/* 商品卡片悬浮效果 */
.product-card, .medicine-card {
  transition: all 0.3s ease;
}

.product-card:active, .medicine-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(160, 82, 45, 0.15);
}

/* 按钮点击效果 */
.cart-btn {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.cart-btn:active {
  transform: scale(0.95);
}

.cart-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.cart-btn:active::after {
  width: 200rpx;
  height: 200rpx;
}
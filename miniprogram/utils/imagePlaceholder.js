// 图片占位符工具
const imagePlaceholder = {
  
  // 生成占位符图片URL
  generatePlaceholder(width = 300, height = 300, text = '图片', bgColor = 'f0f0f0', textColor = '999999') {
    // 使用在线占位符服务或本地占位符
    return `https://via.placeholder.com/${width}x${height}/${bgColor}/${textColor}?text=${encodeURIComponent(text)}`;
  },

  // 处理数据中的图片字段，如果图片不存在则使用占位符
  processDataImages(dataArray, imageField = 'imageUrl', type = 'default', nameField = 'name') {
    return dataArray.map(item => {
      const processedItem = { ...item };
      
      // 如果图片路径不存在或为空，生成占位符
      if (!processedItem[imageField] || processedItem[imageField] === '') {
        const text = processedItem[nameField] || type;
        processedItem[imageField] = this.generatePlaceholder(300, 300, text);
      }
      
      return processedItem;
    });
  },

  // 获取默认占位符图片
  getDefaultImage(type = 'default', text = '图片') {
    const placeholders = {
      'medicine': this.generatePlaceholder(300, 300, text, '8B4513', 'ffffff'),
      'product': this.generatePlaceholder(300, 300, text, '228B22', 'ffffff'),
      'article': this.generatePlaceholder(300, 300, text, 'DAA520', 'ffffff'),
      'banner': this.generatePlaceholder(750, 400, text, '8B4513', 'ffffff'),
      'avatar': this.generatePlaceholder(100, 100, text, 'cccccc', '666666'),
      'default': this.generatePlaceholder(300, 300, text, 'f0f0f0', '999999')
    };
    
    return placeholders[type] || placeholders['default'];
  },

  // 创建CSS背景占位符
  createCSSPlaceholder(width, height, text, bgColor = '#f0f0f0', textColor = '#999') {
    return {
      width: width + 'rpx',
      height: height + 'rpx',
      backgroundColor: bgColor,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: textColor,
      fontSize: '24rpx',
      borderRadius: '10rpx'
    };
  }
};

module.exports = imagePlaceholder;

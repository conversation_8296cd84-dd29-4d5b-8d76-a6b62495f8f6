// 数据库状态检查工具

/**
 * 检查数据库集合状态
 * @returns {Object} 数据库状态信息
 */
const checkDatabaseStatus = async () => {
  const status = {
    collections: {
      users: { exists: false, count: 0, error: null },
      medicines: { exists: false, count: 0, error: null },
      products: { exists: false, count: 0, error: null },
      articles: { exists: false, count: 0, error: null },
      orders: { exists: false, count: 0, error: null }
    },
    summary: {
      totalCollections: 0,
      totalRecords: 0,
      hasData: false,
      errors: []
    }
  };

  const db = wx.cloud.database();
  const collectionNames = Object.keys(status.collections);

  // 检查每个集合
  for (const collectionName of collectionNames) {
    try {
      console.log(`检查集合: ${collectionName}`);
      
      // 尝试查询集合
      const result = await db.collection(collectionName).count();
      
      status.collections[collectionName] = {
        exists: true,
        count: result.total || 0,
        error: null
      };
      
      status.summary.totalRecords += result.total || 0;
      status.summary.totalCollections++;
      
      console.log(`${collectionName} 集合存在，记录数: ${result.total || 0}`);
      
    } catch (error) {
      console.log(`${collectionName} 集合不存在或查询失败:`, error.message);
      
      status.collections[collectionName] = {
        exists: false,
        count: 0,
        error: error.message
      };
      
      status.summary.errors.push({
        collection: collectionName,
        error: error.message
      });
    }
  }

  // 设置汇总信息
  status.summary.hasData = status.summary.totalRecords > 0;

  return status;
};

/**
 * 获取数据库状态的友好描述
 * @param {Object} status 数据库状态
 * @returns {Object} 友好描述
 */
const getStatusDescription = (status) => {
  const description = {
    overall: '',
    details: [],
    recommendations: []
  };

  if (status.summary.totalRecords === 0) {
    description.overall = '数据库为空，需要导入数据';
    description.recommendations.push('使用数据导入工具导入测试数据');
    description.recommendations.push('或使用云函数初始化基础数据');
  } else if (status.summary.errors.length > 0) {
    description.overall = '部分数据库集合存在问题';
    description.recommendations.push('检查缺失的数据库集合');
    description.recommendations.push('重新导入相关数据');
  } else {
    description.overall = '数据库状态正常';
  }

  // 生成详细描述
  Object.keys(status.collections).forEach(collectionName => {
    const collection = status.collections[collectionName];
    if (collection.exists) {
      description.details.push(`${collectionName}: ${collection.count} 条记录`);
    } else {
      description.details.push(`${collectionName}: 不存在或无法访问`);
    }
  });

  return description;
};

/**
 * 显示数据库状态对话框
 */
const showDatabaseStatus = async () => {
  try {
    wx.showLoading({
      title: '检查数据库状态...',
      mask: true
    });

    const status = await checkDatabaseStatus();
    const description = getStatusDescription(status);

    wx.hideLoading();

    const content = [
      `状态: ${description.overall}`,
      '',
      '集合详情:',
      ...description.details,
      '',
      description.recommendations.length > 0 ? '建议:' : '',
      ...description.recommendations
    ].join('\n');

    wx.showModal({
      title: '数据库状态',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });

    return status;

  } catch (error) {
    wx.hideLoading();
    console.error('检查数据库状态失败:', error);
    
    wx.showModal({
      title: '检查失败',
      content: `无法检查数据库状态: ${error.message}`,
      showCancel: false
    });

    return null;
  }
};

/**
 * 检查特定集合是否存在
 * @param {string} collectionName 集合名称
 * @returns {boolean} 是否存在
 */
const checkCollectionExists = async (collectionName) => {
  try {
    const db = wx.cloud.database();
    await db.collection(collectionName).limit(1).get();
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * 获取集合记录数
 * @param {string} collectionName 集合名称
 * @returns {number} 记录数
 */
const getCollectionCount = async (collectionName) => {
  try {
    const db = wx.cloud.database();
    const result = await db.collection(collectionName).count();
    return result.total || 0;
  } catch (error) {
    return 0;
  }
};

/**
 * 检查是否需要导入数据
 * @returns {boolean} 是否需要导入
 */
const needsDataImport = async () => {
  try {
    const status = await checkDatabaseStatus();
    return status.summary.totalRecords === 0;
  } catch (error) {
    return true;
  }
};

module.exports = {
  checkDatabaseStatus,
  getStatusDescription,
  showDatabaseStatus,
  checkCollectionExists,
  getCollectionCount,
  needsDataImport
};

// 数据库检查和初始化工具（修复版 - 只使用云函数）

// 检查数据库连接和数据
const checkDatabase = async () => {
  try {
    console.log('开始检查数据库...');

    // 调用简化版云函数检查数据库
    const result = await wx.cloud.callFunction({
      name: 'simpleFunction',
      data: {
        type: 'checkDatabase'
      }
    });

    console.log('数据库检查结果:', result.result);

    if (result.result && result.result.success) {
      const data = result.result.data;
      console.log('数据库状态:', data);

      // 如果没有用户或没有管理员，返回false
      if (!data.hasUsers || !data.hasAdmin) {
        console.log('数据库需要初始化');
        return false;
      }

      console.log('数据库检查完成，数据正常');
      return true;
    } else {
      console.error('数据库检查失败:', result.result ? result.result.message : '云函数调用失败');
      return false;
    }

  } catch (error) {
    console.error('数据库检查失败:', error);
    return false;
  }
};

// 初始化数据库（使用简化版云函数）
const initDatabase = async () => {
  try {
    console.log('开始数据库初始化...');

    // 调用简化版云函数初始化测试用户
    const result = await wx.cloud.callFunction({
      name: 'simpleFunction',
      data: {
        type: 'initTestUsers'
      }
    });

    console.log('数据库初始化结果:', result.result);

    if (result.result && result.result.success) {
      console.log('数据库初始化完成:', result.result.data);
      return true;
    } else {
      console.error('数据库初始化失败:', result.result ? result.result.message : '云函数调用失败');
      return false;
    }

  } catch (error) {
    console.error('数据库初始化失败:', error);
    return false;
  }
};

// 初始化管理员账号（兼容性保留）
const initAdminUser = async () => {
  return await initDatabase();
};

// 初始化测试用户（兼容性保留）
const initTestUser = async () => {
  return await initDatabase();
};

// 测试管理员登录
const testAdminLogin = async () => {
  try {
    console.log('测试管理员登录...');

    const result = await wx.cloud.callFunction({
      name: 'simpleFunction',
      data: {
        type: 'adminLogin',
        username: 'admin',
        password: 'admin123'
      }
    });

    console.log('管理员登录测试结果:', result);
    return result.result && result.result.success;

  } catch (error) {
    console.error('管理员登录测试失败:', error);
    return false;
  }
};

// 导出工具函数
module.exports = {
  checkDatabase,
  initAdminUser,
  initTestUser,
  initDatabase,
  testAdminLogin
};

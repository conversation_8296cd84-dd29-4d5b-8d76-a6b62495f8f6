// 数据库初始化工具
const app = getApp();

/**
 * 直接在小程序端初始化数据库
 */
const initDatabaseDirect = async () => {
  try {
    console.log('开始直接初始化数据库...');
    
    const db = wx.cloud.database();
    let results = {
      admin: false,
      test: false,
      medicines: false,
      products: false,
      articles: false
    };

    // 1. 创建管理员账号
    try {
      console.log('创建管理员账号...');
      const adminResult = await db.collection('users').add({
        data: {
          username: "admin",
          password: "admin123", 
          nickName: "系统管理员",
          avatarUrl: "/images/用户头像/头像-管理员.png",
          role: "admin",
          createTime: new Date(),
          lastLoginTime: new Date()
        }
      });
      console.log('管理员账号创建成功:', adminResult._id);
      results.admin = true;
    } catch (adminError) {
      console.log('管理员账号可能已存在:', adminError.message);
    }

    // 2. 创建测试用户
    try {
      console.log('创建测试用户...');
      const testResult = await db.collection('users').add({
        data: {
          username: "test",
          password: "test123",
          nickName: "测试用户", 
          avatarUrl: "/images/用户头像/头像-默认.png",
          role: "user",
          createTime: new Date(),
          lastLoginTime: new Date()
        }
      });
      console.log('测试用户创建成功:', testResult._id);
      results.test = true;
    } catch (testError) {
      console.log('测试用户可能已存在:', testError.message);
    }

    // 3. 创建中药材数据
    try {
      console.log('创建中药材数据...');
      const medicinesData = [
        {
          name: "人参",
          category: "补气药",
          effect: "大补元气，复脉固脱，补脾益肺，生津止渴，安神益智",
          description: "人参为五加科植物人参的干燥根和根茎。具有大补元气，复脉固脱，补脾益肺，生津止渴，安神益智的功效。",
          price: 12.5,
          stock: 500,
          imageUrl: "/images/中药材/人参.jpg",
          viewCount: 1250,
          createTime: new Date(),
          updateTime: new Date()
        },
        {
          name: "枸杞子",
          category: "补血药", 
          effect: "滋补肝肾，益精明目",
          description: "枸杞子为茄科植物宁夏枸杞的干燥成熟果实。具有滋补肝肾，益精明目的功效。",
          price: 8.8,
          stock: 1000,
          imageUrl: "/images/中药材/枸杞子.jpg",
          viewCount: 980,
          createTime: new Date(),
          updateTime: new Date()
        },
        {
          name: "当归",
          category: "补血药",
          effect: "补血活血，调经止痛，润肠通便", 
          description: "当归为伞形科植物当归的干燥根。具有补血活血，调经止痛，润肠通便的功效。",
          price: 15.6,
          stock: 300,
          imageUrl: "/images/中药材/当归.jpg",
          viewCount: 756,
          createTime: new Date(),
          updateTime: new Date()
        }
      ];

      for (const medicine of medicinesData) {
        await db.collection('medicines').add({ data: medicine });
      }
      results.medicines = true;
      console.log('中药材数据创建成功');
    } catch (medicinesError) {
      console.log('中药材数据可能已存在:', medicinesError.message);
    }

    // 4. 创建文创产品数据
    try {
      console.log('创建文创产品数据...');
      const productsData = [
        {
          name: "中医养生茶具套装",
          category: "茶具",
          description: "精美的中式茶具，适合日常养生品茶使用",
          price: 168.0,
          stock: 50,
          imageUrl: "/images/文创产品/茶具套装.jpg",
          viewCount: 432,
          createTime: new Date(),
          updateTime: new Date()
        },
        {
          name: "中药香囊",
          category: "香囊",
          description: "传统中药香囊，具有安神助眠的功效",
          price: 28.8,
          stock: 200,
          imageUrl: "/images/文创产品/中药香囊.jpg", 
          viewCount: 567,
          createTime: new Date(),
          updateTime: new Date()
        }
      ];

      for (const product of productsData) {
        await db.collection('products').add({ data: product });
      }
      results.products = true;
      console.log('文创产品数据创建成功');
    } catch (productsError) {
      console.log('文创产品数据可能已存在:', productsError.message);
    }

    // 5. 创建养生文章数据
    try {
      console.log('创建养生文章数据...');
      const articlesData = [
        {
          title: "春季养生小贴士",
          category: "季节养生",
          content: "春季是万物复苏的季节，也是养生的好时机。春季养生要注意以下几点：1. 早睡早起，顺应自然；2. 饮食清淡，多吃蔬菜；3. 适量运动，增强体质。",
          author: "中医专家",
          imageUrl: "/images/养生文章/春季养生.jpg",
          viewCount: 1024,
          createTime: new Date(),
          updateTime: new Date()
        },
        {
          title: "中药泡脚的好处",
          category: "中医保健", 
          content: "中药泡脚是一种简单有效的养生方法。通过热水和中药的作用，可以促进血液循环，缓解疲劳，改善睡眠质量。",
          author: "养生专家",
          imageUrl: "/images/养生文章/中药泡脚.jpg",
          viewCount: 856,
          createTime: new Date(),
          updateTime: new Date()
        }
      ];

      for (const article of articlesData) {
        await db.collection('articles').add({ data: article });
      }
      results.articles = true;
      console.log('养生文章数据创建成功');
    } catch (articlesError) {
      console.log('养生文章数据可能已存在:', articlesError.message);
    }

    return {
      success: true,
      message: '数据库初始化完成',
      results: results
    };

  } catch (error) {
    console.error('数据库初始化失败:', error);
    return {
      success: false,
      message: '初始化失败: ' + error.message,
      error: error
    };
  }
};

module.exports = {
  initDatabaseDirect
};

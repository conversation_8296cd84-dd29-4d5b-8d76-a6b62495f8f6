// 图片占位符工具

/**
 * 生成 SVG 占位符图片的 Base64 编码
 * @param {string} text 显示的文字
 * @param {number} width 宽度
 * @param {number} height 高度
 * @param {string} bgColor 背景色
 * @param {string} textColor 文字颜色
 * @returns {string} Base64 编码的 SVG 图片
 */
const generatePlaceholder = (text, width = 100, height = 100, bgColor = '#f5f5f5', textColor = '#999') => {
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="${width}" height="${height}" fill="${bgColor}"/>
      <text x="${width/2}" y="${height/2 + 5}" font-family="Arial, sans-serif" font-size="14" fill="${textColor}" text-anchor="middle">${text}</text>
    </svg>
  `;
  
  return 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svg)));
};

/**
 * 预定义的占位符图片
 */
const placeholders = {
  // 中药材占位符
  medicine: generatePlaceholder('中药材', 100, 100),
  
  // 文创产品占位符
  product: generatePlaceholder('文创产品', 100, 100),
  
  // 养生文章占位符
  article: generatePlaceholder('养生文章', 100, 100),
  
  // 用户头像占位符
  avatar: generatePlaceholder('头像', 80, 80),
  
  // 轮播图占位符
  banner: generatePlaceholder('轮播图', 300, 150),
  
  // 空状态占位符
  empty: generatePlaceholder('暂无图片', 120, 120, '#f8f8f8', '#ccc'),
  
  // 加载中占位符
  loading: generatePlaceholder('加载中...', 100, 100, '#f0f0f0', '#aaa')
};

/**
 * 根据类型获取占位符图片
 * @param {string} type 类型 (medicine, product, article, avatar, banner, empty, loading)
 * @param {string} customText 自定义文字
 * @returns {string} Base64 编码的占位符图片
 */
const getPlaceholder = (type, customText = null) => {
  if (customText) {
    return generatePlaceholder(customText);
  }
  
  return placeholders[type] || placeholders.empty;
};

/**
 * 检查图片URL是否为本地路径
 * @param {string} url 图片URL
 * @returns {boolean} 是否为本地路径
 */
const isLocalImage = (url) => {
  return url && (
    url.startsWith('/images/') ||
    url.startsWith('./images/') ||
    url.startsWith('../images/') ||
    url.includes('/images/中药材/') ||
    url.includes('/images/文创产品/') ||
    url.includes('/images/养生文章/') ||
    url.includes('/images/界面图标/') ||
    url.includes('/images/轮播图/')
  );
};

/**
 * 处理图片URL，将本地路径替换为占位符
 * @param {string} url 原始图片URL
 * @param {string} type 图片类型
 * @param {string} fallbackText 备用文字
 * @returns {string} 处理后的图片URL
 */
const processImageUrl = (url, type = 'empty', fallbackText = null) => {
  // 如果是本地图片路径，返回占位符
  if (isLocalImage(url)) {
    return getPlaceholder(type, fallbackText);
  }
  
  // 如果是网络图片或 Base64，直接返回
  return url || getPlaceholder(type, fallbackText);
};

/**
 * 为数据对象处理图片URL
 * @param {Object} data 数据对象
 * @param {string} imageField 图片字段名
 * @param {string} type 图片类型
 * @param {string} nameField 名称字段（用作备用文字）
 * @returns {Object} 处理后的数据对象
 */
const processDataImages = (data, imageField = 'imageUrl', type = 'empty', nameField = 'name') => {
  if (!data) return data;
  
  // 处理数组
  if (Array.isArray(data)) {
    return data.map(item => processDataImages(item, imageField, type, nameField));
  }
  
  // 处理单个对象
  if (typeof data === 'object' && data[imageField]) {
    const fallbackText = data[nameField] || null;
    return {
      ...data,
      [imageField]: processImageUrl(data[imageField], type, fallbackText)
    };
  }
  
  return data;
};

/**
 * 图片加载错误处理
 * @param {Event} event 错误事件
 * @param {string} type 图片类型
 * @param {string} fallbackText 备用文字
 */
const handleImageError = (event, type = 'empty', fallbackText = '图片加载失败') => {
  if (event && event.target) {
    event.target.src = getPlaceholder(type, fallbackText);
  }
};

module.exports = {
  generatePlaceholder,
  getPlaceholder,
  isLocalImage,
  processImageUrl,
  processDataImages,
  handleImageError,
  placeholders
};

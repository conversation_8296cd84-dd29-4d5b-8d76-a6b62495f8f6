// app.js
App({
  onLaunch: function () {
    this.globalData = {
      // env 参数说明：
      //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
      //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
      //   如不填则使用默认环境（第一个创建的环境）
      env: "cloud1-8gj5z3ju8fc199f3",
      userInfo: null,
      isAdmin: false
    };

    if (!wx.cloud) {
      console.error("请使用 2.2.3 或以上的基础库以使用云能力");
    } else {
      wx.cloud.init({
        env: this.globalData.env,
        traceUser: true,
      });
    }

    // 检查用户登录状态
    this.checkUserLogin();
  },

  // 检查用户登录状态
  checkUserLogin: function() {
    var userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.globalData.userInfo = userInfo;
      this.globalData.isAdmin = userInfo.role === 'admin';
      console.log('检查到已登录用户:', userInfo);
    }
  },

  // 设置用户信息
  setUserInfo: function(userInfo) {
    this.globalData.userInfo = userInfo;
    this.globalData.isAdmin = userInfo.role === 'admin';
    wx.setStorageSync('userInfo', userInfo);
    console.log('设置用户信息:', userInfo);
  },

  // 清除用户信息
  clearUserInfo: function() {
    this.globalData.userInfo = null;
    this.globalData.isAdmin = false;
    wx.removeStorageSync('userInfo');
    console.log('清除用户信息');
  },

  // 显示加载提示
  showLoading: function(title) {
    wx.showLoading({
      title: title || '加载中...',
      mask: true
    });
  },

  // 隐藏加载提示
  hideLoading: function() {
    wx.hideLoading();
  },

  // 显示成功提示
  showSuccess: function(title) {
    wx.showToast({
      title: title || '操作成功',
      icon: 'success',
      duration: 2000
    });
  },

  // 显示错误提示
  showError: function(title) {
    wx.showToast({
      title: title || '操作失败',
      icon: 'none',
      duration: 2000
    });
  },

  // 更新购物车徽章
  updateCartBadge: function() {
    if (this.globalData.userInfo) {
      var cartKey = 'cart_' + this.globalData.userInfo.id;
      var cartData = wx.getStorageSync(cartKey) || [];
      var totalCount = 0;

      for (var i = 0; i < cartData.length; i++) {
        totalCount += cartData[i].quantity;
      }

      if (totalCount > 0) {
        wx.setTabBarBadge({
          index: 3, // 购物车是第4个tab，索引为3
          text: totalCount > 99 ? '99+' : totalCount.toString()
        });
        console.log('设置购物车徽章:', totalCount);
      } else {
        wx.removeTabBarBadge({
          index: 3
        });
        console.log('移除购物车徽章');
      }
    }
  }
});

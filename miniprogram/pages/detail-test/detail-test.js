// 详情页面跳转测试
Page({
  data: {
    testItems: [
      {
        type: 'medicine',
        id: '1',
        name: '人参',
        category: '补气药',
        imageUrl: '🌿',
        description: '测试中药材详情页面跳转'
      },
      {
        type: 'medicine',
        id: '2',
        name: '黄芪',
        category: '补气药',
        imageUrl: '🌾',
        description: '测试中药材详情页面跳转'
      },
      {
        type: 'medicine',
        id: '7',
        name: '枸杞子',
        category: '补血药',
        imageUrl: '🍇',
        description: '测试中药材详情页面跳转'
      },
      {
        type: 'product',
        id: '1',
        name: '中医养生茶具套装',
        category: '茶具',
        imageUrl: '🍵',
        description: '测试文创产品详情页面跳转'
      },
      {
        type: 'product',
        id: '2',
        name: '紫砂功夫茶具',
        category: '茶具',
        imageUrl: '🫖',
        description: '测试文创产品详情页面跳转'
      },
      {
        type: 'product',
        id: '5',
        name: '本草纲目典藏版',
        category: '书籍',
        imageUrl: '📚',
        description: '测试文创产品详情页面跳转'
      }
    ]
  },

  onLoad: function() {
    console.log('详情页面跳转测试页面加载');
  },

  // 跳转到详情页面
  goToDetail: function(e) {
    var item = e.currentTarget.dataset.item;
    console.log('跳转到详情页面:', item);
    
    if (item.type === 'medicine') {
      wx.navigateTo({
        url: '/pages/medicine-detail/medicine-detail?id=' + item.id
      });
    } else if (item.type === 'product') {
      wx.navigateTo({
        url: '/pages/product-detail/product-detail?id=' + item.id
      });
    }
  },

  // 跳转到中药材列表
  goToMedicines: function() {
    wx.navigateTo({
      url: '/pages/medicines/medicines'
    });
  },

  // 跳转到文创产品列表
  goToProducts: function() {
    wx.navigateTo({
      url: '/pages/products/products'
    });
  },

  // 返回首页
  goHome: function() {
    wx.navigateBack();
  }
});

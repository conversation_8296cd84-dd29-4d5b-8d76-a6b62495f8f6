<!--详情页面跳转测试-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">🔗 详情页面跳转测试</text>
    <text class="subtitle">点击商品测试详情页面跳转功能</text>
  </view>

  <!-- 测试商品列表 -->
  <view class="test-section">
    <view class="section-title">
      <text class="title-icon">🧪</text>
      <text class="title-text">测试商品列表</text>
    </view>
    
    <view class="test-list">
      <view class="test-item" wx:for="{{testItems}}" wx:key="id"
            bindtap="goToDetail" data-item="{{item}}">
        <view class="item-image">
          <text class="image-placeholder">{{item.imageUrl}}</text>
        </view>
        <view class="item-info">
          <text class="item-name">{{item.name}}</text>
          <text class="item-category">{{item.category}}</text>
          <text class="item-type">{{item.type === 'medicine' ? '中药材' : '文创产品'}}</text>
          <text class="item-description">{{item.description}}</text>
        </view>
        <view class="item-arrow">
          <text class="arrow-icon">→</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速跳转 -->
  <view class="quick-nav">
    <view class="section-title">
      <text class="title-icon">🚀</text>
      <text class="title-text">快速跳转</text>
    </view>
    
    <view class="nav-buttons">
      <button class="nav-btn medicine-btn" bindtap="goToMedicines">
        <text class="btn-icon">🌿</text>
        <text class="btn-text">中药材列表</text>
      </button>
      <button class="nav-btn product-btn" bindtap="goToProducts">
        <text class="btn-icon">🎋</text>
        <text class="btn-text">文创产品列表</text>
      </button>
    </view>
  </view>

  <!-- 功能说明 -->
  <view class="info-section">
    <view class="section-title">
      <text class="title-icon">ℹ️</text>
      <text class="title-text">功能说明</text>
    </view>
    
    <view class="info-content">
      <view class="info-item">
        <text class="info-icon">✅</text>
        <text class="info-text">点击商品卡片可跳转到对应的详情页面</text>
      </view>
      <view class="info-item">
        <text class="info-icon">✅</text>
        <text class="info-text">详情页面包含完整的商品信息和购买功能</text>
      </view>
      <view class="info-item">
        <text class="info-icon">✅</text>
        <text class="info-text">支持添加到购物车和立即购买</text>
      </view>
      <view class="info-item">
        <text class="info-icon">✅</text>
        <text class="info-text">包含用户评价和相关推荐</text>
      </view>
    </view>
  </view>

  <!-- 返回按钮 -->
  <view class="back-section">
    <button class="back-btn" bindtap="goHome">
      <text class="btn-icon">🏠</text>
      <text class="btn-text">返回首页</text>
    </button>
  </view>
</view>

/* 详情页面跳转测试样式 */
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 8rpx;
}

.subtitle {
  font-size: 20rpx;
  color: #666;
}

/* 通用区块样式 */
.test-section,
.quick-nav,
.info-section,
.back-section {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-icon {
  font-size: 24rpx;
}

.title-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
}

/* 测试商品列表 */
.test-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.test-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 15rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.test-item:active {
  background: #f0f0f0;
  border-color: #8B4513;
  transform: translateY(-2rpx);
}

.item-image {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.image-placeholder {
  font-size: 40rpx;
  color: white;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.item-name {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.item-category {
  font-size: 18rpx;
  color: #228B22;
  background: rgba(34, 139, 34, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  align-self: flex-start;
}

.item-type {
  font-size: 18rpx;
  color: #8B4513;
  font-weight: bold;
}

.item-description {
  font-size: 18rpx;
  color: #666;
  margin-top: 5rpx;
}

.item-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(139, 69, 19, 0.1);
  border-radius: 50%;
}

.arrow-icon {
  font-size: 20rpx;
  color: #8B4513;
  font-weight: bold;
}

/* 快速跳转 */
.nav-buttons {
  display: flex;
  gap: 15rpx;
}

.nav-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 20rpx;
  border-radius: 15rpx;
  font-size: 20rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.medicine-btn {
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.3);
}

.product-btn {
  background: linear-gradient(135deg, #228B22, #32CD32);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(34, 139, 34, 0.3);
}

.nav-btn:active {
  transform: translateY(-2rpx);
  opacity: 0.9;
}

.btn-icon {
  font-size: 18rpx;
}

.btn-text {
  font-size: 20rpx;
}

/* 功能说明 */
.info-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.info-icon {
  font-size: 18rpx;
  color: #228B22;
  width: 25rpx;
  text-align: center;
}

.info-text {
  font-size: 20rpx;
  color: #666;
  flex: 1;
  line-height: 1.4;
}

/* 返回按钮 */
.back-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 18rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  border: none;
  background: linear-gradient(135deg, #4169E1, #6495ED);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(65, 105, 225, 0.3);
  transition: all 0.3s ease;
}

.back-btn:active {
  transform: translateY(-2rpx);
  opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .nav-buttons {
    flex-direction: column;
  }
  
  .test-item {
    flex-direction: column;
    text-align: center;
  }
  
  .item-info {
    align-items: center;
  }
  
  .item-category {
    align-self: center;
  }
}

/* 动画效果 */
.test-item {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

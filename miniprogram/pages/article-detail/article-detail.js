// 养生文章详情页面
var app = getApp();

Page({
  data: {
    articleId: '',
    article: null,
    loading: true,
    isLiked: false,
    isCollected: false,
    
    // 相关推荐
    relatedArticles: [],
    
    // 评论相关
    comments: [],
    showCommentInput: false,
    commentText: '',
    
    // 分享相关
    shareCount: 0
  },

  onLoad: function(options) {
    console.log('养生文章详情页面加载，参数:', options);
    if (options.id) {
      this.setData({
        articleId: options.id
      });
      this.loadArticleDetail(options.id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(function() {
        wx.navigateBack();
      }, 1500);
    }
  },

  onShow: function() {
    this.refreshUserActions();
  },

  // 加载文章详情
  loadArticleDetail: function(id) {
    var that = this;
    that.setData({ loading: true });

    // 模拟从数据库获取详细数据
    var articleData = that.getArticleData(id);
    
    if (articleData) {
      that.setData({
        article: articleData,
        loading: false
      });
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: articleData.title
      });
      
      // 加载相关推荐
      that.loadRelatedArticles(articleData.category);
      
      // 加载评论
      that.loadComments(id);
      
      // 刷新用户操作状态
      that.refreshUserActions();
      
      // 增加阅读量
      that.increaseReadCount(id);
    } else {
      that.setData({ loading: false });
      wx.showModal({
        title: '提示',
        content: '未找到该文章',
        showCancel: false,
        success: function() {
          wx.navigateBack();
        }
      });
    }
  },

  // 获取文章数据
  getArticleData: function(id) {
    var articles = {
      'article_001': {
        id: 'article_001',
        title: '春季养生：顺应自然，调养身心',
        category: '四季养生',
        author: '中医养生专家',
        publishTime: '2024-03-15',
        readCount: 1256,
        likeCount: 89,
        collectCount: 67,
        shareCount: 34,
        imageUrl: '/images/养生文章/文章-春季养生.jpg',
        summary: '春季是万物复苏的季节，人体阳气开始升发，此时养生应顺应自然规律，注重调养肝脏，保持心情舒畅。',
        content: `
春季养生的核心理念是"顺应自然"。《黄帝内经》中提到："春三月，此谓发陈，天地俱生，万物以荣。"春季是阳气升发、万物生长的季节，人体的生理活动也应该顺应这一自然规律。

## 一、春季养生的基本原则

### 1. 养肝为主
春季对应五脏中的肝脏，肝主疏泄，喜条达而恶抑郁。春季养生应以养肝为主，保持肝气的疏泄功能正常。

### 2. 调节情志
春季容易出现情绪波动，应保持心情舒畅，避免愤怒、忧郁等负面情绪。可以通过户外活动、音乐、书画等方式调节情志。

### 3. 适度运动
春季阳气升发，适合进行户外运动，如散步、慢跑、太极拳等。运动强度应适中，以微微出汗为宜。

## 二、春季饮食调养

### 1. 饮食原则
- 少酸多甘：减少酸性食物，增加甘味食物
- 清淡为主：避免过于油腻、辛辣的食物
- 时令蔬菜：多食用春季时令蔬菜，如韭菜、菠菜、春笋等

### 2. 推荐食材
- **韭菜**：温补肝肾，助阳固精
- **菠菜**：养血润燥，清热除烦
- **春笋**：清热化痰，利水消肿
- **枸杞**：滋补肝肾，明目润肺

## 三、起居调养

### 1. 作息规律
春季应早睡早起，顺应自然界阳气升发的规律。建议晚上10-11点入睡，早上6-7点起床。

### 2. 衣着调节
春季气温变化较大，应注意"春捂"，不要过早脱去冬衣，特别要注意保护颈部、腰部等关键部位。

### 3. 居住环境
保持室内空气流通，适当开窗通风，让新鲜空气进入室内。

## 四、中医调理方法

### 1. 穴位按摩
- **太冲穴**：位于足背，第一、二跖骨结合部之前凹陷处，有疏肝理气的作用
- **期门穴**：位于胸部，第六肋间隙，有疏肝理气、调和气血的功效

### 2. 中药调理
根据个人体质，可选用以下中药：
- **逍遥散**：疏肝解郁，健脾和胃
- **甘麦大枣汤**：养心安神，和中缓急

## 五、注意事项

1. 避免过度劳累，保证充足的休息
2. 预防春季常见疾病，如感冒、过敏等
3. 定期体检，及时发现和处理健康问题
4. 根据个人体质调整养生方案

春季养生是一个系统工程，需要从饮食、起居、运动、情志等多个方面综合调理。只有顺应自然规律，才能达到最佳的养生效果。
        `,
        tags: ['春季养生', '中医调理', '饮食养生', '情志调养']
      },
      'article_002': {
        id: 'article_002',
        title: '中医药膳：食疗养生的智慧',
        category: '药膳食疗',
        author: '药膳专家',
        publishTime: '2024-03-10',
        readCount: 892,
        likeCount: 76,
        collectCount: 54,
        shareCount: 28,
        imageUrl: '/images/养生文章/文章-食疗智慧.jpg',
        summary: '药膳是中医学的重要组成部分，通过食物与药物的合理搭配，达到防病治病、强身健体的目的。',
        content: `
药膳，又称食疗，是中医学的重要组成部分。它以中医理论为指导，将具有药用价值的食物与中药材合理搭配，制成既有营养价值又有治疗作用的膳食。

## 一、药膳的基本理论

### 1. 药食同源
中医认为"药食同源"，许多食物本身就具有药用价值。如生姜能温中散寒，大枣能补中益气，枸杞能滋补肝肾等。

### 2. 四性五味
食物和药物一样，具有寒、热、温、凉四性和酸、苦、甘、辛、咸五味，不同性味的食物对人体有不同的作用。

### 3. 辨证施膳
根据个人的体质、病情、季节等因素，选择合适的药膳配方，做到因人、因时、因地制宜。

## 二、常用药膳食材

### 1. 补气类
- **人参**：大补元气，适用于气虚体弱
- **黄芪**：补气升阳，适用于气虚下陷
- **党参**：补中益气，适用于脾胃虚弱

### 2. 补血类
- **当归**：补血活血，适用于血虚血瘀
- **阿胶**：滋阴补血，适用于阴血不足
- **红枣**：补中益气，养血安神

### 3. 滋阴类
- **枸杞子**：滋补肝肾，明目润肺
- **百合**：润肺止咳，清心安神
- **银耳**：滋阴润燥，美容养颜

## 三、经典药膳方

### 1. 四物汤
**组成**：当归、川芎、白芍、熟地黄
**功效**：补血调经，适用于血虚证
**制法**：将四味药材煎煮后，可加入鸡肉、排骨等炖制

### 2. 八珍汤
**组成**：人参、白术、茯苓、甘草、当归、川芎、白芍、熟地黄
**功效**：气血双补，适用于气血两虚证
**制法**：可制成八珍鸡汤、八珍排骨汤等

### 3. 六味地黄丸汤
**组成**：熟地黄、山茱萸、山药、泽泻、茯苓、牡丹皮
**功效**：滋阴补肾，适用于肾阴虚证
**制法**：可制成滋补汤品或粥品

## 四、季节性药膳

### 1. 春季药膳
- **韭菜炒蛋**：温补肝肾，助阳固精
- **菠菜猪肝汤**：养血润燥，明目护肝
- **春笋排骨汤**：清热化痰，健脾开胃

### 2. 夏季药膳
- **绿豆莲子汤**：清热解毒，消暑除烦
- **冬瓜薏米汤**：利水消肿，清热祛湿
- **银耳莲子羹**：滋阴润燥，清心安神

### 3. 秋季药膳
- **百合银耳汤**：润肺止咳，滋阴润燥
- **梨子川贝汤**：清热润肺，止咳化痰
- **山药排骨汤**：健脾益肺，滋阴润燥

### 4. 冬季药膳
- **当归生姜羊肉汤**：温中散寒，补血调经
- **黄芪炖鸡汤**：补气升阳，增强免疫
- **枸杞炖牛肉**：滋补肝肾，强筋健骨

## 五、药膳制作要点

### 1. 选材要求
- 药材要道地，食材要新鲜
- 根据体质选择合适的配方
- 注意药物的配伍禁忌

### 2. 制作方法
- 药材需要预处理，如清洗、浸泡等
- 掌握好火候和时间
- 调味要适中，不要掩盖药材的功效

### 3. 服用注意
- 根据病情和体质确定用量
- 注意观察身体反应
- 有不适症状应及时停用

药膳养生是一门深奥的学问，需要在专业指导下进行。通过合理的药膳调理，可以达到防病治病、延年益寿的目的。
        `,
        tags: ['药膳食疗', '中医养生', '食疗方', '季节养生']
      },
      '1': {
        id: '1',
        title: '春季养生：顺应自然，调养身心',
        category: '四季养生',
        author: '中医养生专家',
        publishTime: '2024-03-15',
        readCount: 1256,
        likeCount: 89,
        collectCount: 67,
        shareCount: 34,
        imageUrl: '/images/养生文章/文章-春季养生.jpg',
        summary: '春季是万物复苏的季节，人体阳气开始升发，此时养生应顺应自然规律，注重调养肝脏，保持心情舒畅。',
        content: '春季养生的核心理念是"顺应自然"。《黄帝内经》中提到："春三月，此谓发陈，天地俱生，万物以荣。"春季是阳气升发、万物生长的季节，人体的生理活动也应该顺应这一自然规律...',
        tags: ['春季养生', '中医调理', '饮食养生', '情志调养']
      }
    };
    
    return articles[id] || null;
  },

  // 加载相关推荐
  loadRelatedArticles: function(category) {
    var related = [
      {
        id: 'article_002',
        title: '中医药膳：食疗养生的智慧',
        category: '药膳食疗',
        imageUrl: '/images/养生文章/文章-食疗智慧.jpg',
        readCount: 892
      },
      {
        id: 'article_003',
        title: '夏季清热解毒的中药茶饮',
        category: '四季养生',
        imageUrl: '/images/养生文章/文章-茶饮推荐.jpg',
        readCount: 654
      },
      {
        id: 'article_004',
        title: '中医穴位按摩养生法',
        category: '中医保健',
        imageUrl: '/images/养生文章/文章-穴位按摩.jpg',
        readCount: 789
      }
    ];
    
    this.setData({
      relatedArticles: related
    });
  },

  // 加载评论
  loadComments: function(articleId) {
    var comments = [
      {
        id: 'comment_001',
        userName: '养生爱好者',
        avatar: '👨‍⚕️',
        content: '文章写得很好，很实用的养生知识！',
        time: '2024-03-16 10:30',
        likeCount: 12
      },
      {
        id: 'comment_002',
        userName: '中医学习者',
        avatar: '👩‍🎓',
        content: '学到了很多，感谢分享这么详细的养生方法。',
        time: '2024-03-16 14:20',
        likeCount: 8
      }
    ];
    
    this.setData({
      comments: comments
    });
  },

  // 增加阅读量
  increaseReadCount: function(id) {
    // 模拟增加阅读量
    var article = this.data.article;
    if (article) {
      article.readCount += 1;
      this.setData({
        article: article
      });
    }
  },

  // 点赞文章
  toggleLike: function() {
    var that = this;
    var isLiked = !that.data.isLiked;
    var article = that.data.article;
    
    that.setData({
      isLiked: isLiked
    });
    
    // 更新点赞数
    if (isLiked) {
      article.likeCount += 1;
    } else {
      article.likeCount -= 1;
    }
    
    that.setData({
      article: article
    });
    
    // 保存点赞状态
    that.saveLikeStatus(article.id, isLiked);
    
    wx.showToast({
      title: isLiked ? '已点赞' : '已取消点赞',
      icon: 'success'
    });
  },

  // 收藏文章
  toggleCollect: function() {
    var that = this;
    var isCollected = !that.data.isCollected;
    var article = that.data.article;
    
    that.setData({
      isCollected: isCollected
    });
    
    // 更新收藏数
    if (isCollected) {
      article.collectCount += 1;
    } else {
      article.collectCount -= 1;
    }
    
    that.setData({
      article: article
    });
    
    // 保存收藏状态
    that.saveCollectStatus(article.id, isCollected);
    
    wx.showToast({
      title: isCollected ? '已收藏' : '已取消收藏',
      icon: 'success'
    });
  },

  // 查看相关文章
  viewRelated: function(e) {
    var id = e.currentTarget.dataset.id;
    wx.redirectTo({
      url: '/pages/article-detail/article-detail?id=' + id
    });
  },

  // 显示评论输入框
  showCommentInput: function() {
    this.setData({
      showCommentInput: true
    });
  },

  // 隐藏评论输入框
  hideCommentInput: function() {
    this.setData({
      showCommentInput: false,
      commentText: ''
    });
  },

  // 评论输入
  onCommentInput: function(e) {
    this.setData({
      commentText: e.detail.value
    });
  },

  // 发布评论
  submitComment: function() {
    var commentText = this.data.commentText.trim();
    if (commentText === '') {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }
    
    // 模拟发布评论
    var newComment = {
      id: 'comment_' + Date.now(),
      userName: '我',
      avatar: '👤',
      content: commentText,
      time: new Date().toLocaleString(),
      likeCount: 0
    };
    
    var comments = [newComment].concat(this.data.comments);
    this.setData({
      comments: comments,
      showCommentInput: false,
      commentText: ''
    });
    
    wx.showToast({
      title: '评论发布成功',
      icon: 'success'
    });
  },

  // 保存点赞状态
  saveLikeStatus: function(id, isLiked) {
    var userInfo = app.globalData.userInfo;
    if (!userInfo) {
      userInfo = {
        id: 'guest_' + Date.now(),
        name: '游客用户',
        avatar: '👤',
        role: 'user',
        isGuest: true
      };
      app.setUserInfo(userInfo);
    }
    
    var key = 'article_likes_' + userInfo.id;
    var likes = wx.getStorageSync(key) || [];
    
    if (isLiked) {
      if (likes.indexOf(id) === -1) {
        likes.push(id);
      }
    } else {
      likes = likes.filter(function(likeId) {
        return likeId !== id;
      });
    }
    
    wx.setStorageSync(key, likes);
  },

  // 保存收藏状态
  saveCollectStatus: function(id, isCollected) {
    var userInfo = app.globalData.userInfo;
    if (!userInfo) {
      userInfo = {
        id: 'guest_' + Date.now(),
        name: '游客用户',
        avatar: '👤',
        role: 'user',
        isGuest: true
      };
      app.setUserInfo(userInfo);
    }
    
    var key = 'article_collections_' + userInfo.id;
    var collections = wx.getStorageSync(key) || [];
    
    if (isCollected) {
      if (collections.indexOf(id) === -1) {
        collections.push(id);
      }
    } else {
      collections = collections.filter(function(collectId) {
        return collectId !== id;
      });
    }
    
    wx.setStorageSync(key, collections);
  },

  // 刷新用户操作状态
  refreshUserActions: function() {
    var userInfo = app.globalData.userInfo;
    if (!userInfo || !this.data.article) return;
    
    var articleId = this.data.article.id;
    
    // 检查点赞状态
    var likesKey = 'article_likes_' + userInfo.id;
    var likes = wx.getStorageSync(likesKey) || [];
    
    // 检查收藏状态
    var collectionsKey = 'article_collections_' + userInfo.id;
    var collections = wx.getStorageSync(collectionsKey) || [];
    
    this.setData({
      isLiked: likes.indexOf(articleId) !== -1,
      isCollected: collections.indexOf(articleId) !== -1
    });
  },

  // 分享功能
  onShareAppMessage: function() {
    var article = this.data.article;
    
    // 增加分享数
    if (article) {
      article.shareCount += 1;
      this.setData({
        article: article
      });
    }
    
    return {
      title: article ? article.title : '慧心制药 - 养生文章',
      path: '/pages/article-detail/article-detail?id=' + this.data.articleId,
      imageUrl: article ? article.imageUrl : ''
    };
  }
});

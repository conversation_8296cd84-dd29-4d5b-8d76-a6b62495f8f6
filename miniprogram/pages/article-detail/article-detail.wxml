<!--养生文章详情页面-->
<view class="container" wx:if="{{!loading}}">
  <!-- 文章头部 -->
  <view class="article-header">
    <view class="article-image">
      <text class="image-placeholder">{{article.imageUrl}}</text>
    </view>
    
    <view class="article-meta">
      <text class="article-title">{{article.title}}</text>
      <text class="article-category">{{article.category}}</text>
      <view class="article-info">
        <text class="article-author">👨‍⚕️ {{article.author}}</text>
        <text class="article-date">📅 {{article.publishTime}}</text>
      </view>
      <view class="article-stats">
        <text class="stat-item">👁️ {{article.readCount}}</text>
        <text class="stat-item">👍 {{article.likeCount}}</text>
        <text class="stat-item">⭐ {{article.collectCount}}</text>
        <text class="stat-item">📤 {{article.shareCount}}</text>
      </view>
    </view>
  </view>

  <!-- 文章摘要 -->
  <view class="article-summary">
    <text class="summary-text">{{article.summary}}</text>
  </view>

  <!-- 文章标签 -->
  <view class="article-tags">
    <text class="tag-item" wx:for="{{article.tags}}" wx:key="*this">
      #{{item}}
    </text>
  </view>

  <!-- 文章内容 -->
  <view class="article-content">
    <rich-text nodes="{{article.content}}" class="content-text"></rich-text>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="action-btn like-btn {{isLiked ? 'liked' : ''}}" bindtap="toggleLike">
      <text class="btn-icon">{{isLiked ? '❤️' : '🤍'}}</text>
      <text class="btn-text">{{isLiked ? '已点赞' : '点赞'}}</text>
      <text class="btn-count">{{article.likeCount}}</text>
    </button>
    
    <button class="action-btn collect-btn {{isCollected ? 'collected' : ''}}" bindtap="toggleCollect">
      <text class="btn-icon">{{isCollected ? '⭐' : '☆'}}</text>
      <text class="btn-text">{{isCollected ? '已收藏' : '收藏'}}</text>
      <text class="btn-count">{{article.collectCount}}</text>
    </button>
    
    <button class="action-btn comment-btn" bindtap="showCommentInput">
      <text class="btn-icon">💬</text>
      <text class="btn-text">评论</text>
      <text class="btn-count">{{comments.length}}</text>
    </button>
  </view>

  <!-- 评论区域 -->
  <view class="comment-section" wx:if="{{comments.length > 0}}">
    <view class="section-title">
      <text class="title-icon">💬</text>
      <text class="title-text">精彩评论</text>
    </view>
    
    <view class="comment-list">
      <view class="comment-item" wx:for="{{comments}}" wx:key="id">
        <view class="comment-header">
          <text class="commenter-avatar">{{item.avatar}}</text>
          <view class="commenter-info">
            <text class="commenter-name">{{item.userName}}</text>
            <text class="comment-time">{{item.time}}</text>
          </view>
          <view class="comment-like">
            <text class="like-icon">👍</text>
            <text class="like-count">{{item.likeCount}}</text>
          </view>
        </view>
        <text class="comment-content">{{item.content}}</text>
      </view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section" wx:if="{{relatedArticles.length > 0}}">
    <view class="section-title">
      <text class="title-icon">📚</text>
      <text class="title-text">相关推荐</text>
    </view>
    
    <view class="related-list">
      <view class="related-item" wx:for="{{relatedArticles}}" wx:key="id"
            bindtap="viewRelated" data-id="{{item.id}}">
        <view class="related-image">
          <text class="image-placeholder">{{item.imageUrl}}</text>
        </view>
        <view class="related-info">
          <text class="related-title">{{item.title}}</text>
          <text class="related-category">{{item.category}}</text>
          <text class="related-read">👁️ {{item.readCount}}阅读</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="bottom-btn like-bottom-btn {{isLiked ? 'liked' : ''}}" bindtap="toggleLike">
      <text class="btn-icon">{{isLiked ? '❤️' : '🤍'}}</text>
    </button>
    <button class="bottom-btn collect-bottom-btn {{isCollected ? 'collected' : ''}}" bindtap="toggleCollect">
      <text class="btn-icon">{{isCollected ? '⭐' : '☆'}}</text>
    </button>
    <button class="bottom-btn comment-bottom-btn" bindtap="showCommentInput">
      <text class="btn-icon">💬</text>
    </button>
    <button class="bottom-btn share-btn" open-type="share">
      <text class="btn-icon">📤</text>
    </button>
  </view>
</view>

<!-- 评论输入弹窗 -->
<view class="modal-overlay" wx:if="{{showCommentInput}}" bindtap="hideCommentInput">
  <view class="comment-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">发表评论</text>
      <button class="modal-close" bindtap="hideCommentInput">×</button>
    </view>
    
    <view class="modal-body">
      <textarea class="comment-textarea" 
                placeholder="分享你的看法..." 
                value="{{commentText}}" 
                bindinput="onCommentInput"
                maxlength="200"
                auto-height></textarea>
      <view class="char-count">{{commentText.length}}/200</view>
    </view>
    
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="hideCommentInput">取消</button>
      <button class="submit-btn" bindtap="submitComment" disabled="{{commentText.trim() === ''}}">
        发布
      </button>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-content">
    <text class="loading-icon">⏳</text>
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 占位，防止底部按钮遮挡内容 -->
<view class="bottom-placeholder"></view>

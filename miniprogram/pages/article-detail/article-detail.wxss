/* 养生文章详情页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 文章头部 */
.article-header {
  background: white;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.article-image {
  width: 100%;
  height: 300rpx;
  background: linear-gradient(135deg, #228B22, #32CD32);
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-placeholder {
  font-size: 100rpx;
  color: white;
}

.article-meta {
  padding: 25rpx 30rpx;
}

.article-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  display: block;
  margin-bottom: 15rpx;
}

.article-category {
  font-size: 20rpx;
  color: #228B22;
  background: rgba(34, 139, 34, 0.1);
  padding: 6rpx 15rpx;
  border-radius: 15rpx;
  display: inline-block;
  margin-bottom: 15rpx;
}

.article-info {
  display: flex;
  gap: 20rpx;
  margin-bottom: 15rpx;
}

.article-author,
.article-date {
  font-size: 20rpx;
  color: #666;
}

.article-stats {
  display: flex;
  gap: 25rpx;
}

.stat-item {
  font-size: 18rpx;
  color: #999;
}

/* 文章摘要 */
.article-summary {
  background: white;
  padding: 25rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.08);
}

.summary-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  font-style: italic;
  border-left: 4rpx solid #228B22;
  padding-left: 15rpx;
}

/* 文章标签 */
.article-tags {
  background: white;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.08);
}

.tag-item {
  font-size: 18rpx;
  color: #228B22;
  background: rgba(34, 139, 34, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(34, 139, 34, 0.2);
}

/* 文章内容 */
.article-content {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.08);
}

.content-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
}

/* 操作按钮 */
.action-section {
  background: white;
  padding: 25rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.08);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background: transparent;
  border: none;
  padding: 15rpx;
  border-radius: 15rpx;
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.action-btn:active {
  background: #f0f0f0;
  transform: scale(0.95);
}

.like-btn.liked {
  background: rgba(255, 182, 193, 0.2);
}

.collect-btn.collected {
  background: rgba(255, 215, 0, 0.2);
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 18rpx;
  color: #666;
}

.btn-count {
  font-size: 16rpx;
  color: #999;
}

/* 通用区块样式 */
.comment-section,
.related-section {
  background: white;
  margin-bottom: 20rpx;
  padding: 25rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.08);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-icon {
  font-size: 24rpx;
}

.title-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
}

/* 评论列表 */
.comment-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.comment-item {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 15rpx;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 10rpx;
}

.commenter-avatar {
  font-size: 32rpx;
}

.commenter-info {
  flex: 1;
}

.commenter-name {
  font-size: 20rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.comment-time {
  font-size: 16rpx;
  color: #999;
}

.comment-like {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.like-icon {
  font-size: 16rpx;
  color: #999;
}

.like-count {
  font-size: 16rpx;
  color: #999;
}

.comment-content {
  font-size: 20rpx;
  color: #333;
  line-height: 1.5;
}

/* 相关推荐 */
.related-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.related-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.related-item:active {
  background: #f0f0f0;
  transform: translateY(-2rpx);
}

.related-image {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #228B22, #32CD32);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.related-image .image-placeholder {
  font-size: 40rpx;
  color: white;
}

.related-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.related-title {
  font-size: 20rpx;
  font-weight: bold;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-category {
  font-size: 16rpx;
  color: #228B22;
  background: rgba(34, 139, 34, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  align-self: flex-start;
}

.related-read {
  font-size: 16rpx;
  color: #999;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 15rpx 30rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-around;
  z-index: 1000;
}

.bottom-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: #f8f8f8;
}

.bottom-btn:active {
  transform: scale(0.9);
}

.like-bottom-btn.liked {
  background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
}

.collect-bottom-btn.collected {
  background: linear-gradient(135deg, #FFD700, #FFA500);
}

.comment-bottom-btn {
  background: linear-gradient(135deg, #87CEEB, #4682B4);
}

.share-btn {
  background: linear-gradient(135deg, #98FB98, #228B22);
}

.bottom-btn .btn-icon {
  font-size: 24rpx;
  color: #666;
}

.like-bottom-btn.liked .btn-icon,
.collect-bottom-btn.collected .btn-icon,
.comment-bottom-btn .btn-icon,
.share-btn .btn-icon {
  color: white;
}

.bottom-placeholder {
  height: 120rpx;
}

/* 评论输入弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 2000;
}

.comment-modal {
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: none;
  font-size: 30rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 25rpx 30rpx;
}

.comment-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 15rpx;
  font-size: 24rpx;
  line-height: 1.5;
  box-sizing: border-box;
  background: #f8f8f8;
}

.comment-textarea:focus {
  border-color: #228B22;
  background: white;
}

.char-count {
  text-align: right;
  font-size: 18rpx;
  color: #999;
  margin-top: 10rpx;
}

.modal-footer {
  display: flex;
  gap: 15rpx;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.cancel-btn,
.submit-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  font-size: 22rpx;
  font-weight: bold;
  border: none;
}

.cancel-btn {
  background: #f0f0f0;
  color: #666;
}

.submit-btn {
  background: linear-gradient(135deg, #228B22, #32CD32);
  color: white;
}

.submit-btn:disabled {
  background: #ccc;
  color: #999;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f5f5f5;
}

.loading-content {
  text-align: center;
}

.loading-icon {
  font-size: 60rpx;
  display: block;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .action-section {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .action-btn {
    flex-direction: row;
    justify-content: center;
  }
  
  .related-item {
    flex-direction: column;
    text-align: center;
  }
}

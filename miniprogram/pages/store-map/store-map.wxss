/* 门店地图页面样式 */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

/* 搜索栏 */
.search-section {
  background: white;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  background: #f8f8f8;
  border-radius: 35rpx;
  font-size: 24rpx;
  border: 2rpx solid transparent;
}

.search-input:focus {
  border-color: #8B4513;
  background: white;
}

.search-btn,
.filter-btn {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
  border: none;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-btn {
  background: linear-gradient(135deg, #228B22, #32CD32);
}

/* 地图容器 */
.map-container {
  position: relative;
  height: 400rpx;
  margin-bottom: 20rpx;
}

.map {
  width: 100%;
  height: 100%;
}

.map-controls {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  z-index: 10;
}

.control-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 门店列表 */
.store-list-section {
  flex: 1;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
}

.store-count {
  font-size: 20rpx;
  color: #666;
}

.store-list {
  height: 100%;
  padding: 0 20rpx;
}

.store-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 25rpx;
  margin-bottom: 15rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.store-item:active {
  background: #f0f0f0;
  transform: translateY(-2rpx);
}

.store-image {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.image-placeholder {
  font-size: 50rpx;
  color: white;
}

.store-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.store-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 5rpx;
}

.store-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.store-status {
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  font-size: 18rpx;
}

.store-status.open {
  background: rgba(34, 139, 34, 0.1);
  color: #228B22;
}

.store-status.closed {
  background: rgba(231, 76, 60, 0.1);
  color: #E74C3C;
}

.status-text {
  font-size: 18rpx;
  font-weight: 500;
}

.store-address,
.store-distance,
.store-hours {
  font-size: 20rpx;
  color: #666;
  line-height: 1.4;
}

.store-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-stars {
  font-size: 16rpx;
  color: #FFD700;
}

.rating-score {
  font-size: 18rpx;
  color: #333;
  font-weight: bold;
}

.store-services {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 5rpx;
}

.service-tag {
  padding: 3rpx 8rpx;
  background: rgba(139, 69, 19, 0.1);
  color: #8B4513;
  font-size: 16rpx;
  border-radius: 8rpx;
}

.store-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: none;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.call-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.nav-btn {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 门店详情弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.store-detail-modal {
  width: 90%;
  max-width: 700rpx;
  max-height: 80vh;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  background: #f8f8f8;
}

.modal-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
}

.modal-close {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: none;
  font-size: 30rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  max-height: 50vh;
  padding: 25rpx 30rpx;
}

.detail-image {
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 25rpx;
}

.detail-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-description {
  font-size: 22rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
  margin-bottom: 10rpx;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 15rpx;
}

.detail-label {
  font-size: 20rpx;
  color: #666;
  min-width: 100rpx;
}

.detail-value {
  font-size: 20rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

.detail-section {
  margin-top: 15rpx;
}

.service-list,
.feature-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-top: 10rpx;
}

.service-item,
.feature-item {
  font-size: 20rpx;
  color: #333;
  line-height: 1.4;
}

.modal-footer {
  display: flex;
  gap: 15rpx;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.footer-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 22rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.footer-btn.call-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.footer-btn.nav-btn {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

/* 筛选弹窗 */
.filter-modal {
  width: 90%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.filter-content {
  padding: 25rpx 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-group {
  margin-bottom: 30rpx;
}

.filter-label {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 15rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.filter-option {
  padding: 12rpx 20rpx;
  background: #f8f8f8;
  border: 2rpx solid transparent;
  border-radius: 25rpx;
  transition: all 0.3s ease;
}

.filter-option.selected {
  background: rgba(139, 69, 19, 0.1);
  border-color: #8B4513;
}

.option-text {
  font-size: 20rpx;
  color: #333;
}

.filter-option.selected .option-text {
  color: #8B4513;
  font-weight: bold;
}

.filter-footer {
  display: flex;
  gap: 15rpx;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.filter-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  font-size: 22rpx;
  font-weight: bold;
  border: none;
}

.reset-btn {
  background: #f0f0f0;
  color: #666;
}

.apply-btn {
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .store-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .store-actions {
    flex-direction: row;
    width: 100%;
    justify-content: flex-end;
  }
  
  .filter-options {
    justify-content: center;
  }
}

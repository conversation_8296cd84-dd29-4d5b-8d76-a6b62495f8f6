// 门店地图页面
Page({
  data: {
    // 地图相关
    latitude: 39.908823,
    longitude: 116.397470,
    scale: 14,
    markers: [],
    
    // 门店列表
    storeList: [
      {
        id: 'store_001',
        name: '慧心制药总店',
        address: '北京市朝阳区建国路88号',
        phone: '010-12345678',
        hours: '08:00-20:00',
        latitude: 39.908823,
        longitude: 116.397470,
        distance: '1.2km',
        rating: 4.8,
        services: ['中药材销售', '中医咨询', '养生指导', '文创产品'],
        features: ['停车位充足', '无障碍通道', '免费WiFi', '空调环境'],
        imageUrl: '🏪',
        isOpen: true,
        description: '慧心制药旗舰店，提供优质中药材和专业中医服务'
      },
      {
        id: 'store_002',
        name: '慧心制药分店(西单)',
        address: '北京市西城区西单北大街128号',
        phone: '010-87654321',
        hours: '09:00-19:00',
        latitude: 39.906217,
        longitude: 116.374142,
        distance: '2.8km',
        rating: 4.6,
        services: ['中药材销售', '养生茶具', '文创产品'],
        features: ['地铁直达', '免费WiFi', '空调环境'],
        imageUrl: '🏬',
        isOpen: true,
        description: '位于西单商圈，交通便利，产品丰富'
      },
      {
        id: 'store_003',
        name: '慧心制药分店(三里屯)',
        address: '北京市朝阳区三里屯路19号',
        phone: '010-11223344',
        hours: '10:00-22:00',
        latitude: 39.937967,
        longitude: 116.447836,
        distance: '4.5km',
        rating: 4.7,
        services: ['中药材销售', '中医咨询', '养生文创'],
        features: ['时尚装修', '年轻化服务', '免费WiFi'],
        imageUrl: '🏢',
        isOpen: false,
        description: '时尚现代的中医药体验店，深受年轻人喜爱'
      }
    ],
    
    // 当前选中的门店
    selectedStore: null,
    showStoreDetail: false,
    
    // 搜索相关
    searchKeyword: '',
    
    // 筛选相关
    showFilterModal: false,
    filterDistance: '全部',
    filterService: '全部',
    filterStatus: '全部',
    distanceOptions: ['全部', '1km内', '3km内', '5km内'],
    serviceOptions: ['全部', '中药材销售', '中医咨询', '养生指导', '文创产品'],
    statusOptions: ['全部', '营业中', '已关闭']
  },

  onLoad: function() {
    console.log('门店地图页面加载');
    this.initMap();
    this.getCurrentLocation();
  },

  // 初始化地图
  initMap: function() {
    var markers = this.data.storeList.map(function(store, index) {
      return {
        id: store.id,
        latitude: store.latitude,
        longitude: store.longitude,
        iconPath: '/images/map-marker.png',
        width: 30,
        height: 30,
        title: store.name,
        callout: {
          content: store.name,
          color: '#333',
          fontSize: 12,
          borderRadius: 5,
          bgColor: '#fff',
          padding: 5,
          display: 'ALWAYS'
        }
      };
    });
    
    this.setData({
      markers: markers
    });
  },

  // 获取当前位置
  getCurrentLocation: function() {
    var that = this;
    wx.getLocation({
      type: 'gcj02',
      success: function(res) {
        console.log('获取位置成功:', res);
        that.setData({
          latitude: res.latitude,
          longitude: res.longitude
        });
        
        // 计算门店距离
        that.calculateDistances(res.latitude, res.longitude);
      },
      fail: function(error) {
        console.error('获取位置失败:', error);
        wx.showModal({
          title: '提示',
          content: '获取位置失败，将显示默认位置',
          showCancel: false
        });
      }
    });
  },

  // 计算门店距离
  calculateDistances: function(userLat, userLng) {
    var that = this;
    var storeList = this.data.storeList.map(function(store) {
      var distance = that.getDistance(userLat, userLng, store.latitude, store.longitude);
      return Object.assign({}, store, {
        distance: distance < 1 ? (distance * 1000).toFixed(0) + 'm' : distance.toFixed(1) + 'km'
      });
    });

    // 按距离排序
    storeList.sort(function(a, b) {
      var distanceA = parseFloat(a.distance);
      var distanceB = parseFloat(b.distance);
      return distanceA - distanceB;
    });

    this.setData({
      storeList: storeList
    });
  },

  // 计算两点间距离（km）
  getDistance: function(lat1, lng1, lat2, lng2) {
    var radLat1 = lat1 * Math.PI / 180.0;
    var radLat2 = lat2 * Math.PI / 180.0;
    var a = radLat1 - radLat2;
    var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
    var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a/2),2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b/2),2)));
    s = s * 6378.137;
    s = Math.round(s * 10000) / 10000;
    return s;
  },

  // 地图标记点击
  onMarkerTap: function(e) {
    var markerId = e.detail.markerId;
    var store = this.data.storeList.find(function(item) {
      return item.id === markerId;
    });
    
    if (store) {
      this.setData({
        selectedStore: store,
        showStoreDetail: true
      });
    }
  },

  // 门店列表项点击
  onStoreItemTap: function(e) {
    var store = e.currentTarget.dataset.store;
    this.setData({
      selectedStore: store,
      showStoreDetail: true,
      latitude: store.latitude,
      longitude: store.longitude
    });
  },

  // 关闭门店详情
  closeStoreDetail: function() {
    this.setData({
      showStoreDetail: false,
      selectedStore: null
    });
  },

  // 拨打电话
  makeCall: function(e) {
    var phone = e.currentTarget.dataset.phone;
    wx.makePhoneCall({
      phoneNumber: phone,
      success: function() {
        console.log('拨打电话成功');
      },
      fail: function(error) {
        console.error('拨打电话失败:', error);
      }
    });
  },

  // 导航到门店
  navigateToStore: function(e) {
    var store = e.currentTarget.dataset.store;
    wx.openLocation({
      latitude: store.latitude,
      longitude: store.longitude,
      name: store.name,
      address: store.address,
      scale: 18
    });
  },

  // 搜索门店
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  onSearch: function() {
    var keyword = this.data.searchKeyword.trim();
    if (keyword === '') {
      return;
    }
    
    var filteredStores = this.data.storeList.filter(function(store) {
      return store.name.indexOf(keyword) !== -1 || 
             store.address.indexOf(keyword) !== -1;
    });
    
    if (filteredStores.length === 0) {
      wx.showToast({
        title: '未找到相关门店',
        icon: 'none'
      });
    } else {
      // 显示搜索结果
      this.setData({
        storeList: filteredStores
      });
    }
  },

  // 清空搜索
  clearSearch: function() {
    this.setData({
      searchKeyword: ''
    });
    this.loadAllStores();
  },

  // 加载所有门店
  loadAllStores: function() {
    // 重新加载完整的门店列表
    this.onLoad();
  },

  // 显示筛选弹窗
  showFilterModal: function() {
    this.setData({
      showFilterModal: true
    });
  },

  // 隐藏筛选弹窗
  hideFilterModal: function() {
    this.setData({
      showFilterModal: false
    });
  },

  // 选择距离筛选
  selectDistance: function(e) {
    this.setData({
      filterDistance: e.currentTarget.dataset.distance
    });
  },

  // 选择服务筛选
  selectService: function(e) {
    this.setData({
      filterService: e.currentTarget.dataset.service
    });
  },

  // 选择状态筛选
  selectStatus: function(e) {
    this.setData({
      filterStatus: e.currentTarget.dataset.status
    });
  },

  // 重置筛选
  resetFilter: function() {
    this.setData({
      filterDistance: '全部',
      filterService: '全部',
      filterStatus: '全部'
    });
  },

  // 应用筛选
  applyFilter: function() {
    this.hideFilterModal();
    // 这里可以根据筛选条件过滤门店列表
    wx.showToast({
      title: '筛选已应用',
      icon: 'success'
    });
  },

  // 分享功能
  onShareAppMessage: function() {
    return {
      title: '慧心制药 - 门店地图',
      path: '/pages/store-map/store-map',
      imageUrl: '/images/share-store-map.png'
    };
  }
});

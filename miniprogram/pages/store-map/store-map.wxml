<!--门店地图页面-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <input class="search-input" placeholder="搜索门店名称或地址" 
             value="{{searchKeyword}}" bindinput="onSearchInput" />
      <button class="search-btn" bindtap="onSearch">🔍</button>
      <button class="filter-btn" bindtap="showFilterModal">🎛️</button>
    </view>
  </view>

  <!-- 地图容器 -->
  <view class="map-container">
    <map id="storeMap" 
         class="map" 
         latitude="{{latitude}}" 
         longitude="{{longitude}}" 
         scale="{{scale}}"
         markers="{{markers}}"
         bindmarkertap="onMarkerTap"
         show-location="true">
    </map>
    
    <!-- 地图控制按钮 -->
    <view class="map-controls">
      <button class="control-btn" bindtap="getCurrentLocation">📍</button>
    </view>
  </view>

  <!-- 门店列表 -->
  <view class="store-list-section">
    <view class="section-header">
      <text class="section-title">附近门店</text>
      <text class="store-count">共{{storeList.length}}家</text>
    </view>
    
    <scroll-view class="store-list" scroll-y="true">
      <view class="store-item" wx:for="{{storeList}}" wx:key="id"
            bindtap="onStoreItemTap" data-store="{{item}}">
        <view class="store-image">
          <text class="image-placeholder">{{item.imageUrl}}</text>
        </view>
        
        <view class="store-info">
          <view class="store-header">
            <text class="store-name">{{item.name}}</text>
            <view class="store-status {{item.isOpen ? 'open' : 'closed'}}">
              <text class="status-text">{{item.isOpen ? '营业中' : '已关闭'}}</text>
            </view>
          </view>
          
          <text class="store-address">📍 {{item.address}}</text>
          <text class="store-distance">🚶 {{item.distance}}</text>
          <text class="store-hours">🕐 {{item.hours}}</text>
          
          <view class="store-rating">
            <text class="rating-stars">⭐⭐⭐⭐⭐</text>
            <text class="rating-score">{{item.rating}}</text>
          </view>
          
          <view class="store-services">
            <text class="service-tag" wx:for="{{item.services}}" wx:key="*this" wx:for-item="service">
              {{service}}
            </text>
          </view>
        </view>
        
        <view class="store-actions">
          <button class="action-btn call-btn" bindtap="makeCall" data-phone="{{item.phone}}" catchtap="stopPropagation">
            📞
          </button>
          <button class="action-btn nav-btn" bindtap="navigateToStore" data-store="{{item}}" catchtap="stopPropagation">
            🧭
          </button>
        </view>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 门店详情弹窗 -->
<view class="modal-overlay" wx:if="{{showStoreDetail}}" bindtap="closeStoreDetail">
  <view class="store-detail-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{selectedStore.name}}</text>
      <button class="modal-close" bindtap="closeStoreDetail">×</button>
    </view>
    
    <scroll-view class="modal-body" scroll-y="true">
      <view class="detail-image">
        <text class="image-placeholder">{{selectedStore.imageUrl}}</text>
      </view>
      
      <view class="detail-info">
        <text class="detail-description">{{selectedStore.description}}</text>
        
        <view class="detail-item">
          <text class="detail-label">📍 地址</text>
          <text class="detail-value">{{selectedStore.address}}</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">📞 电话</text>
          <text class="detail-value">{{selectedStore.phone}}</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">🕐 营业时间</text>
          <text class="detail-value">{{selectedStore.hours}}</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">🚶 距离</text>
          <text class="detail-value">{{selectedStore.distance}}</text>
        </view>
        
        <view class="detail-section">
          <text class="section-title">服务项目</text>
          <view class="service-list">
            <text class="service-item" wx:for="{{selectedStore.services}}" wx:key="*this">
              ✓ {{item}}
            </text>
          </view>
        </view>
        
        <view class="detail-section">
          <text class="section-title">店铺特色</text>
          <view class="feature-list">
            <text class="feature-item" wx:for="{{selectedStore.features}}" wx:key="*this">
              ⭐ {{item}}
            </text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <view class="modal-footer">
      <button class="footer-btn call-btn" bindtap="makeCall" data-phone="{{selectedStore.phone}}">
        📞 拨打电话
      </button>
      <button class="footer-btn nav-btn" bindtap="navigateToStore" data-store="{{selectedStore}}">
        🧭 导航前往
      </button>
    </view>
  </view>
</view>

<!-- 筛选弹窗 -->
<view class="modal-overlay" wx:if="{{showFilterModal}}" bindtap="hideFilterModal">
  <view class="filter-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">筛选门店</text>
      <button class="modal-close" bindtap="hideFilterModal">×</button>
    </view>
    
    <view class="filter-content">
      <view class="filter-group">
        <text class="filter-label">距离</text>
        <view class="filter-options">
          <view class="filter-option {{filterDistance === item ? 'selected' : ''}}" 
                wx:for="{{distanceOptions}}" wx:key="*this"
                bindtap="selectDistance" data-distance="{{item}}">
            <text class="option-text">{{item}}</text>
          </view>
        </view>
      </view>
      
      <view class="filter-group">
        <text class="filter-label">服务</text>
        <view class="filter-options">
          <view class="filter-option {{filterService === item ? 'selected' : ''}}" 
                wx:for="{{serviceOptions}}" wx:key="*this"
                bindtap="selectService" data-service="{{item}}">
            <text class="option-text">{{item}}</text>
          </view>
        </view>
      </view>
      
      <view class="filter-group">
        <text class="filter-label">状态</text>
        <view class="filter-options">
          <view class="filter-option {{filterStatus === item ? 'selected' : ''}}" 
                wx:for="{{statusOptions}}" wx:key="*this"
                bindtap="selectStatus" data-status="{{item}}">
            <text class="option-text">{{item}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="filter-footer">
      <button class="filter-btn reset-btn" bindtap="resetFilter">重置</button>
      <button class="filter-btn apply-btn" bindtap="applyFilter">应用</button>
    </view>
  </view>
</view>

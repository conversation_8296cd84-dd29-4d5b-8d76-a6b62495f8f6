<!--数据概览页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">📊 数据概览</text>
    <view class="admin-badge">
      <text class="badge-icon">👑</text>
      <text class="badge-text">管理员专属</text>
    </view>
  </view>

  <!-- 总体统计 -->
  <view class="summary-section">
    <view class="section-title">总体统计</view>
    <view class="summary-grid">
      <view class="summary-item" bindtap="viewMedicines">
        <text class="summary-icon">🌿</text>
        <text class="summary-number">{{totalMedicines}}</text>
        <text class="summary-label">中药材</text>
      </view>
      <view class="summary-item" bindtap="viewProducts">
        <text class="summary-icon">🎋</text>
        <text class="summary-number">{{totalProducts}}</text>
        <text class="summary-label">文创产品</text>
      </view>
      <view class="summary-item">
        <text class="summary-icon">📦</text>
        <text class="summary-number">{{totalItems}}</text>
        <text class="summary-label">总商品</text>
      </view>
    </view>
  </view>

  <!-- 中药材分类统计 -->
  <view class="stats-section">
    <view class="section-title">中药材分类统计</view>
    <view class="stats-list">
      <view class="stats-item" wx:for="{{medicineStats}}" wx:key="category" 
            bindtap="viewCategoryDetail" data-category="{{item.category}}" data-type="medicine">
        <view class="stats-info">
          <text class="stats-category">{{item.category}}</text>
          <text class="stats-count">{{item.count}}种</text>
        </view>
        <view class="stats-bar">
          <view class="stats-progress" style="width: {{item.percentage}}%"></view>
        </view>
        <text class="stats-percentage">{{item.percentage}}%</text>
      </view>
    </view>
  </view>

  <!-- 文创产品分类统计 -->
  <view class="stats-section">
    <view class="section-title">文创产品分类统计</view>
    <view class="stats-list">
      <view class="stats-item" wx:for="{{productStats}}" wx:key="category"
            bindtap="viewCategoryDetail" data-category="{{item.category}}" data-type="product">
        <view class="stats-info">
          <text class="stats-category">{{item.category}}</text>
          <text class="stats-count">{{item.count}}件</text>
        </view>
        <view class="stats-bar">
          <view class="stats-progress product" style="width: {{item.percentage}}%"></view>
        </view>
        <text class="stats-percentage">{{item.percentage}}%</text>
      </view>
    </view>
  </view>

  <!-- 价格区间统计 -->
  <view class="stats-section">
    <view class="section-title">价格区间分布</view>
    <view class="stats-list">
      <view class="stats-item" wx:for="{{priceRanges}}" wx:key="range">
        <view class="stats-info">
          <text class="stats-category">{{item.range}}</text>
          <text class="stats-count">{{item.count}}件</text>
        </view>
        <view class="stats-bar">
          <view class="stats-progress price" style="width: {{item.percentage}}%"></view>
        </view>
        <text class="stats-percentage">{{item.percentage}}%</text>
      </view>
    </view>
  </view>

  <!-- 热销商品 -->
  <view class="hot-section">
    <view class="section-title">热销商品 TOP 5</view>
    <view class="hot-list">
      <view class="hot-item" wx:for="{{hotItems}}" wx:key="name">
        <view class="hot-rank">{{index + 1}}</view>
        <view class="hot-info">
          <text class="hot-name">{{item.name}}</text>
          <text class="hot-type">{{item.type}} · {{item.category}}</text>
        </view>
        <view class="hot-sales">
          <text class="sales-number">{{item.sales}}</text>
          <text class="sales-unit">次浏览</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-buttons">
      <button class="action-btn primary" bindtap="refreshData">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">刷新数据</text>
      </button>
      <button class="action-btn secondary" bindtap="exportData">
        <text class="btn-icon">📤</text>
        <text class="btn-text">导出数据</text>
      </button>
    </view>
  </view>
</view>

// 数据概览页面
var app = getApp();

Page({
  data: {
    isAdmin: false,
    totalMedicines: 25,
    totalProducts: 25,
    totalItems: 50,
    
    // 中药材分类统计
    medicineStats: [
      { category: '补气药', count: 5, percentage: 20 },
      { category: '补血药', count: 5, percentage: 20 },
      { category: '清热药', count: 5, percentage: 20 },
      { category: '解表药', count: 3, percentage: 12 },
      { category: '化痰药', count: 2, percentage: 8 },
      { category: '活血药', count: 2, percentage: 8 },
      { category: '安神药', count: 2, percentage: 8 },
      { category: '消食药', count: 1, percentage: 4 }
    ],
    
    // 文创产品分类统计
    productStats: [
      { category: '茶具', count: 4, percentage: 16 },
      { category: '书籍', count: 4, percentage: 16 },
      { category: '养生用品', count: 4, percentage: 16 },
      { category: '香薰用品', count: 3, percentage: 12 },
      { category: '保健器具', count: 4, percentage: 16 },
      { category: '文房四宝', count: 3, percentage: 12 },
      { category: '装饰品', count: 3, percentage: 12 }
    ],
    
    // 价格区间统计
    priceRanges: [
      { range: '0-10元', count: 8, percentage: 16 },
      { range: '10-50元', count: 15, percentage: 30 },
      { range: '50-100元', count: 12, percentage: 24 },
      { range: '100-200元', count: 8, percentage: 16 },
      { range: '200元以上', count: 7, percentage: 14 }
    ],
    
    // 热销商品
    hotItems: [
      { name: '人参', type: '中药材', sales: 1250, category: '补气药' },
      { name: '枸杞子', type: '中药材', sales: 980, category: '补血药' },
      { name: '金银花', type: '中药材', sales: 678, category: '清热药' },
      { name: '中医养生茶具套装', type: '文创产品', sales: 125, category: '茶具' },
      { name: '本草纲目典藏版', type: '文创产品', sales: 234, category: '书籍' }
    ]
  },

  onLoad: function() {
    console.log('数据概览页面加载');
    this.checkAdminPermission();
  },

  onShow: function() {
    this.setData({
      isAdmin: app.globalData.isAdmin || false
    });
  },

  // 检查管理员权限
  checkAdminPermission: function() {
    if (!app.globalData.isAdmin) {
      wx.showModal({
        title: '权限不足',
        content: '此页面仅限管理员访问',
        showCancel: false,
        success: function() {
          wx.navigateBack();
        }
      });
      return;
    }
    
    this.setData({
      isAdmin: true
    });
  },

  // 查看中药材列表
  viewMedicines: function() {
    wx.navigateTo({
      url: '/pages/medicines/medicines?mode=admin'
    });
  },

  // 查看文创产品列表
  viewProducts: function() {
    wx.navigateTo({
      url: '/pages/products/products?mode=admin'
    });
  },

  // 查看分类详情
  viewCategoryDetail: function(e) {
    var category = e.currentTarget.dataset.category;
    var type = e.currentTarget.dataset.type;
    
    if (type === 'medicine') {
      wx.navigateTo({
        url: '/pages/medicines/medicines?category=' + category
      });
    } else {
      wx.navigateTo({
        url: '/pages/products/products?category=' + category
      });
    }
  },

  // 导出数据
  exportData: function() {
    wx.showModal({
      title: '导出数据',
      content: '数据导出功能正在开发中...',
      showCancel: false
    });
  },

  // 刷新数据
  refreshData: function() {
    wx.showLoading({
      title: '刷新中...'
    });
    
    setTimeout(function() {
      wx.hideLoading();
      wx.showToast({
        title: '数据已刷新',
        icon: 'success'
      });
    }, 1500);
  }
});

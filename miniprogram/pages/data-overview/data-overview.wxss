/* 数据概览页面样式 */
.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 15rpx;
}

.admin-badge {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 215, 0, 0.3);
}

.badge-icon {
  font-size: 20rpx;
  color: #8B4513;
}

.badge-text {
  font-size: 20rpx;
  font-weight: bold;
  color: #8B4513;
}

/* 通用区块样式 */
.summary-section,
.stats-section,
.hot-section,
.action-section {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.12);
  border: 1rpx solid rgba(139, 69, 19, 0.08);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 25rpx;
  border-left: 4rpx solid #8B4513;
  padding-left: 15rpx;
}

/* 总体统计样式 */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 25rpx;
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.05), rgba(139, 69, 19, 0.02));
  border-radius: 20rpx;
  border: 1rpx solid rgba(139, 69, 19, 0.1);
  transition: all 0.3s ease;
}

.summary-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(139, 69, 19, 0.15);
}

.summary-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.summary-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 5rpx;
}

.summary-label {
  font-size: 20rpx;
  color: #666;
}

/* 统计列表样式 */
.stats-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 15rpx;
  transition: all 0.3s ease;
}

.stats-item:active {
  background: #f0f0f0;
  transform: translateY(-1rpx);
}

.stats-info {
  flex: 0 0 120rpx;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.stats-category {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
}

.stats-count {
  font-size: 18rpx;
  color: #666;
}

.stats-bar {
  flex: 1;
  height: 12rpx;
  background: #e0e0e0;
  border-radius: 6rpx;
  overflow: hidden;
}

.stats-progress {
  height: 100%;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.stats-progress.product {
  background: linear-gradient(135deg, #228B22, #32CD32);
}

.stats-progress.price {
  background: linear-gradient(135deg, #4169E1, #6495ED);
}

.stats-percentage {
  flex: 0 0 50rpx;
  font-size: 18rpx;
  font-weight: bold;
  color: #8B4513;
  text-align: right;
}

/* 热销商品样式 */
.hot-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.hot-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.05));
  border-radius: 15rpx;
  border: 1rpx solid rgba(255, 215, 0, 0.2);
}

.hot-rank {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #8B4513;
  font-size: 20rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hot-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.hot-name {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
}

.hot-type {
  font-size: 18rpx;
  color: #666;
}

.hot-sales {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2rpx;
}

.sales-number {
  font-size: 24rpx;
  font-weight: bold;
  color: #E74C3C;
}

.sales-unit {
  font-size: 16rpx;
  color: #999;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 18rpx;
  border-radius: 25rpx;
  font-size: 22rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(139, 69, 19, 0.3);
}

.action-btn.primary:active {
  background: linear-gradient(135deg, #A0522D, #8B4513);
  transform: translateY(-2rpx);
}

.action-btn.secondary {
  background: linear-gradient(135deg, #228B22, #32CD32);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(34, 139, 34, 0.3);
}

.action-btn.secondary:active {
  background: linear-gradient(135deg, #32CD32, #228B22);
  transform: translateY(-2rpx);
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 22rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .summary-grid {
    grid-template-columns: 1fr;
    gap: 15rpx;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}

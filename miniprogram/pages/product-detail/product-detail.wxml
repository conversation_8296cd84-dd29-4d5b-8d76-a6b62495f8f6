<!--文创产品详情页面-->
<view class="container" wx:if="{{!loading}}">
  <!-- 商品主图和基本信息 -->
  <view class="product-header">
    <view class="product-image-section">
      <view class="main-image">
        <image class="product-detail-image" src="{{product.imageUrl}}" mode="aspectFill" wx:if="{{product.imageUrl && !imageError}}" binderror="onImageError" bindload="onImageLoad"></image>
        <view class="image-placeholder" wx:if="{{!product.imageUrl || imageError}}">
          <text class="placeholder-icon">🎨</text>
          <text class="placeholder-text">{{product.name}}</text>
        </view>
      </view>
      <view class="image-badges" wx:if="{{product.isHot}}">
        <text class="badge hot">🔥 热销</text>
      </view>
    </view>
    
    <view class="product-info-section">
      <text class="product-name">{{product.name}}</text>
      <text class="product-category">{{product.category}}</text>
      <text class="product-description">{{product.description}}</text>
      
      <view class="price-section">
        <text class="current-price">¥{{product.price}}</text>
        <text class="original-price" wx:if="{{product.originalPrice > product.price}}">¥{{product.originalPrice}}</text>
        <text class="stock-info">库存：{{product.stock}}件</text>
      </view>
      
      <view class="rating-section">
        <view class="rating-stars">
          <text class="star {{index < product.rating ? 'filled' : ''}}" 
                wx:for="{{[1,2,3,4,5]}}" wx:key="*this" wx:for-index="index">⭐</text>
        </view>
        <text class="rating-text">{{product.rating}}分</text>
        <text class="review-count">{{product.reviewCount}}条评价</text>
        <text class="sales-count">销量{{product.sales}}</text>
      </view>
      
      <view class="basic-info">
        <view class="info-item">
          <text class="info-label">品牌：</text>
          <text class="info-value">{{product.brand}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">材质：</text>
          <text class="info-value">{{product.material}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">尺寸：</text>
          <text class="info-value">{{product.size}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 数量选择 -->
  <view class="quantity-section">
    <view class="section-title">购买数量</view>
    <view class="quantity-selector">
      <button class="quantity-btn decrease" bindtap="decreaseQuantity">-</button>
      <input class="quantity-input" type="number" value="{{quantity}}" bindinput="inputQuantity" />
      <button class="quantity-btn increase" bindtap="increaseQuantity">+</button>
      <text class="quantity-unit">件</text>
    </view>
  </view>

  <!-- 产品特色 -->
  <view class="features-section">
    <view class="section-title">产品特色</view>
    <view class="features-list">
      <view class="feature-item" wx:for="{{product.features}}" wx:key="*this">
        <text class="feature-icon">✨</text>
        <text class="feature-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 详细信息 -->
  <view class="detail-section">
    <view class="section-title">详细信息</view>
    <view class="detail-content">
      <view class="detail-item">
        <text class="detail-label">产品描述</text>
        <text class="detail-text">{{product.detailedDescription}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">重量</text>
        <text class="detail-text">{{product.weight}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">包装清单</text>
        <text class="detail-text">{{product.package}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">保养说明</text>
        <text class="detail-text">{{product.maintenance}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">质保信息</text>
        <text class="detail-text">{{product.warranty}}</text>
      </view>
    </view>
  </view>

  <!-- 用户评价 -->
  <view class="review-section">
    <view class="section-title">用户评价</view>
    <view class="review-list">
      <view class="review-item" wx:for="{{reviews}}" wx:key="id">
        <view class="review-header">
          <text class="reviewer-avatar">{{item.avatar}}</text>
          <view class="reviewer-info">
            <text class="reviewer-name">{{item.userName}}</text>
            <view class="review-rating">
              <text class="star {{index < item.rating ? 'filled' : ''}}" 
                    wx:for="{{[1,2,3,4,5]}}" wx:key="*this" wx:for-index="index">⭐</text>
            </view>
          </view>
          <text class="review-date">{{item.date}}</text>
        </view>
        <text class="review-content">{{item.content}}</text>
      </view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section" wx:if="{{relatedProducts.length > 0}}">
    <view class="section-title">相关推荐</view>
    <scroll-view class="related-scroll" scroll-x="true">
      <view class="related-item" wx:for="{{relatedProducts}}" wx:key="id"
            bindtap="viewRelated" data-id="{{item.id}}">
        <text class="related-image">{{item.imageUrl}}</text>
        <text class="related-name">{{item.name}}</text>
        <text class="related-price">¥{{item.price}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="action-btn cart-btn" bindtap="addToCart">
      <text class="btn-icon">🛒</text>
      <text class="btn-text">加入购物车</text>
    </button>
    <button class="action-btn buy-btn" bindtap="buyNow">
      <text class="btn-icon">💰</text>
      <text class="btn-text">立即购买</text>
    </button>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-content">
    <text class="loading-icon">⏳</text>
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 占位，防止底部按钮遮挡内容 -->
<view class="bottom-placeholder"></view>

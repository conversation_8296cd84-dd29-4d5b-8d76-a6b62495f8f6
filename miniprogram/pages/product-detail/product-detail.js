// 文创产品详情页面
var app = getApp();

Page({
  data: {
    productId: '',
    product: null,
    loading: true,
    quantity: 1,
    
    // 相关推荐
    relatedProducts: [],
    
    // 用户评价
    reviews: [
      {
        id: '1',
        userName: '茶艺爱好者',
        avatar: '🍵',
        rating: 5,
        content: '茶具质量很好，做工精细，泡茶效果很棒！',
        date: '2024-01-18',
        images: []
      },
      {
        id: '2',
        userName: '文化收藏家',
        avatar: '📚',
        rating: 4,
        content: '包装精美，内容丰富，值得收藏。',
        date: '2024-01-12',
        images: []
      }
    ]
  },

  onLoad: function(options) {
    console.log('文创产品详情页面加载，参数:', options);
    if (options.id) {
      this.setData({
        productId: options.id
      });
      this.loadProductDetail(options.id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onShow: function() {
    // 页面显示时更新购物车数量
    if (app.updateCartBadge) {
      app.updateCartBadge();
    }
  },

  // 加载产品详情
  loadProductDetail: function(id) {
    var that = this;
    that.setData({ loading: true });

    // 模拟从数据库或缓存中获取详细数据
    var productData = that.getProductData(id);
    
    if (productData) {
      that.setData({
        product: productData,
        loading: false
      });
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: productData.name + ' - 文创产品详情'
      });
      
      // 加载相关推荐
      that.loadRelatedProducts(productData.category);
    } else {
      that.setData({ loading: false });
      wx.showModal({
        title: '提示',
        content: '未找到该产品信息',
        showCancel: false,
        success: function() {
          wx.navigateBack();
        }
      });
    }
  },

  // 获取产品数据（模拟数据库查询）
  getProductData: function(id) {
    var products = {
      '1': {
        id: '1',
        name: '中医养生茶具套装',
        category: '茶具',
        description: '精美的紫砂茶具，包含茶壶、茶杯、茶盘等，适合泡制各种养生茶。',
        detailedDescription: '这套中医养生茶具采用宜兴紫砂制作，经过传统工艺精心打造。套装包含一壶四杯一盘，设计典雅，实用性强。紫砂材质透气性好，有利于茶叶的发酵和香味的保持，是品茶养生的理想选择。',
        price: 168,
        originalPrice: 198,
        stock: 50,
        sales: 125,
        isHot: true,
        rating: 4.8,
        reviewCount: 89,
        imageUrl: '🍵',
        brand: '慧心制药',
        material: '宜兴紫砂',
        size: '茶壶容量：200ml，茶杯容量：50ml',
        weight: '约1.2kg',
        package: '茶壶×1，茶杯×4，茶盘×1，说明书×1',
        maintenance: '使用后清水冲洗，自然晾干，避免使用洗洁精',
        warranty: '1年质保',
        features: [
          '宜兴紫砂材质，透气性佳',
          '传统手工制作，工艺精湛',
          '一壶四杯设计，适合家庭使用',
          '典雅造型，兼具实用与美观',
          '适合各种茶叶冲泡'
        ]
      },
      '2': {
        id: '2',
        name: '紫砂功夫茶具',
        category: '茶具',
        description: '宜兴紫砂制作，手工精制，适合品茗养生。',
        detailedDescription: '精选宜兴优质紫砂泥料，由资深工艺师手工制作。壶身线条流畅，出水顺畅，断水干净。适合冲泡乌龙茶、普洱茶等各类茶叶，是茶艺爱好者的首选。',
        price: 288,
        originalPrice: 328,
        stock: 25,
        sales: 78,
        isHot: false,
        rating: 4.9,
        reviewCount: 56,
        imageUrl: '🫖',
        brand: '慧心制药',
        material: '宜兴紫砂',
        size: '茶壶容量：150ml',
        weight: '约0.8kg',
        package: '紫砂茶壶×1，收藏证书×1，保养说明×1',
        maintenance: '开壶后使用，定期保养，避免油污接触',
        warranty: '2年质保',
        features: [
          '名师手工制作，收藏价值高',
          '泥料纯正，透气性极佳',
          '造型古朴典雅，韵味十足',
          '出水流畅，使用体验佳',
          '越用越润，包浆效果好'
        ]
      },
      '5': {
        id: '5',
        name: '本草纲目典藏版',
        category: '书籍',
        description: '李时珍经典著作《本草纲目》典藏版，精装硬壳，内容详实。',
        detailedDescription: '《本草纲目》是明代李时珍编写的中医药学巨著，本典藏版采用精装硬壳装帧，内页使用高品质纸张印刷，图文并茂，是中医药学习和收藏的珍贵资料。',
        price: 58,
        originalPrice: 68,
        stock: 100,
        sales: 234,
        isHot: false,
        rating: 4.9,
        reviewCount: 156,
        imageUrl: '📚',
        brand: '慧心制药出版',
        material: '精装硬壳，铜版纸内页',
        size: '16开本（26cm×19cm）',
        weight: '约1.5kg',
        package: '精装图书×1，书签×1，收藏证书×1',
        maintenance: '避免潮湿，远离火源，轻拿轻放',
        warranty: '非人为损坏可退换',
        features: [
          '李时珍原著，权威版本',
          '精装硬壳，装帧精美',
          '高清彩图，图文并茂',
          '注释详细，便于理解',
          '收藏价值高，传承经典'
        ]
      }
    };
    
    return products[id] || null;
  },

  // 加载相关推荐
  loadRelatedProducts: function(category) {
    // 模拟相关推荐数据
    var related = [
      {
        id: '3',
        name: '青瓷茶杯套装',
        category: '茶具',
        price: 98,
        imageUrl: '🍵'
      },
      {
        id: '4',
        name: '竹制茶盘',
        category: '茶具',
        price: 128,
        imageUrl: '🎋'
      },
      {
        id: '6',
        name: '黄帝内经注释版',
        category: '书籍',
        price: 48,
        imageUrl: '📖'
      }
    ];
    
    this.setData({
      relatedProducts: related
    });
  },

  // 数量减少
  decreaseQuantity: function() {
    var quantity = this.data.quantity;
    if (quantity > 1) {
      this.setData({
        quantity: quantity - 1
      });
    }
  },

  // 数量增加
  increaseQuantity: function() {
    var quantity = this.data.quantity;
    var stock = this.data.product.stock;
    if (quantity < stock) {
      this.setData({
        quantity: quantity + 1
      });
    } else {
      wx.showToast({
        title: '库存不足',
        icon: 'none'
      });
    }
  },

  // 输入数量
  inputQuantity: function(e) {
    var value = parseInt(e.detail.value) || 1;
    var stock = this.data.product.stock;
    if (value > stock) {
      value = stock;
      wx.showToast({
        title: '超出库存限制',
        icon: 'none'
      });
    }
    this.setData({
      quantity: value
    });
  },

  // 添加到购物车
  addToCart: function() {
    var that = this;
    var product = that.data.product;
    var quantity = that.data.quantity;

    if (!product) {
      wx.showToast({
        title: '商品信息错误',
        icon: 'none'
      });
      return;
    }

    // 确保有用户信息
    that.ensureUserInfo();

    // 获取用户信息
    var userInfo = app.globalData.userInfo;
    if (!userInfo) {
      wx.showToast({
        title: '用户信息错误',
        icon: 'none'
      });
      return;
    }

    // 创建购物车商品
    var cartItem = {
      id: 'cart_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      productId: product.id,
      name: product.name,
      price: product.price,
      imageUrl: product.imageUrl,
      type: 'product',
      category: product.category,
      quantity: quantity,
      selected: true,
      spec: '1件',
      unit: '/件',
      totalPrice: (product.price * quantity).toFixed(2)
    };

    // 获取当前购物车数据
    var cartKey = 'cart_' + userInfo.id;
    var cartData = wx.getStorageSync(cartKey) || [];

    // 检查是否已存在相同商品
    var existingIndex = -1;
    for (var i = 0; i < cartData.length; i++) {
      if (cartData[i].productId === product.id) {
        existingIndex = i;
        break;
      }
    }

    if (existingIndex >= 0) {
      // 如果已存在，增加数量
      cartData[existingIndex].quantity += quantity;
      cartData[existingIndex].totalPrice = (cartData[existingIndex].price * cartData[existingIndex].quantity).toFixed(2);
    } else {
      // 如果不存在，添加新商品
      cartData.push(cartItem);
    }

    try {
      // 保存到本地存储
      wx.setStorageSync(cartKey, cartData);

      // 显示成功提示
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      });

      // 更新购物车徽章
      if (app.updateCartBadge) {
        app.updateCartBadge();
      }

    } catch (error) {
      console.error('保存购物车数据失败:', error);
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      });
    }
  },

  // 立即购买
  buyNow: function() {
    // 先添加到购物车
    this.addToCart();
    
    // 跳转到购物车页面
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/cart/cart'
      });
    }, 1000);
  },

  // 查看相关商品
  viewRelated: function(e) {
    var id = e.currentTarget.dataset.id;
    wx.redirectTo({
      url: '/pages/product-detail/product-detail?id=' + id
    });
  },

  // 确保用户信息存在
  ensureUserInfo: function() {
    if (!app.globalData.userInfo) {
      var guestUser = {
        id: 'guest_' + Date.now(),
        name: '游客用户',
        avatar: '👤',
        role: 'user',
        isGuest: true
      };
      app.setUserInfo(guestUser);
    }
  },

  // 分享功能
  onShareAppMessage: function() {
    var product = this.data.product;
    return {
      title: product ? product.name + ' - 精品文创' : '慧心制药 - 文创产品详情',
      path: '/pages/product-detail/product-detail?id=' + this.data.productId,
      imageUrl: product ? product.imageUrl : ''
    };
  }
});

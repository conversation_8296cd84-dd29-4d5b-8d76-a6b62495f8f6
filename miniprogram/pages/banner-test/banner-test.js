// 轮播图测试页面
Page({
  data: {
    bannerList: [],
    testResults: []
  },

  onLoad: function() {
    console.log('轮播图测试页面加载');
    this.loadBannerData();
    this.runImageTests();
  },

  // 加载轮播图数据
  loadBannerData: function() {
    const bannerList = [
      {
        id: 1,
        title: '传承千年中医智慧',
        description: '精选道地药材，传承古法炮制工艺',
        imageUrl: '/images/轮播图/轮播图-传承千年中医智慧.jpg',
        icon: '🌿'
      },
      {
        id: 2,
        title: '精选道地药材',
        description: '源头直采，严格把控每一道工序',
        imageUrl: '/images/轮播图/轮播图-精选道地药材.jpg',
        icon: '🍃'
      },
      {
        id: 3,
        title: '弘扬养生文化',
        description: '传播中医文化，弘扬养生智慧',
        imageUrl: '/images/轮播图/轮播图-弘扬养生文化.jpg',
        icon: '🎋'
      }
    ];

    this.setData({ bannerList });
    console.log('轮播图数据已加载:', bannerList.length, '张');
  },

  // 运行图片测试
  runImageTests: function() {
    var tests = [];
    var that = this;
    
    this.data.bannerList.forEach(function(banner, index) {
      tests.push({
        id: banner.id,
        title: banner.title,
        imageUrl: banner.imageUrl,
        status: 'testing',
        message: '检测图片是否存在...'
      });
    });

    this.setData({ testResults: tests });

    // 模拟测试完成
    setTimeout(function() {
      var updatedTests = tests.map(function(test) {
        return Object.assign({}, test, {
          status: 'unknown',
          message: '请查看轮播图显示效果'
        });
      });
      
      that.setData({ testResults: updatedTests });
    }, 1000);
  },

  // 轮播图图片加载成功
  onBannerImageLoad: function(e) {
    var index = e.currentTarget.dataset.index;
    var testResults = this.data.testResults;
    if (testResults[index]) {
      testResults[index].status = 'success';
      testResults[index].message = '图片加载成功';
      this.setData({ testResults: testResults });
    }
    console.log('轮播图图片加载成功:', index);
  },

  // 轮播图图片加载错误
  onBannerImageError: function(e) {
    var index = e.currentTarget.dataset.index;
    var bannerList = this.data.bannerList;
    var testResults = this.data.testResults;
    
    if (bannerList[index]) {
      bannerList[index].imageError = true;
      this.setData({ bannerList: bannerList });
    }
    
    if (testResults[index]) {
      testResults[index].status = 'error';
      testResults[index].message = '图片加载失败，显示备用图标';
      this.setData({ testResults: testResults });
    }
    
    console.log('轮播图图片加载失败:', index);
  },

  // 轮播图点击事件
  onBannerTap: function(e) {
    var item = e.currentTarget.dataset.item;
    wx.showModal({
      title: '轮播图点击',
      content: '点击了：' + item.title,
      showCancel: false
    });
  },

  // 返回首页
  goHome: function() {
    wx.navigateBack();
  }
});

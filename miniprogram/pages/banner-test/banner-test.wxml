<!--轮播图测试页面-->
<view class="container">
  <view class="header">
    <text class="title">🎠 轮播图测试</text>
    <text class="subtitle">测试轮播图图片是否能正确加载</text>
  </view>

  <!-- 轮播图展示 -->
  <view class="banner-section">
    <view class="section-title">
      <text class="title-icon">🖼️</text>
      <text class="title-text">轮播图展示</text>
    </view>
    
    <view class="banner-container">
      <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="800">
        <swiper-item wx:for="{{bannerList}}" wx:key="id" bindtap="onBannerTap" data-item="{{item}}">
          <view class="banner-item">
            <image 
              class="banner-image" 
              src="{{item.imageUrl}}" 
              mode="aspectFill" 
              wx:if="{{item.imageUrl}}"
              bindload="onBannerImageLoad"
              binderror="onBannerImageError"
              data-index="{{index}}">
            </image>
            <view class="banner-bg" wx:if="{{!item.imageUrl || item.imageError}}">
              <text class="banner-icon">{{item.icon || '🌿'}}</text>
            </view>
            <view class="banner-content">
              <text class="banner-title">{{item.title}}</text>
              <text class="banner-desc">{{item.description}}</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="test-section">
    <view class="section-title">
      <text class="title-icon">🧪</text>
      <text class="title-text">测试结果</text>
    </view>
    
    <view class="test-list">
      <view class="test-item" wx:for="{{testResults}}" wx:key="id">
        <view class="test-info">
          <text class="test-name">{{item.title}}</text>
          <text class="test-path">{{item.imageUrl}}</text>
        </view>
        
        <view class="test-status">
          <text class="status-icon {{item.status}}">
            {{item.status === 'success' ? '✅' : item.status === 'error' ? '❌' : item.status === 'testing' ? '⏳' : '❓'}}
          </text>
          <text class="status-text">{{item.message}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 说明信息 -->
  <view class="info-section">
    <view class="section-title">
      <text class="title-icon">ℹ️</text>
      <text class="title-text">说明</text>
    </view>
    
    <view class="info-content">
      <view class="info-item">
        <text class="info-icon">✅</text>
        <text class="info-text">如果图片正常显示，说明图片文件存在且路径正确</text>
      </view>
      <view class="info-item">
        <text class="info-icon">❌</text>
        <text class="info-text">如果显示emoji图标，说明图片加载失败，使用了备用方案</text>
      </view>
      <view class="info-item">
        <text class="info-icon">🔧</text>
        <text class="info-text">请检查图片文件是否存在于正确的路径下</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="back-btn" bindtap="goHome">
      <text class="btn-icon">🏠</text>
      <text class="btn-text">返回首页</text>
    </button>
  </view>
</view>

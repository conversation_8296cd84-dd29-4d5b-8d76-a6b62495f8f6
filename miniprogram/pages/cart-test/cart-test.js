// 购物车测试页面
var app = getApp();

Page({
  data: {
    userInfo: null,
    cartItems: [],
    testProducts: [
      {
        id: 'test_1',
        name: '测试人参',
        price: 12.5,
        imageUrl: '🌿',
        type: 'medicine',
        category: '补气药',
        stock: 100
      },
      {
        id: 'test_2',
        name: '测试茶具',
        price: 168,
        imageUrl: '🍵',
        type: 'product',
        category: '茶具',
        stock: 50
      }
    ]
  },

  onLoad: function() {
    console.log('购物车测试页面加载');
    this.loadUserInfo();
    this.loadCartItems();
  },

  onShow: function() {
    this.loadUserInfo();
    this.loadCartItems();
  },

  // 加载用户信息
  loadUserInfo: function() {
    this.setData({
      userInfo: app.globalData.userInfo
    });
    console.log('当前用户信息:', app.globalData.userInfo);
  },

  // 加载购物车数据
  loadCartItems: function() {
    var userInfo = app.globalData.userInfo;
    if (userInfo) {
      var cartKey = 'cart_' + userInfo.id;
      var cartData = wx.getStorageSync(cartKey) || [];
      this.setData({
        cartItems: cartData
      });
      console.log('购物车数据:', cartData);
    }
  },

  // 创建游客账号
  createGuestUser: function() {
    var guestUser = {
      id: 'guest_' + Date.now(),
      name: '游客用户',
      avatar: '👤',
      role: 'user',
      isGuest: true
    };
    
    app.setUserInfo(guestUser);
    this.loadUserInfo();
    
    wx.showToast({
      title: '游客账号已创建',
      icon: 'success'
    });
  },

  // 添加测试商品到购物车
  addTestProduct: function(e) {
    var item = e.currentTarget.dataset.item;
    
    // 确保有用户信息
    if (!app.globalData.userInfo) {
      this.createGuestUser();
    }
    
    var userInfo = app.globalData.userInfo;
    var cartKey = 'cart_' + userInfo.id;
    var cartData = wx.getStorageSync(cartKey) || [];
    
    // 创建购物车商品
    var cartItem = {
      id: 'cart_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      productId: item.id,
      name: item.name,
      price: item.price,
      imageUrl: item.imageUrl,
      type: item.type,
      category: item.category,
      quantity: 1,
      selected: true,
      spec: item.type === 'medicine' ? '1克' : '1件',
      unit: item.type === 'medicine' ? '/克' : '/件',
      totalPrice: item.price.toFixed(2)
    };
    
    // 检查是否已存在
    var existingIndex = -1;
    for (var i = 0; i < cartData.length; i++) {
      if (cartData[i].productId === item.id) {
        existingIndex = i;
        break;
      }
    }
    
    if (existingIndex >= 0) {
      // 增加数量
      cartData[existingIndex].quantity += 1;
      cartData[existingIndex].totalPrice = (cartData[existingIndex].price * cartData[existingIndex].quantity).toFixed(2);
    } else {
      // 添加新商品
      cartData.push(cartItem);
    }
    
    // 保存到本地存储
    wx.setStorageSync(cartKey, cartData);
    
    // 更新页面数据
    this.loadCartItems();
    
    // 更新购物车徽章
    if (app.updateCartBadge) {
      app.updateCartBadge();
    }
    
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success'
    });
    
    console.log('添加商品到购物车:', cartItem);
  },

  // 清空购物车
  clearCart: function() {
    var that = this;
    wx.showModal({
      title: '确认清空',
      content: '确定要清空购物车吗？',
      success: function(res) {
        if (res.confirm) {
          var userInfo = app.globalData.userInfo;
          if (userInfo) {
            var cartKey = 'cart_' + userInfo.id;
            wx.removeStorageSync(cartKey);
            that.loadCartItems();
            
            // 更新购物车徽章
            if (app.updateCartBadge) {
              app.updateCartBadge();
            }
            
            wx.showToast({
              title: '购物车已清空',
              icon: 'success'
            });
          }
        }
      }
    });
  },

  // 查看购物车
  goToCart: function() {
    wx.switchTab({
      url: '/pages/cart/cart'
    });
  },

  // 清除用户信息
  clearUser: function() {
    app.clearUserInfo();
    this.loadUserInfo();
    this.loadCartItems();
    
    wx.showToast({
      title: '用户信息已清除',
      icon: 'success'
    });
  },

  // 查看存储数据
  viewStorage: function() {
    var userInfo = app.globalData.userInfo;
    var storageInfo = {
      userInfo: userInfo,
      cartData: userInfo ? wx.getStorageSync('cart_' + userInfo.id) : null,
      allKeys: wx.getStorageInfoSync().keys
    };
    
    console.log('存储数据详情:', storageInfo);
    
    wx.showModal({
      title: '存储数据',
      content: JSON.stringify(storageInfo, null, 2),
      showCancel: false
    });
  }
});

/* 购物车测试页面样式 */
.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
}

.section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #8B4513;
  padding-left: 15rpx;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.user-avatar {
  font-size: 60rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  border-radius: 50%;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.user-id, .user-role {
  font-size: 20rpx;
  color: #666;
}

.user-guest {
  font-size: 18rpx;
  color: #E74C3C;
  background: rgba(231, 76, 60, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  width: fit-content;
}

.no-user {
  text-align: center;
  padding: 40rpx;
}

.no-user-text {
  font-size: 24rpx;
  color: #999;
}

/* 测试商品样式 */
.product-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 15rpx;
}

.product-icon {
  font-size: 50rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border-radius: 15rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.product-name {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.product-price {
  font-size: 22rpx;
  color: #E74C3C;
  font-weight: bold;
}

.product-category {
  font-size: 18rpx;
  color: #666;
}

.add-btn {
  font-size: 20rpx;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
  border: none;
  border-radius: 20rpx;
  min-width: 120rpx;
}

.add-btn:active {
  background: linear-gradient(135deg, #A0522D, #8B4513);
}

/* 购物车状态样式 */
.cart-status {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.cart-count {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
}

.cart-items {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.cart-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx;
  background: #f0f8f0;
  border-radius: 10rpx;
  border-left: 4rpx solid #4CAF50;
}

.item-icon {
  font-size: 30rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.item-name {
  font-size: 20rpx;
  color: #333;
}

.item-price {
  font-size: 18rpx;
  color: #4CAF50;
  font-weight: bold;
}

.empty-cart {
  font-size: 20rpx;
  color: #999;
  text-align: center;
  padding: 20rpx;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.test-btn {
  font-size: 22rpx;
  padding: 15rpx 25rpx;
  border: 1rpx solid #8B4513;
  color: #8B4513;
  background: white;
  border-radius: 25rpx;
  text-align: center;
}

.test-btn:active {
  background: rgba(139, 69, 19, 0.1);
}

.test-btn.primary {
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
  border-color: #8B4513;
}

.test-btn.primary:active {
  background: linear-gradient(135deg, #A0522D, #8B4513);
}

.test-btn.danger {
  border-color: #E74C3C;
  color: #E74C3C;
}

.test-btn.danger:active {
  background: rgba(231, 76, 60, 0.1);
}

/* 说明样式 */
.instructions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.instruction {
  font-size: 20rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 20rpx;
  position: relative;
}

.instruction::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #8B4513;
  font-weight: bold;
}

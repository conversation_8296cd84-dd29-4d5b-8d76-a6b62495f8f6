<!--购物车测试页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">🛒 购物车功能测试</text>
  </view>

  <!-- 用户信息 -->
  <view class="section">
    <view class="section-title">用户信息</view>
    <view class="user-info" wx:if="{{userInfo}}">
      <text class="user-avatar">{{userInfo.avatar}}</text>
      <view class="user-details">
        <text class="user-name">{{userInfo.name}}</text>
        <text class="user-id">ID: {{userInfo.id}}</text>
        <text class="user-role">角色: {{userInfo.role}}</text>
        <text class="user-guest" wx:if="{{userInfo.isGuest}}">游客模式</text>
      </view>
    </view>
    <view class="no-user" wx:else>
      <text class="no-user-text">暂无用户信息</text>
    </view>
  </view>

  <!-- 测试商品 -->
  <view class="section">
    <view class="section-title">测试商品</view>
    <view class="product-list">
      <view class="product-item" wx:for="{{testProducts}}" wx:key="id">
        <view class="product-icon">{{item.imageUrl}}</view>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-price">¥{{item.price}}</text>
          <text class="product-category">{{item.category}}</text>
        </view>
        <button class="add-btn" bindtap="addTestProduct" data-item="{{item}}">
          加入购物车
        </button>
      </view>
    </view>
  </view>

  <!-- 购物车状态 -->
  <view class="section">
    <view class="section-title">购物车状态</view>
    <view class="cart-status">
      <text class="cart-count">商品数量: {{cartItems.length}}</text>
      <view class="cart-items" wx:if="{{cartItems.length > 0}}">
        <view class="cart-item" wx:for="{{cartItems}}" wx:key="id">
          <text class="item-icon">{{item.imageUrl}}</text>
          <view class="item-info">
            <text class="item-name">{{item.name}}</text>
            <text class="item-price">¥{{item.price}} × {{item.quantity}}</text>
          </view>
        </view>
      </view>
      <text class="empty-cart" wx:else>购物车为空</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="section">
    <view class="section-title">操作</view>
    <view class="button-group">
      <button class="test-btn primary" bindtap="createGuestUser">创建游客账号</button>
      <button class="test-btn" bindtap="goToCart">查看购物车页面</button>
      <button class="test-btn" bindtap="viewStorage">查看存储数据</button>
      <button class="test-btn danger" bindtap="clearCart">清空购物车</button>
      <button class="test-btn danger" bindtap="clearUser">清除用户信息</button>
    </view>
  </view>

  <!-- 说明 -->
  <view class="section">
    <view class="section-title">使用说明</view>
    <view class="instructions">
      <text class="instruction">1. 点击"创建游客账号"创建测试用户</text>
      <text class="instruction">2. 点击"加入购物车"添加测试商品</text>
      <text class="instruction">3. 点击"查看购物车页面"跳转到购物车</text>
      <text class="instruction">4. 观察购物车图标的数量徽章变化</text>
      <text class="instruction">5. 使用"查看存储数据"检查数据保存情况</text>
    </view>
  </view>
</view>

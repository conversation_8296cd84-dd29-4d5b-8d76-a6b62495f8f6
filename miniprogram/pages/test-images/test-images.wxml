<!--图片测试页面-->
<view class="container">
  <view class="header">
    <text class="title">图片加载测试</text>
    <text class="subtitle">验证中药材和文创产品图片是否正常加载</text>
  </view>

  <view class="test-results">
    <view class="result-item" wx:for="{{testResults}}" wx:key="index">
      <view class="image-container">
        <image 
          class="test-image" 
          src="{{item.path}}" 
          mode="aspectFill"
          bindload="onImageLoad"
          binderror="onImageError"
          data-index="{{index}}"
        />
        <view class="placeholder" wx:if="{{item.status === 'error'}}">
          <text class="placeholder-icon">{{item.category === '中药材' ? '🌿' : '🎨'}}</text>
        </view>
      </view>
      <view class="item-info">
        <text class="item-name">{{item.name}}</text>
        <text class="item-category">{{item.category}}</text>
        <text class="item-path">{{item.path}}</text>
        <text class="item-status status-{{item.status}}">{{item.message}}</text>
      </view>
    </view>
  </view>

  <view class="actions">
    <button class="action-btn" bindtap="goToMedicines">查看中药材页面</button>
    <button class="action-btn" bindtap="goToProducts">查看文创产品页面</button>
  </view>
</view>

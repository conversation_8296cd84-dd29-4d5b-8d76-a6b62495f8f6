/* 图片测试页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2c5530;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.test-results {
  margin-bottom: 30rpx;
}

.result-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.image-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #f0f0f0;
}

.test-image {
  width: 100%;
  height: 100%;
}

.placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
}

.placeholder-icon {
  font-size: 60rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.item-category {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.item-path {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
  word-break: break-all;
}

.item-status {
  font-size: 26rpx;
  font-weight: bold;
}

.status-success {
  color: #4caf50;
}

.status-error {
  color: #f44336;
}

.status-testing {
  color: #ff9800;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  background-color: #2c5530;
  color: #fff;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 32rpx;
}

.action-btn:active {
  background-color: #1e3a21;
}

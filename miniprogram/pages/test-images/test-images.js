// 图片测试页面
Page({
  data: {
    medicineImages: [
      { name: '人参', path: '/images/中药材/人参.jpg' },
      { name: '黄芪', path: '/images/中药材/黄芪.jpg' },
      { name: '甘草', path: '/images/中药材/甘草.jpg' },
      { name: '白术', path: '/images/中药材/白术.jpg' },
      { name: '党参', path: '/images/中药材/党参.jpg' },
      { name: '当归', path: '/images/中药材/当归.jpg' },
      { name: '枸杞子', path: '/images/中药材/枸杞子.jpg' },
      { name: '熟地黄', path: '/images/中药材/熟地黄.jpg' },
      { name: '白芍', path: '/images/中药材/白芍.jpg' },
      { name: '川芎', path: '/images/中药材/川芎.jpg' }
    ],
    productImages: [
      { name: '中医养生茶具套装', path: '/images/文创/中医养生茶具套装.jpg' },
      { name: '本草纲目典藏版', path: '/images/文创/本草纲目典藏版.jpg' },
      { name: '艾灸养生套装', path: '/images/文创/艾灸养生套装.jpg' },
      { name: '中药香薰炉', path: '/images/文创/中药香薰炉.jpg' },
      { name: '养生香囊', path: '/images/文创/养生香囊.jpg' },
      { name: '养生书籍', path: '/images/文创/养生书籍.jpg' },
      { name: '中医经络图', path: '/images/文创/中医经络图.jpg' },
      { name: '中药材标本', path: '/images/文创/中药材标本.jpg' }
    ],
    testResults: []
  },

  onLoad: function() {
    console.log('图片测试页面加载');
    this.testAllImages();
  },

  testAllImages: function() {
    var that = this;
    var allImages = [
      ...this.data.medicineImages.map(item => ({...item, category: '中药材'})),
      ...this.data.productImages.map(item => ({...item, category: '文创产品'}))
    ];

    var results = allImages.map(function(item) {
      return {
        name: item.name,
        path: item.path,
        category: item.category,
        status: 'testing',
        message: '测试中...'
      };
    });

    this.setData({
      testResults: results
    });
  },

  onImageLoad: function(e) {
    var index = e.currentTarget.dataset.index;
    var testResults = this.data.testResults;
    
    if (testResults[index]) {
      testResults[index].status = 'success';
      testResults[index].message = '✅ 加载成功';
      this.setData({ testResults: testResults });
    }
  },

  onImageError: function(e) {
    var index = e.currentTarget.dataset.index;
    var testResults = this.data.testResults;
    
    if (testResults[index]) {
      testResults[index].status = 'error';
      testResults[index].message = '❌ 加载失败';
      this.setData({ testResults: testResults });
    }
  },

  goToMedicines: function() {
    wx.navigateTo({
      url: '/pages/medicines/medicines'
    });
  },

  goToProducts: function() {
    wx.navigateTo({
      url: '/pages/products/products'
    });
  },

  // 测试详情页跳转
  testDetailNavigation: function() {
    var that = this;

    // 测试中药材详情页跳转
    wx.showModal({
      title: '测试详情页跳转',
      content: '选择要测试的功能',
      confirmText: '中药材详情',
      cancelText: '文创产品详情',
      success: function(res) {
        if (res.confirm) {
          // 测试中药材详情页
          wx.navigateTo({
            url: '/pages/medicine-detail/medicine-detail?id=1'
          });
        } else if (res.cancel) {
          // 测试文创产品详情页
          wx.navigateTo({
            url: '/pages/product-detail/product-detail?id=1'
          });
        }
      }
    });
  },

  // 清除图片缓存并重新测试
  clearCacheAndRetest: function() {
    var that = this;

    wx.showModal({
      title: '清除缓存',
      content: '是否清除图片缓存并重新测试？',
      success: function(res) {
        if (res.confirm) {
          // 重新设置测试结果
          that.testAllImages();
          wx.showToast({
            title: '已重新测试',
            icon: 'success'
          });
        }
      }
    });
  }
});

<!--articles.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <input class="search-input" placeholder="搜索养生文章标题、内容..." value="{{searchKeyword}}" bindinput="onSearchInput" />
      <button class="search-btn btn btn-primary" bindtap="searchArticles">搜索</button>
    </view>
    <view class="filter-bar">
      <scroll-view class="category-scroll" scroll-x="true">
        <view class="category-item {{selectedCategory === '' ? 'active' : ''}}" bindtap="selectCategory" data-category="">
          全部
        </view>
        <view class="category-item {{selectedCategory === item ? 'active' : ''}}" 
              wx:for="{{categories}}" wx:key="*this" 
              bindtap="selectCategory" data-category="{{item}}">
          {{item}}
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 管理员操作栏 -->
  <view class="admin-actions" wx:if="{{isAdmin}}">
    <button class="btn btn-primary" bindtap="addArticle">添加文章</button>
    <button class="btn btn-secondary" bindtap="batchManage">批量管理</button>
  </view>

  <!-- 文章列表 -->
  <view class="article-list">
    <view class="article-item list-item" wx:for="{{articleList}}" wx:key="id" bindtap="viewArticle" data-id="{{item.id}}">
      <view class="article-image-container">
        <image class="article-image" src="{{item.imageUrl}}" mode="aspectFill" wx:if="{{item.imageUrl && !item.imageError}}" binderror="onImageError" data-index="{{index}}"></image>
        <view class="article-placeholder" wx:if="{{!item.imageUrl || item.imageError}}">
          <text class="placeholder-icon">📖</text>
        </view>
      </view>
      <view class="article-content">
        <view class="article-header">
          <text class="article-title">{{item.title}}</text>
          <view class="article-status" wx:if="{{isAdmin}}">
            <text class="status-text {{item.status === 'published' ? 'published' : 'draft'}}">
              {{item.status === 'published' ? '已发布' : '草稿'}}
            </text>
          </view>
        </view>
        <text class="article-category">{{item.category}}</text>
        <text class="article-summary">{{item.summary}}</text>
        <view class="article-meta">
          <text class="article-author">作者：{{item.author}}</text>
          <text class="article-date">{{item.createTime}}</text>
          <text class="article-views">阅读{{item.views}}</text>
        </view>
      </view>
      <view class="article-actions" wx:if="{{isAdmin}}">
        <button class="action-btn edit-btn" bindtap="editArticle" data-id="{{item.id}}" catchtap="stopPropagation">编辑</button>
        <button class="action-btn delete-btn" bindtap="deleteArticle" data-id="{{item.id}}" catchtap="stopPropagation">删除</button>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <button class="btn btn-outline" bindtap="loadMore" disabled="{{loading}}">
      {{loading ? '加载中...' : '加载更多'}}
    </button>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{articleList.length === 0 && !loading}}">
    <image class="empty-image" src="/images/empty-article.png" mode="aspectFit"></image>
    <text class="empty-text">暂无养生文章</text>
    <button class="btn btn-primary" bindtap="addArticle" wx:if="{{isAdmin}}">添加第一篇文章</button>
  </view>

  <!-- 调试工具 -->
  <view class="debug-tools" wx:if="{{isAdmin}}">
    <button class="btn btn-outline debug-btn" bindtap="testImages">🔧 图片测试</button>
  </view>
</view>

<!-- 添加/编辑文章弹窗 -->
<view class="modal-overlay" wx:if="{{showEditModal}}" bindtap="hideEditModal">
  <view class="modal-content edit-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{editMode === 'add' ? '添加' : '编辑'}}养生文章</text>
      <text class="modal-close" bindtap="hideEditModal">×</text>
    </view>
    <scroll-view class="modal-body" scroll-y="true">
      <view class="form-group">
        <text class="form-label">文章标题</text>
        <input class="form-input" placeholder="请输入文章标题" value="{{editData.title}}" bindinput="onEditInput" data-field="title" />
      </view>
      
      <view class="form-group">
        <text class="form-label">文章分类</text>
        <picker class="form-picker" range="{{categories}}" value="{{editData.categoryIndex}}" bindchange="onCategoryChange">
          <view class="picker-text">{{editData.category || '请选择分类'}}</view>
        </picker>
      </view>
      
      <view class="form-group">
        <text class="form-label">作者</text>
        <input class="form-input" placeholder="请输入作者姓名" value="{{editData.author}}" bindinput="onEditInput" data-field="author" />
      </view>
      
      <view class="form-group">
        <text class="form-label">文章摘要</text>
        <textarea class="form-textarea" placeholder="请输入文章摘要" value="{{editData.summary}}" bindinput="onEditInput" data-field="summary"></textarea>
      </view>
      
      <view class="form-group">
        <text class="form-label">文章内容</text>
        <textarea class="form-textarea content-textarea" placeholder="请输入文章内容" value="{{editData.content}}" bindinput="onEditInput" data-field="content"></textarea>
      </view>
      
      <view class="form-group">
        <text class="form-label">发布状态</text>
        <radio-group bindchange="onStatusChange">
          <label class="radio-item">
            <radio value="draft" checked="{{editData.status === 'draft'}}" />
            <text class="radio-text">保存为草稿</text>
          </label>
          <label class="radio-item">
            <radio value="published" checked="{{editData.status === 'published'}}" />
            <text class="radio-text">立即发布</text>
          </label>
        </radio-group>
      </view>
      
      <view class="form-group">
        <text class="form-label">文章封面</text>
        <view class="image-upload">
          <image class="upload-preview" src="{{editData.imageUrl}}" mode="aspectFill" wx:if="{{editData.imageUrl}}"></image>
          <button class="upload-btn btn btn-outline" bindtap="uploadImage">
            {{editData.imageUrl ? '更换封面' : '上传封面'}}
          </button>
        </view>
      </view>
    </scroll-view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="hideEditModal">取消</button>
      <button class="btn btn-primary" bindtap="saveArticle" disabled="{{!canSave}}">保存</button>
    </view>
  </view>
</view>

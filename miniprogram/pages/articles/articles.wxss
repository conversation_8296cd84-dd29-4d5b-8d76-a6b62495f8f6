/**articles.wxss**/

/* 搜索区域 */
.search-section {
  margin-bottom: 30rpx;
}

.search-bar {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  font-size: 28rpx;
  background-color: white;
}

.search-btn {
  padding: 20rpx 30rpx;
  font-size: 26rpx;
}

.filter-bar {
  background-color: white;
  border-radius: 15rpx;
  padding: 20rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-item {
  display: inline-block;
  padding: 15rpx 25rpx;
  margin-right: 15rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background-color: #F5F2E8;
  color: #666;
}

.category-item.active {
  background-color: #8B4513;
  color: white;
}

/* 管理员操作 */
.admin-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.admin-actions .btn {
  flex: 1;
  padding: 25rpx;
  font-size: 26rpx;
}

/* 文章列表 */
.article-list {
  margin-bottom: 30rpx;
}

.article-item {
  display: flex;
  align-items: flex-start;
  padding: 25rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.article-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 15rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.article-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.article-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10rpx;
}

.article-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  flex: 1;
  margin-right: 15rpx;
}

.article-status {
  flex-shrink: 0;
}

.status-text {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  color: white;
}

.status-text.published {
  background-color: #228B22;
}

.status-text.draft {
  background-color: #999;
}

.article-category {
  font-size: 22rpx;
  color: #228B22;
  background-color: #F0F8F0;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  align-self: flex-start;
  margin-bottom: 10rpx;
}

.article-summary {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20rpx;
  color: #999;
}

.article-author {
  margin-right: 20rpx;
}

.article-date {
  margin-right: 20rpx;
}

.article-views {
  margin-left: auto;
}

/* 管理员操作按钮 */
.article-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-left: 20rpx;
  flex-shrink: 0;
}

.action-btn {
  padding: 12rpx 20rpx;
  font-size: 22rpx;
  border-radius: 8rpx;
  border: none;
  color: white;
}

.edit-btn {
  background-color: #3498DB;
}

.delete-btn {
  background-color: #E74C3C;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-bottom: 30rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 50rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 30rpx;
}

/* 编辑弹窗 */
.edit-modal {
  width: 90%;
  max-width: 700rpx;
  max-height: 80vh;
}

.modal-body {
  max-height: 60vh;
  padding: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  font-size: 26rpx;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #8B4513;
}

.form-picker {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  background-color: white;
}

.picker-text {
  font-size: 26rpx;
  color: #333;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  font-size: 26rpx;
  box-sizing: border-box;
}

.content-textarea {
  min-height: 200rpx;
}

.form-textarea:focus {
  border-color: #8B4513;
}

/* 单选框 */
.radio-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.radio-text {
  font-size: 26rpx;
  color: #333;
  margin-left: 15rpx;
}

/* 图片上传 */
.image-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.upload-preview {
  width: 200rpx;
  height: 150rpx;
  border-radius: 15rpx;
}

.upload-btn {
  padding: 20rpx 40rpx;
  font-size: 24rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .search-bar {
    flex-direction: column;
  }
  
  .admin-actions {
    flex-direction: column;
  }
  
  .article-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .article-image {
    width: 100%;
    height: 200rpx;
    margin-bottom: 15rpx;
    margin-right: 0;
  }
  
  .article-actions {
    flex-direction: row;
    margin-left: 0;
    margin-top: 15rpx;
    width: 100%;
  }
  
  .action-btn {
    flex: 1;
  }
  
  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 5rpx;
  }
}

/* 图片占位符 */
.article-image-container {
  position: relative;
  width: 200rpx;
  height: 150rpx;
  border-radius: 15rpx;
  overflow: hidden;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.article-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #F0F8FF, #E6F3FF);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx dashed #B0C4DE;
}

.article-placeholder .placeholder-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

/* 调试工具 */
.debug-tools {
  padding: 40rpx;
  text-align: center;
  border-top: 1px solid #E5E5E5;
  margin-top: 40rpx;
  background: #F8F5F0;
}

.debug-btn {
  font-size: 28rpx;
  padding: 16rpx 32rpx;
  border-radius: 20rpx;
  background: #FFF;
  border: 2rpx solid #4682B4;
  color: #4682B4;
}

.debug-btn:active {
  background: #4682B4;
  color: #FFF;
}

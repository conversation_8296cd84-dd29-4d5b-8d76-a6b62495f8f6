// articles.js
const app = getApp();

Page({
  data: {
    isAdmin: false,
    searchKeyword: '',
    selectedCategory: '',
    categories: ['春季养生', '夏季养生', '秋季养生', '冬季养生', '食疗养生', '运动养生', '中医理论', '药膳食谱'],
    articleList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 编辑相关
    showEditModal: false,
    editMode: 'add', // add 或 edit
    editData: {},
    canSave: false
  },

  onLoad(options) {
    this.setData({
      isAdmin: app.globalData.isAdmin
    });
    
    // 检查是否是管理模式
    if (options.mode === 'admin' || options.mode === 'add') {
      if (!app.globalData.isAdmin) {
        wx.showModal({
          title: '权限不足',
          content: '您没有管理员权限',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }
      
      if (options.mode === 'add') {
        this.addArticle();
      }
    }
    
    this.loadArticles();
  },

  onShow() {
    this.setData({
      isAdmin: app.globalData.isAdmin
    });
  },

  // 加载养生文章数据
  async loadArticles(refresh = true) {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    if (refresh) {
      this.setData({ 
        page: 1, 
        articleList: [],
        hasMore: true 
      });
    }

    try {
      const db = wx.cloud.database();
      const { page, pageSize, searchKeyword, selectedCategory } = this.data;
      
      let query = db.collection('articles');
      
      // 非管理员只能看到已发布的文章
      if (!this.data.isAdmin) {
        query = query.where({
          status: 'published'
        });
      }
      
      // 添加搜索条件
      if (searchKeyword) {
        query = query.where({
          title: db.RegExp({
            regexp: searchKeyword,
            options: 'i'
          })
        });
      }
      
      // 添加分类筛选
      if (selectedCategory) {
        query = query.where({
          category: selectedCategory
        });
      }
      
      const result = await query
        .orderBy('createTime', 'desc')
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .get();

      const newList = refresh ? result.data : [...this.data.articleList, ...result.data];
      
      this.setData({
        articleList: newList,
        hasMore: result.data.length === pageSize,
        page: page + 1,
        loading: false
      });
      
    } catch (error) {
      console.error('加载养生文章数据失败:', error);
      this.setData({ loading: false });
      
      // 设置默认数据
      if (refresh) {
        this.setDefaultData();
      }
    }
  },

  // 设置默认数据
  setDefaultData() {
    const defaultArticles = [
      {
        id: '1',
        title: '春季养生：顺应自然，调养身心',
        category: '春季养生',
        author: '张医师',
        summary: '春季是万物复苏的季节，人体阳气开始升发，此时养生应顺应自然规律，注重调养肝脏，保持心情舒畅...',
        content: '春季养生的详细内容...',
        imageUrl: '/images/养生文章/文章-春季养生.jpg',
        status: 'published',
        views: 1250,
        createTime: '2024-03-15',
        updateTime: new Date()
      },
      {
        id: '2',
        title: '中医食疗：药食同源的智慧',
        category: '食疗养生',
        author: '李医师',
        summary: '中医认为"药食同源"，许多食物既是美味佳肴，又具有药用价值。合理搭配食材，可以达到养生保健的效果...',
        content: '中医食疗的详细内容...',
        imageUrl: '/images/养生文章/文章-食疗智慧.jpg',
        status: 'published',
        views: 980,
        createTime: '2024-03-12',
        updateTime: new Date()
      },
      {
        id: '3',
        title: '四季养生茶饮推荐',
        category: '药膳食谱',
        author: '王医师',
        summary: '不同季节适合饮用不同的养生茶，春饮花茶，夏饮绿茶，秋饮乌龙，冬饮红茶，每种茶都有其独特的养生功效...',
        content: '四季养生茶饮的详细内容...',
        imageUrl: '/images/养生文章/文章-茶饮推荐.jpg',
        status: 'published',
        views: 756,
        createTime: '2024-03-10',
        updateTime: new Date()
      }
    ];
    
    this.setData({
      articleList: defaultArticles,
      hasMore: false,
      loading: false
    });
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索文章
  searchArticles() {
    this.loadArticles(true);
  },

  // 选择分类
  selectCategory(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      selectedCategory: category
    });
    this.loadArticles(true);
  },

  // 加载更多
  loadMore() {
    this.loadArticles(false);
  },

  // 查看文章详情
  viewArticle(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/article-detail/article-detail?id=${id}`
    });
  },

  // 图片加载错误处理
  onImageError: function(e) {
    var index = e.currentTarget.dataset.index;
    var articleList = this.data.articleList;
    if (articleList[index]) {
      articleList[index].imageError = true;
      this.setData({
        articleList: articleList
      });
      console.log('养生文章图片加载失败，使用备用图标:', articleList[index].title);
    }
  },

  // 图片测试
  testImages: function() {
    wx.navigateTo({
      url: '/pages/image-test/image-test'
    });
  },

  // 添加文章
  addArticle() {
    this.setData({
      showEditModal: true,
      editMode: 'add',
      editData: {
        title: '',
        category: '',
        categoryIndex: 0,
        author: app.globalData.userInfo?.nickName || '',
        summary: '',
        content: '',
        status: 'draft',
        imageUrl: ''
      },
      canSave: false
    });
  },

  // 编辑文章
  editArticle(e) {
    const id = e.currentTarget.dataset.id;
    const article = this.data.articleList.find(item => item.id === id);
    
    if (article) {
      const categoryIndex = this.data.categories.indexOf(article.category);
      
      this.setData({
        showEditModal: true,
        editMode: 'edit',
        editData: {
          ...article,
          categoryIndex: categoryIndex >= 0 ? categoryIndex : 0
        }
      });
      this.checkCanSave();
    }
  },

  // 删除文章
  deleteArticle(e) {
    const id = e.currentTarget.dataset.id;
    const article = this.data.articleList.find(item => item.id === id);
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${article.title}"吗？此操作不可恢复。`,
      success: async (res) => {
        if (res.confirm) {
          try {
            app.showLoading('删除中...');
            
            // 这里应该调用云函数删除数据
            await wx.cloud.callFunction({
              name: 'quickstartFunctions',
              data: {
                type: 'deleteArticle',
                id: id
              }
            });
            
            app.showSuccess('删除成功');
            this.loadArticles(true);
            
          } catch (error) {
            console.error('删除失败:', error);
            app.showError('删除失败');
          }
          
          app.hideLoading();
        }
      }
    });
  },

  // 批量管理
  batchManage() {
    wx.showActionSheet({
      itemList: ['批量导入', '批量导出', '批量发布', '批量删除'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.batchImport();
            break;
          case 1:
            this.batchExport();
            break;
          case 2:
            this.batchPublish();
            break;
          case 3:
            this.batchDelete();
            break;
        }
      }
    });
  },

  // 批量导入
  batchImport() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      success: (res) => {
        app.showLoading('导入中...');
        
        setTimeout(() => {
          app.hideLoading();
          app.showSuccess('导入完成');
          this.loadArticles(true);
        }, 2000);
      }
    });
  },

  // 批量导出
  batchExport() {
    app.showLoading('导出中...');
    
    setTimeout(() => {
      app.hideLoading();
      app.showSuccess('导出完成');
    }, 1500);
  },

  // 批量发布
  batchPublish() {
    wx.showModal({
      title: '批量发布',
      content: '批量发布功能正在开发中...',
      showCancel: false
    });
  },

  // 批量删除
  batchDelete() {
    wx.showModal({
      title: '批量删除',
      content: '批量删除功能正在开发中...',
      showCancel: false
    });
  },

  // 隐藏编辑弹窗
  hideEditModal() {
    this.setData({
      showEditModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击模态框内容时关闭弹窗
  },

  // 编辑输入
  onEditInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`editData.${field}`]: value
    });
    
    this.checkCanSave();
  },

  // 分类选择
  onCategoryChange(e) {
    const index = e.detail.value;
    const category = this.data.categories[index];
    
    this.setData({
      'editData.category': category,
      'editData.categoryIndex': index
    });
    
    this.checkCanSave();
  },

  // 状态选择
  onStatusChange(e) {
    this.setData({
      'editData.status': e.detail.value
    });
  },

  // 检查是否可以保存
  checkCanSave() {
    const { title, category, author, summary, content } = this.data.editData;
    const canSave = title && category && author && summary && content;
    
    this.setData({ canSave });
  },

  // 上传图片
  uploadImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        try {
          app.showLoading('上传中...');
          
          // 上传到云存储
          const cloudPath = `articles/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.jpg`;
          const uploadResult = await wx.cloud.uploadFile({
            cloudPath: cloudPath,
            filePath: tempFilePath
          });
          
          this.setData({
            'editData.imageUrl': uploadResult.fileID
          });
          
          app.hideLoading();
          app.showSuccess('上传成功');
          
        } catch (error) {
          console.error('上传失败:', error);
          app.hideLoading();
          app.showError('上传失败');
        }
      }
    });
  },

  // 保存文章
  async saveArticle() {
    if (!this.data.canSave) return;
    
    const { editMode, editData } = this.data;
    
    try {
      app.showLoading(editMode === 'add' ? '添加中...' : '保存中...');
      
      // 准备数据
      const articleData = {
        ...editData,
        updateTime: new Date()
      };
      
      if (editMode === 'add') {
        articleData.id = Date.now().toString();
        articleData.createTime = this.formatDate(new Date());
        articleData.views = 0;
      }
      
      // 调用云函数保存数据
      await wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: editMode === 'add' ? 'addArticle' : 'updateArticle',
          data: articleData
        }
      });
      
      app.showSuccess(editMode === 'add' ? '添加成功' : '保存成功');
      this.hideEditModal();
      this.loadArticles(true);
      
    } catch (error) {
      console.error('保存失败:', error);
      app.showError('保存失败');
    }
    
    app.hideLoading();
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
});

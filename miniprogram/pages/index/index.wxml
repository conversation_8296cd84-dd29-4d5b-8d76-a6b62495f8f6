<!--慧心制药首页-->
<view class="container">
  <!-- 顶部导航 -->
  <view class="header">
    <view class="header-left">
      <text class="app-logo">🌿</text>
      <text class="app-name">慧心智药</text>
    </view>
    <view class="header-right">
      <view class="weather" wx:if="{{weather}}">
        <text class="weather-text">{{weather.city}} {{weather.temperature}}°</text>
      </view>
      <view class="user-avatar" bindtap="goToProfile">
        <text class="avatar-text">👤</text>
      </view>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="4000" duration="800">
      <swiper-item wx:for="{{bannerList}}" wx:key="id" bindtap="onBannerTap" data-item="{{item}}">
        <view class="banner-item">
          <image class="banner-image" src="{{item.imageUrl}}" mode="aspectFill" wx:if="{{item.imageUrl}}" binderror="onBannerImageError" data-index="{{index}}"></image>
          <view class="banner-bg" wx:if="{{!item.imageUrl || item.imageError}}">
            <text class="banner-icon">{{item.icon || '🌿'}}</text>
          </view>
          <view class="banner-content">
            <text class="banner-title">{{item.title}}</text>
            <text class="banner-desc">{{item.description}}</text>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 功能导航 -->
  <view class="nav-section">
    <view class="section-title">功能导航</view>
    <view class="nav-container">
      <view class="nav-grid-3x2">
        <!-- 第一行 -->
        <view class="nav-item" bindtap="goToMedicines">
          <view class="nav-icon medicine">
            <text class="icon">🌿</text>
          </view>
          <text class="nav-label">中药材</text>
          <text class="nav-count">{{stats.medicineCount}}+</text>
        </view>

        <view class="nav-item" bindtap="goToProducts">
          <view class="nav-icon product">
            <text class="icon">🎋</text>
          </view>
          <text class="nav-label">文创产品</text>
          <text class="nav-count">{{stats.productCount}}+</text>
        </view>

        <view class="nav-item" bindtap="goToEncyclopedia">
          <view class="nav-icon encyclopedia">
            <text class="icon">📖</text>
          </view>
          <text class="nav-label">中药图鉴</text>
          <text class="nav-count">500+</text>
        </view>

        <!-- 第二行 -->
        <view class="nav-item" bindtap="goToStoreMap">
          <view class="nav-icon map">
            <text class="icon">🗺️</text>
          </view>
          <text class="nav-label">门店地图</text>
          <text class="nav-count">{{nearbyStores.length}}家</text>
        </view>

        <view class="nav-item" bindtap="goToArticles">
          <view class="nav-icon article">
            <text class="icon">📚</text>
          </view>
          <text class="nav-label">养生文章</text>
          <text class="nav-count">{{stats.articleCount}}+</text>
        </view>

        <view class="nav-item" bindtap="goToConsultation">
          <view class="nav-icon consultation">
            <text class="icon">👨‍⚕️</text>
          </view>
          <text class="nav-label">在线咨询</text>
          <text class="nav-count">{{onlineDoctors.length}}位</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据统计（仅管理员可见） -->
  <view class="stats-section" wx:if="{{isAdmin}}">
    <view class="section-title">数据概览</view>
    <view class="admin-badge">
      <text class="badge-icon">👑</text>
      <text class="badge-text">管理员专属</text>
    </view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-icon">🌿</text>
        <text class="stat-number">{{stats.medicineCount}}</text>
        <text class="stat-label">中药材</text>
        <view class="stat-trend positive" wx:if="{{stats.medicineGrowth > 0}}">
          <text class="trend-icon">📈</text>
          <text class="trend-text">+{{stats.medicineGrowth}}%</text>
        </view>
      </view>
      <view class="stat-item">
        <text class="stat-icon">🎋</text>
        <text class="stat-number">{{stats.productCount}}</text>
        <text class="stat-label">文创产品</text>
        <view class="stat-trend positive" wx:if="{{stats.productGrowth > 0}}">
          <text class="trend-icon">📈</text>
          <text class="trend-text">+{{stats.productGrowth}}%</text>
        </view>
      </view>
      <view class="stat-item">
        <text class="stat-icon">📚</text>
        <text class="stat-number">{{stats.articleCount}}</text>
        <text class="stat-label">养生文章</text>
        <view class="stat-trend positive" wx:if="{{stats.articleGrowth > 0}}">
          <text class="trend-icon">📈</text>
          <text class="trend-text">+{{stats.articleGrowth}}%</text>
        </view>
      </view>
      <view class="stat-item">
        <text class="stat-icon">👥</text>
        <text class="stat-number">{{stats.userCount}}</text>
        <text class="stat-label">注册用户</text>
        <view class="stat-trend positive" wx:if="{{stats.userGrowth > 0}}">
          <text class="trend-icon">📈</text>
          <text class="trend-text">+{{stats.userGrowth}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 今日推荐 -->
  <view class="recommend-section">
    <view class="section-title">今日推荐</view>
    <view class="recommend-banner" bindtap="goToTodayRecommend">
      <view class="recommend-bg">
        <text class="recommend-icon">🌸</text>
      </view>
      <view class="recommend-content">
        <text class="recommend-title">{{todayRecommend.title}}</text>
        <text class="recommend-desc">{{todayRecommend.description}}</text>
        <view class="recommend-tag">
          <text class="tag-text">今日特惠</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 热门中药材 -->
  <view class="medicine-section">
    <view class="section-header">
      <text class="section-title">热门中药材</text>
      <navigator url="/pages/medicines/medicines" class="more-btn">
        <text class="more-text">查看全部</text>
        <text class="more-arrow">→</text>
      </navigator>
    </view>
    <scroll-view class="medicine-scroll" scroll-x="true" show-scrollbar="false">
      <view class="medicine-item" wx:for="{{recommendMedicines}}" wx:key="id" bindtap="goToMedicineDetail" data-id="{{item.id}}">
        <view class="medicine-card">
          <view class="medicine-image">
            <image class="medicine-img" src="{{item.imageUrl}}" mode="aspectFill" wx:if="{{item.imageUrl}}" binderror="onMedicineImageError" data-index="{{index}}"></image>
            <text class="medicine-icon" wx:if="{{!item.imageUrl || item.imageError}}">🌿</text>
          </view>
          <view class="medicine-info">
            <text class="medicine-name">{{item.name}}</text>
            <text class="medicine-category">{{item.category}}</text>
            <view class="medicine-price">
              <text class="price">¥{{item.price}}</text>
              <text class="unit">/克</text>
            </view>
            <view class="medicine-tags">
              <text class="tag hot" wx:if="{{item.isHot}}">热销</text>
              <text class="tag quality" wx:if="{{item.isQuality}}">道地</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 精美文创 -->
  <view class="product-section">
    <view class="section-header">
      <text class="section-title">精美文创</text>
      <navigator url="/pages/products/products" class="more-btn">
        <text class="more-text">查看全部</text>
        <text class="more-arrow">→</text>
      </navigator>
    </view>
    <view class="product-grid">
      <view class="product-item" wx:for="{{recommendProducts}}" wx:key="id" bindtap="goToProductDetail" data-id="{{item.id}}">
        <view class="product-card">
          <view class="product-image">
            <image class="product-img" src="{{item.imageUrl}}" mode="aspectFill" wx:if="{{item.imageUrl}}" binderror="onProductImageError" data-index="{{index}}"></image>
            <text class="product-icon" wx:if="{{!item.imageUrl || item.imageError}}">🎋</text>
          </view>
          <view class="product-info">
            <text class="product-name">{{item.name}}</text>
            <text class="product-desc">{{item.description}}</text>
            <view class="product-price">
              <text class="price">¥{{item.price}}</text>
              <view class="rating">
                <text class="stars">★★★★★</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 中药图鉴 -->
  <view class="encyclopedia-section">
    <view class="section-header">
      <text class="section-title">中药图鉴</text>
      <navigator url="/pages/encyclopedia/encyclopedia" class="more-btn">
        <text class="more-text">进入图鉴</text>
        <text class="more-arrow">→</text>
      </navigator>
    </view>
    <view class="encyclopedia-grid">
      <view class="encyclopedia-item" wx:for="{{encyclopediaPreview}}" wx:key="id" bindtap="goToEncyclopediaDetail" data-id="{{item.id}}">
        <view class="encyclopedia-card">
          <view class="encyclopedia-image">
            <image class="encyclopedia-img" src="{{item.imageUrl}}" mode="aspectFill" wx:if="{{item.imageUrl}}" binderror="onEncyclopediaImageError" data-index="{{index}}"></image>
            <text class="encyclopedia-icon" wx:if="{{!item.imageUrl || item.imageError}}">📖</text>
          </view>
          <view class="encyclopedia-info">
            <text class="encyclopedia-name">{{item.name}}</text>
            <text class="encyclopedia-latin">{{item.latinName}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 养生文章 -->
  <view class="article-section">
    <view class="section-header">
      <text class="section-title">养生科普</text>
      <navigator url="/pages/articles/articles" class="more-btn">
        <text class="more-text">查看全部</text>
        <text class="more-arrow">→</text>
      </navigator>
    </view>
    <view class="article-list">
      <view class="article-item" wx:for="{{recommendArticles}}" wx:key="id" bindtap="goToArticleDetail" data-id="{{item.id}}">
        <view class="article-card">
          <view class="article-image">
            <text class="article-icon">📚</text>
          </view>
          <view class="article-content">
            <text class="article-title">{{item.title}}</text>
            <text class="article-summary">{{item.summary}}</text>
            <view class="article-meta">
              <text class="article-author">{{item.author}}</text>
              <text class="article-date">{{item.createTime}}</text>
              <text class="article-views">{{item.views}}阅读</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 地图功能 -->
  <view class="map-section">
    <view class="section-header">
      <text class="section-title">附近门店</text>
      <navigator url="/pages/map/map" class="more-btn">
        <text class="more-text">查看地图</text>
        <text class="more-arrow">→</text>
      </navigator>
    </view>
    <view class="map-card" bindtap="goToStoreMap">
      <view class="map-preview">
        <text class="map-icon">🗺️</text>
        <view class="map-info">
          <text class="store-count">发现{{nearbyStores.length}}家门店</text>
          <text class="nearest-distance">最近{{nearbyStores[0].distance}}km</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 在线咨询 -->
  <view class="consultation-section">
    <view class="section-header">
      <text class="section-title">在线咨询</text>
      <navigator url="/pages/consultation/consultation" class="more-btn">
        <text class="more-text">立即咨询</text>
        <text class="more-arrow">→</text>
      </navigator>
    </view>
    <view class="consultation-card" bindtap="goToConsultation">
      <view class="doctors-list">
        <view class="doctor-avatar" wx:for="{{onlineDoctors}}" wx:key="id">
          <text class="doctor-icon">👨‍⚕️</text>
          <view class="online-status"></view>
        </view>
      </view>
      <view class="consultation-info">
        <text class="online-count">{{onlineDoctors.length}}位医师在线</text>
        <text class="response-time">平均响应时间{{responseTime}}秒</text>
      </view>
    </view>
  </view>

  <!-- 开发工具 -->
  <view class="dev-tools">
    <view class="dev-header">
      <text class="dev-title">开发工具</text>
      <text class="dev-subtitle" wx:if="{{!isAdmin}}">点击"管理后台"可切换管理员模式</text>
    </view>
    <view class="dev-buttons">
      <button class="dev-btn" bindtap="goToIndexTest">首页测试</button>
      <button class="dev-btn" bindtap="goToTestIndex">测试页面</button>
      <button class="dev-btn" bindtap="goToCartTest">购物车测试</button>
      <button class="dev-btn" bindtap="goToDataTest">数据测试</button>
      <button class="dev-btn" bindtap="goToDetailTest">详情测试</button>
      <button class="dev-btn" bindtap="goToModuleTest">模块测试</button>
      <button class="dev-btn" bindtap="goToModuleFixTest">修复测试</button>
      <button class="dev-btn" bindtap="goToImageTest">图片测试</button>
      <button class="dev-btn" bindtap="goToMedicineImageTest">中药材图片</button>
      <button class="dev-btn" bindtap="goToDataOverview">数据概览</button>
      <button class="dev-btn" bindtap="goToLayoutPreview">布局预览</button>
      <button class="dev-btn" bindtap="goToDebug">系统诊断</button>
      <button class="dev-btn {{isAdmin ? 'admin-btn' : 'toggle-btn'}}" bindtap="goToAdmin">
        {{isAdmin ? '管理后台' : '切换管理员'}}
      </button>
    </view>
  </view>
</view>
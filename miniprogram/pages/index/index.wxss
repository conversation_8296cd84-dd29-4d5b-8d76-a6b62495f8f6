/**慧心制药首页样式**/

/* 全局容器 */
.container {
  background: linear-gradient(180deg, #F5F2E8 0%, #FEFCF7 100%);
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 顶部导航 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-bottom: 1rpx solid rgba(139, 69, 19, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.app-logo {
  font-size: 35rpx;
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.2);
}

.app-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  letter-spacing: 2rpx;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.weather {
  text-align: right;
}

.weather-text {
  font-size: 22rpx;
  color: #8B4513;
  font-weight: 500;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.2);
}

.avatar-text {
  font-size: 30rpx;
  color: white;
}

/* 轮播图 */
.banner-section {
  margin: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.1);
}

.banner-swiper {
  height: 400rpx;
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.banner-image {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-icon {
  font-size: 120rpx;
  opacity: 0.3;
  color: white;
}

.banner-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx 30rpx;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  color: white;
}

.banner-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.banner-desc {
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.4;
}

/* 功能导航 */
.nav-section {
  margin: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 25rpx;
  text-align: center;
}

/* 导航容器 - 大容器样式 */
.nav-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.12);
  border: 1rpx solid rgba(139, 69, 19, 0.08);
  position: relative;
  overflow: hidden;
}

/* 添加容器装饰效果 */
.nav-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #8B4513, #A0522D, #228B22);
  opacity: 0.6;
}

/* 3×2网格布局 */
.nav-grid-3x2 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 25rpx;
  width: 100%;
}

/* 导航项样式 */
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 25rpx 15rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 246, 240, 0.8));
  border-radius: 18rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.08);
  border: 1rpx solid rgba(139, 69, 19, 0.05);
  transition: all 0.3s ease;
  position: relative;
  min-height: 140rpx;
}

.nav-item:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 6rpx 20rpx rgba(139, 69, 19, 0.12);
  background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(248, 246, 240, 0.9));
}

/* 导航图标样式 */
.nav-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.15);
  position: relative;
  transition: all 0.3s ease;
}

/* 图标悬停效果 */
.nav-item:active .nav-icon {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 添加图标光晕效果 */
.nav-icon::after {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  border-radius: 18rpx;
  background: inherit;
  opacity: 0.3;
  filter: blur(4rpx);
  z-index: -1;
}

/* 图标渐变背景 */
.nav-icon.medicine {
  background: linear-gradient(135deg, #8B4513, #A0522D);
}

.nav-icon.product {
  background: linear-gradient(135deg, #228B22, #32CD32);
}

.nav-icon.encyclopedia {
  background: linear-gradient(135deg, #DAA520, #FFD700);
}

.nav-icon.map {
  background: linear-gradient(135deg, #4169E1, #6495ED);
}

.nav-icon.article {
  background: linear-gradient(135deg, #DC143C, #FF6347);
}

.nav-icon.consultation {
  background: linear-gradient(135deg, #9370DB, #BA55D3);
}

/* 图标文字 */
.icon {
  font-size: 36rpx;
  color: white;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.3));
}

/* 导航标签 */
.nav-label {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
  text-align: center;
  line-height: 1.2;
}

/* 导航计数 */
.nav-count {
  font-size: 16rpx;
  color: #8B4513;
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.1), rgba(139, 69, 19, 0.05));
  padding: 3rpx 10rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(139, 69, 19, 0.1);
  font-weight: 500;
}

/* 统计数据（管理员专用） */
.stats-section {
  margin: 30rpx;
  position: relative;
}

/* 管理员徽章 */
.admin-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  margin: 15rpx auto 25rpx;
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 215, 0, 0.3);
  width: fit-content;
  border: 1rpx solid rgba(255, 215, 0, 0.5);
}

.badge-icon {
  font-size: 20rpx;
  color: #8B4513;
}

.badge-text {
  font-size: 20rpx;
  font-weight: bold;
  color: #8B4513;
  letter-spacing: 1rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 25rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 246, 240, 0.9));
  border-radius: 20rpx;
  box-shadow: 0 6rpx 24rpx rgba(139, 69, 19, 0.1);
  border: 1rpx solid rgba(139, 69, 19, 0.05);
  position: relative;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 28rpx rgba(139, 69, 19, 0.15);
}

.stat-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 5rpx;
  text-shadow: 0 1rpx 2rpx rgba(139, 69, 19, 0.1);
}

.stat-label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

/* 增长趋势 */
.stat-trend {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
  font-size: 16rpx;
  font-weight: 500;
}

.stat-trend.positive {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
  color: #4CAF50;
  border: 1rpx solid rgba(76, 175, 80, 0.2);
}

.trend-icon {
  font-size: 14rpx;
}

.trend-text {
  font-size: 14rpx;
  font-weight: bold;
}

/* 今日推荐 */
.recommend-section {
  margin: 30rpx;
}

.recommend-banner {
  position: relative;
  margin-top: 25rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.1);
}

.recommend-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  display: flex;
  align-items: center;
  justify-content: center;
}

.recommend-icon {
  font-size: 100rpx;
  opacity: 0.3;
  color: white;
}

.recommend-content {
  position: relative;
  padding: 40rpx 30rpx;
  color: white;
  z-index: 10;
}

.recommend-title {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.recommend-desc {
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.recommend-tag {
  display: inline-block;
}

.tag-text {
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 通用区块样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.more-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #8B4513;
  font-size: 22rpx;
}

.more-text {
  font-weight: 500;
}

.more-arrow {
  font-size: 18rpx;
  transition: transform 0.3s ease;
}

.more-btn:active .more-arrow {
  transform: translateX(3rpx);
}

/* 中药材样式 */
.medicine-section {
  margin: 30rpx;
}

.medicine-scroll {
  margin-top: 25rpx;
  white-space: nowrap;
}

.medicine-item {
  display: inline-block;
  width: 280rpx;
  margin-right: 20rpx;
  vertical-align: top;
}

.medicine-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(139, 69, 19, 0.1);
  transition: all 0.3s ease;
}

.medicine-card:active {
  transform: translateY(-3rpx);
  box-shadow: 0 8rpx 28rpx rgba(139, 69, 19, 0.15);
}

.medicine-image {
  width: 100%;
  height: 180rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.medicine-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.medicine-icon {
  font-size: 60rpx;
  color: white;
  opacity: 0.8;
}

.medicine-info {
  padding: 25rpx;
}

.medicine-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.medicine-category {
  font-size: 20rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.medicine-price {
  display: flex;
  align-items: baseline;
  gap: 5rpx;
  margin-bottom: 15rpx;
}

.price {
  font-size: 28rpx;
  font-weight: bold;
  color: #E74C3C;
}

.unit {
  font-size: 18rpx;
  color: #999;
}

.medicine-tags {
  display: flex;
  gap: 8rpx;
}

.tag {
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  font-size: 18rpx;
  font-weight: 500;
}

.tag.hot {
  background: rgba(231, 76, 60, 0.1);
  color: #E74C3C;
}

.tag.quality {
  background: rgba(34, 139, 34, 0.1);
  color: #228B22;
}

/* 文创产品样式 */
.product-section {
  margin: 30rpx;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 25rpx;
}

.product-item {
  width: 100%;
}

.product-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(139, 69, 19, 0.1);
  transition: all 0.3s ease;
}

.product-card:active {
  transform: translateY(-3rpx);
  box-shadow: 0 8rpx 28rpx rgba(139, 69, 19, 0.15);
}

.product-image {
  width: 100%;
  height: 180rpx;
  background: linear-gradient(135deg, #228B22, #32CD32);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.product-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-icon {
  font-size: 60rpx;
  color: white;
  opacity: 0.8;
}

.product-info {
  padding: 25rpx;
}

.product-name {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-desc {
  font-size: 20rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 26rpx;
  font-weight: bold;
  color: #E74C3C;
}

.rating {
  display: flex;
  align-items: center;
}

.stars {
  font-size: 16rpx;
  color: #FFD700;
}

/* 中药图鉴样式 */
.encyclopedia-section {
  margin: 30rpx;
}

.encyclopedia-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 25rpx;
}

.encyclopedia-item {
  position: relative;
  height: 200rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(139, 69, 19, 0.1);
}

.encyclopedia-card {
  position: relative;
  width: 100%;
  height: 100%;
}

.encyclopedia-image {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #DAA520, #FFD700);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.encyclopedia-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.encyclopedia-icon {
  font-size: 60rpx;
  color: white;
  opacity: 0.8;
}

.encyclopedia-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 30rpx 20rpx 20rpx;
  color: white;
}

.encyclopedia-name {
  font-size: 24rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 5rpx;
}

.encyclopedia-latin {
  font-size: 18rpx;
  opacity: 0.8;
  font-style: italic;
}

/* 养生文章样式 */
.article-section {
  margin: 30rpx;
}

.article-list {
  margin-top: 25rpx;
}

.article-item {
  margin-bottom: 20rpx;
}

.article-card {
  display: flex;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 25rpx;
  box-shadow: 0 6rpx 24rpx rgba(139, 69, 19, 0.1);
  transition: all 0.3s ease;
}

.article-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 28rpx rgba(139, 69, 19, 0.15);
}

.article-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  background: linear-gradient(135deg, #DC143C, #FF6347);
  display: flex;
  align-items: center;
  justify-content: center;
}

.article-icon {
  font-size: 50rpx;
  color: white;
  opacity: 0.8;
}

.article-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.article-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-summary {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18rpx;
  color: #999;
}

.article-author {
  font-weight: 500;
}

.article-date {
  margin: 0 10rpx;
}

.article-views {
  color: #8B4513;
}

/* 地图功能样式 */
.map-section {
  margin: 30rpx;
}

.map-card {
  margin-top: 25rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 24rpx rgba(139, 69, 19, 0.1);
  transition: all 0.3s ease;
}

.map-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 28rpx rgba(139, 69, 19, 0.15);
}

.map-preview {
  position: relative;
  height: 150rpx;
  border-radius: 15rpx;
  overflow: hidden;
  background: linear-gradient(135deg, #4169E1, #6495ED);
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-icon {
  font-size: 60rpx;
  color: white;
  opacity: 0.8;
}

.map-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 20rpx;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.store-count, .nearest-distance {
  font-size: 20rpx;
}

/* 在线咨询样式 */
.consultation-section {
  margin: 30rpx;
}

.consultation-card {
  margin-top: 25rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 24rpx rgba(139, 69, 19, 0.1);
  transition: all 0.3s ease;
}

.consultation-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 28rpx rgba(139, 69, 19, 0.15);
}

.doctors-list {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.doctor-avatar {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #9370DB, #BA55D3);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.doctor-icon {
  font-size: 30rpx;
  color: white;
}

.online-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #4CAF50;
  border: 2rpx solid #fff;
}

.consultation-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.online-count {
  font-size: 22rpx;
  color: #4CAF50;
  font-weight: 500;
}

.response-time {
  font-size: 20rpx;
  color: #666;
}

/* 开发工具样式 */
.dev-tools {
  margin: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 2rpx dashed #8B4513;
}

.dev-header {
  margin-bottom: 20rpx;
  text-align: center;
}

.dev-title {
  font-size: 24rpx;
  color: #8B4513;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.dev-subtitle {
  font-size: 18rpx;
  color: #666;
  font-style: italic;
}

.dev-buttons {
  display: flex;
  gap: 15rpx;
  justify-content: center;
  flex-wrap: wrap;
}

.dev-btn {
  font-size: 20rpx;
  padding: 12rpx 20rpx;
  border: 1rpx solid #8B4513;
  color: #8B4513;
  background: transparent;
  border-radius: 20rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.dev-btn:active {
  background: rgba(139, 69, 19, 0.1);
}

/* 管理员按钮样式 */
.admin-btn {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #8B4513;
  border-color: #FFD700;
  font-weight: bold;
}

.admin-btn:active {
  background: linear-gradient(135deg, #FFA500, #FF8C00);
}

/* 切换按钮样式 */
.toggle-btn {
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.1), rgba(139, 69, 19, 0.05));
  border: 2rpx dashed #8B4513;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  /* 小屏幕时导航改为2×3布局 */
  .nav-grid-3x2 {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 20rpx;
  }

  .nav-container {
    padding: 25rpx;
  }

  .nav-item {
    padding: 20rpx 12rpx;
    min-height: 120rpx;
  }

  .nav-icon {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 10rpx;
  }

  .icon {
    font-size: 32rpx;
  }

  .nav-label {
    font-size: 20rpx;
  }

  .nav-count {
    font-size: 14rpx;
    padding: 2rpx 8rpx;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .product-grid {
    grid-template-columns: 1fr;
  }

  .encyclopedia-grid {
    grid-template-columns: 1fr;
  }

  .article-card {
    flex-direction: column;
  }

  .article-image {
    width: 100%;
    height: 200rpx;
    margin-right: 0;
    margin-bottom: 15rpx;
  }
}

/* 快捷功能导航 */
.quick-navigation {
  margin: 40rpx 30rpx;
  position: relative;
  z-index: 1;
}

.nav-header {
  text-align: center;
  margin-bottom: 35rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.1);
}

.nav-subtitle {
  font-size: 24rpx;
  color: #A0522D;
  opacity: 0.8;
}

.nav-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.nav-card {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent, rgba(139, 69, 19, 0.02));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-card:active {
  transform: translateY(-3rpx);
  box-shadow: 0 12rpx 40rpx rgba(139, 69, 19, 0.15);
}

.nav-card:active::before {
  opacity: 1;
}

.nav-icon {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 25rpx;
  flex-shrink: 0;
}

.medicine-theme {
  background: linear-gradient(135deg, #8B4513, #A0522D);
}

.product-theme {
  background: linear-gradient(135deg, #228B22, #32CD32);
}

.encyclopedia-theme {
  background: linear-gradient(135deg, #DAA520, #FFD700);
}

.map-theme {
  background: linear-gradient(135deg, #4169E1, #6495ED);
}

.article-theme {
  background: linear-gradient(135deg, #DC143C, #FF6347);
}

.consultation-theme {
  background: linear-gradient(135deg, #9370DB, #BA55D3);
}

.icon-symbol {
  font-size: 40rpx;
  color: white;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
}

.icon-glow {
  position: absolute;
  top: -5rpx;
  left: -5rpx;
  right: -5rpx;
  bottom: -5rpx;
  border-radius: 25rpx;
  opacity: 0.3;
  filter: blur(8rpx);
  z-index: -1;
}

.medicine-glow {
  background: linear-gradient(135deg, #8B4513, #A0522D);
}

.product-glow {
  background: linear-gradient(135deg, #228B22, #32CD32);
}

.encyclopedia-glow {
  background: linear-gradient(135deg, #DAA520, #FFD700);
}

.map-glow {
  background: linear-gradient(135deg, #4169E1, #6495ED);
}

.article-glow {
  background: linear-gradient(135deg, #DC143C, #FF6347);
}

.consultation-glow {
  background: linear-gradient(135deg, #9370DB, #BA55D3);
}

.nav-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.nav-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.nav-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.3;
  margin-bottom: 8rpx;
}

.nav-stats {
  display: flex;
  align-items: center;
}

.stats-text {
  font-size: 20rpx;
  color: #8B4513;
  font-weight: 500;
  background: rgba(139, 69, 19, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.nav-arrow {
  font-size: 24rpx;
  color: #8B4513;
  font-weight: bold;
  margin-left: 15rpx;
  transition: transform 0.3s ease;
}

.nav-card:active .nav-arrow {
  transform: translateX(5rpx);
}

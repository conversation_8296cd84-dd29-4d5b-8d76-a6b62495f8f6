// 测试首页
const app = getApp();

Page({
  data: {
    currentTime: '',
    loadTime: '',
    dataLoaded: false,
    userInfo: null,
    isAdmin: false,
    
    // 基础数据
    stats: {
      medicineCount: 0,
      productCount: 0,
      articleCount: 0,
      userCount: 0
    },
    
    bannerList: [],
    recommendMedicines: [],
    recommendProducts: [],
    recommendArticles: []
  },

  onLoad() {
    console.log('测试页面开始加载...');
    const startTime = new Date();
    this.setData({
      loadTime: startTime.toLocaleTimeString(),
      currentTime: startTime.toLocaleString()
    });
    
    this.loadAllData();
  },

  // 加载所有数据
  loadAllData() {
    console.log('开始加载所有数据...');
    
    // 加载用户信息
    this.setData({
      userInfo: app.globalData.userInfo,
      isAdmin: app.globalData.isAdmin
    });
    
    // 立即设置基础数据
    this.setData({
      stats: {
        medicineCount: 5,
        productCount: 6,
        articleCount: 4,
        userCount: 2
      }
    });
    
    // 设置轮播图数据
    this.setData({
      bannerList: [
        {
          id: 1,
          title: '传承千年中医智慧',
          subtitle: '慧心智药',
          description: '精选道地药材，传承古法炮制工艺'
        },
        {
          id: 2,
          title: '精选道地药材',
          subtitle: '品质保证',
          description: '源头直采，严格把控每一道工序'
        },
        {
          id: 3,
          title: '精美文创产品',
          subtitle: '文化传承',
          description: '融合传统文化与现代设计理念'
        }
      ]
    });
    
    // 设置推荐数据
    this.setData({
      recommendMedicines: [
        { id: 1, name: '人参', price: 12.5, category: '补气药' },
        { id: 2, name: '枸杞子', price: 8.8, category: '补血药' },
        { id: 3, name: '当归', price: 15.6, category: '补血药' },
        { id: 4, name: '黄芪', price: 9.2, category: '补气药' },
        { id: 5, name: '甘草', price: 6.5, category: '补气药' }
      ],
      
      recommendProducts: [
        { id: 1, name: '中医养生茶具套装', price: 168, description: '精美的中式茶具' },
        { id: 2, name: '本草纲目典藏版', price: 58, description: '经典中医药学著作' },
        { id: 3, name: '艾灸养生套装', price: 128, description: '传统艾灸工具' },
        { id: 4, name: '中药香薰炉', price: 88, description: '精美香薰炉' }
      ],
      
      recommendArticles: [
        { id: 1, title: '春季养生：顺应自然，调养身心', author: '中医专家' },
        { id: 2, title: '中医食疗：药食同源的智慧', author: '养生专家' },
        { id: 3, title: '四季养生茶饮推荐', author: '茶艺师' }
      ]
    });
    
    this.setData({
      dataLoaded: true
    });
    
    console.log('所有数据加载完成');
    console.log('轮播图数量:', this.data.bannerList.length);
    console.log('中药材数量:', this.data.recommendMedicines.length);
    console.log('文创产品数量:', this.data.recommendProducts.length);
    console.log('文章数量:', this.data.recommendArticles.length);
  },

  // 重新加载数据
  reloadData() {
    console.log('重新加载数据...');
    this.setData({
      dataLoaded: false,
      currentTime: new Date().toLocaleString()
    });
    
    setTimeout(() => {
      this.loadAllData();
    }, 500);
  },

  // 跳转到中药材页面
  goToMedicines() {
    console.log('跳转到中药材页面');
    wx.switchTab({
      url: '/pages/medicines/medicines'
    });
  },

  // 跳转到文创产品页面
  goToProducts() {
    console.log('跳转到文创产品页面');
    wx.switchTab({
      url: '/pages/products/products'
    });
  },

  // 跳转到养生文章页面
  goToArticles() {
    console.log('跳转到养生文章页面');
    wx.switchTab({
      url: '/pages/articles/articles'
    });
  },

  // 跳转到中药图鉴页面
  goToEncyclopedia() {
    console.log('跳转到中药图鉴页面');
    wx.navigateTo({
      url: '/pages/encyclopedia/encyclopedia'
    });
  },

  // 跳转到门店地图页面
  goToMap() {
    console.log('跳转到门店地图页面');
    wx.navigateTo({
      url: '/pages/map/map'
    });
  },

  // 跳转到在线咨询页面
  goToConsultation() {
    console.log('跳转到在线咨询页面');
    wx.navigateTo({
      url: '/pages/consultation/consultation'
    });
  },

  // 跳转到系统诊断
  goToDebug() {
    console.log('跳转到系统诊断页面');
    wx.navigateTo({
      url: '/pages/debug/debug'
    });
  }
});

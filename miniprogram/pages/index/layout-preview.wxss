/* 首页布局预览样式 */
.layout-preview {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.preview-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  text-align: center;
  display: block;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
  border-radius: 20rpx;
}

.section-preview {
  margin-bottom: 40rpx;
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #8B4513;
}

/* 轮播图预览 */
.banner-preview {
  display: flex;
  gap: 20rpx;
  overflow-x: auto;
}

.banner-placeholder {
  width: 200rpx;
  height: 120rpx;
  flex-shrink: 0;
}

/* 欢迎区域预览 */
.welcome-preview {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.logo-placeholder {
  width: 80rpx;
  height: 80rpx;
  flex-shrink: 0;
}

.welcome-text-preview {
  flex: 1;
}

.welcome-title-preview {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 8rpx;
}

.welcome-subtitle-preview {
  font-size: 22rpx;
  color: #666;
}

/* 数据统计预览 */
.stats-preview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-preview {
  padding: 20rpx;
  background-color: #F5F2E8;
  border-radius: 15rpx;
  text-align: center;
  font-size: 24rpx;
  color: #8B4513;
  font-weight: bold;
}

/* 图表预览 */
.chart-preview {
  height: 200rpx;
  background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
  border-radius: 15rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx dashed #ccc;
}

.chart-desc {
  font-size: 20rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 推荐内容预览 */
.recommend-preview {
  display: flex;
  gap: 20rpx;
  overflow-x: auto;
}

.recommend-item-preview {
  flex-shrink: 0;
  text-align: center;
}

.medicine-placeholder {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
}

.product-placeholder {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #228B22, #32CD32);
}

/* 文章预览 */
.article-preview {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.article-item-preview {
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background-color: #F5F2E8;
  border-radius: 15rpx;
}

.article-placeholder {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #DAA520, #FFD700);
  flex-shrink: 0;
}

.article-content-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.article-title-preview {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.article-summary-preview {
  font-size: 20rpx;
  color: #666;
}

/* 通用图片占位符 */
.image-placeholder {
  border-radius: 15rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  position: relative;
  border: 2rpx dashed rgba(255,255,255,0.5);
}

.placeholder-text {
  font-size: 20rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.path-text {
  font-size: 16rpx;
  opacity: 0.8;
  text-align: center;
  margin-bottom: 5rpx;
  word-break: break-all;
}

.size-text {
  font-size: 14rpx;
  opacity: 0.6;
}

/* 规格说明 */
.specs-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.spec-item {
  font-size: 22rpx;
  color: #333;
  padding: 8rpx 0;
}

/* 色彩搭配 */
.color-palette {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.color-item {
  width: 200rpx;
  height: 100rpx;
  border-radius: 15rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.color-text {
  font-size: 20rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.color-desc {
  font-size: 18rpx;
  opacity: 0.8;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .banner-preview {
    flex-direction: column;
  }
  
  .banner-placeholder {
    width: 100%;
    height: 120rpx;
  }
  
  .recommend-preview {
    flex-wrap: wrap;
  }
  
  .color-palette {
    justify-content: center;
  }
}

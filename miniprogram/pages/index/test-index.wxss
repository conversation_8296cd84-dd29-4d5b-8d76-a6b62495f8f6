/* 测试首页样式 */
.test-container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border-radius: 20rpx;
  color: white;
}

.test-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.test-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

.test-section {
  margin-bottom: 30rpx;
  background: white;
  border-radius: 15rpx;
  padding: 25rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #8B4513;
}

/* 基础信息 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.info-item {
  font-size: 24rpx;
  color: #333;
  padding: 8rpx 0;
}

/* 统计数据 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
  border-radius: 10rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #666;
}

/* 轮播图测试 */
.banner-test {
  display: flex;
  gap: 15rpx;
  overflow-x: auto;
}

.banner-item-test {
  flex-shrink: 0;
  width: 200rpx;
  text-align: center;
}

.banner-placeholder-test {
  width: 200rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}

.banner-emoji-test {
  font-size: 50rpx;
  opacity: 0.7;
}

.banner-title-test {
  font-size: 20rpx;
  color: #333;
}

/* 中药材测试 */
.medicine-test {
  display: flex;
  gap: 15rpx;
  overflow-x: auto;
}

.medicine-item-test {
  flex-shrink: 0;
  width: 150rpx;
  text-align: center;
}

.medicine-placeholder-test {
  width: 150rpx;
  height: 150rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.medicine-name-test {
  font-size: 22rpx;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.medicine-price-test {
  font-size: 20rpx;
  color: #E74C3C;
  font-weight: bold;
}

/* 文创产品测试 */
.product-test {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.product-item-test {
  text-align: center;
}

.product-placeholder-test {
  width: 100%;
  height: 120rpx;
  background: linear-gradient(135deg, #228B22, #32CD32);
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 50rpx;
  margin-bottom: 10rpx;
}

.product-name-test {
  font-size: 20rpx;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.product-price-test {
  font-size: 18rpx;
  color: #E74C3C;
  font-weight: bold;
}

/* 文章测试 */
.article-test {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.article-item-test {
  display: flex;
  gap: 15rpx;
  align-items: center;
}

.article-placeholder-test {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #DC143C, #FF6347);
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  flex-shrink: 0;
}

.article-content-test {
  flex: 1;
}

.article-title-test {
  font-size: 22rpx;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.article-author-test {
  font-size: 18rpx;
  color: #999;
}

/* 按钮网格 */
.button-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.test-btn {
  padding: 20rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 22rpx;
}

.test-btn:active {
  opacity: 0.8;
}

/* 调试信息 */
.debug-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.debug-item {
  font-size: 20rpx;
  color: #666;
  padding: 5rpx 0;
}

<!--测试首页 - 确保能显示基础内容-->
<view class="test-container">
  <!-- 标题 -->
  <view class="test-header">
    <text class="test-title">慧心制药 - 测试页面</text>
    <text class="test-subtitle">基础内容展示</text>
  </view>

  <!-- 基础信息 -->
  <view class="test-section">
    <text class="section-title">基础信息</text>
    <view class="info-list">
      <text class="info-item">应用名称：慧心制药</text>
      <text class="info-item">当前时间：{{currentTime}}</text>
      <text class="info-item">用户状态：{{userInfo ? '已登录' : '未登录'}}</text>
      <text class="info-item">管理员：{{isAdmin ? '是' : '否'}}</text>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="test-section">
    <text class="section-title">统计数据</text>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{stats.medicineCount}}</text>
        <text class="stat-label">中药材</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.productCount}}</text>
        <text class="stat-label">文创产品</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.articleCount}}</text>
        <text class="stat-label">养生文章</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.userCount}}</text>
        <text class="stat-label">注册用户</text>
      </view>
    </view>
  </view>

  <!-- 轮播图测试 -->
  <view class="test-section">
    <text class="section-title">轮播图 ({{bannerList.length}}张)</text>
    <view class="banner-test">
      <view class="banner-item-test" wx:for="{{bannerList}}" wx:key="id">
        <view class="banner-placeholder-test">
          <text class="banner-emoji-test">🌿</text>
        </view>
        <text class="banner-title-test">{{item.title}}</text>
      </view>
    </view>
  </view>

  <!-- 推荐中药材测试 -->
  <view class="test-section">
    <text class="section-title">推荐中药材 ({{recommendMedicines.length}}个)</text>
    <view class="medicine-test">
      <view class="medicine-item-test" wx:for="{{recommendMedicines}}" wx:key="id">
        <view class="medicine-placeholder-test">🌿</view>
        <text class="medicine-name-test">{{item.name}}</text>
        <text class="medicine-price-test">¥{{item.price}}/克</text>
      </view>
    </view>
  </view>

  <!-- 推荐文创产品测试 -->
  <view class="test-section">
    <text class="section-title">推荐文创产品 ({{recommendProducts.length}}个)</text>
    <view class="product-test">
      <view class="product-item-test" wx:for="{{recommendProducts}}" wx:key="id">
        <view class="product-placeholder-test">🎋</view>
        <text class="product-name-test">{{item.name}}</text>
        <text class="product-price-test">¥{{item.price}}</text>
      </view>
    </view>
  </view>

  <!-- 推荐文章测试 -->
  <view class="test-section">
    <text class="section-title">推荐文章 ({{recommendArticles.length}}篇)</text>
    <view class="article-test">
      <view class="article-item-test" wx:for="{{recommendArticles}}" wx:key="id">
        <view class="article-placeholder-test">📚</view>
        <view class="article-content-test">
          <text class="article-title-test">{{item.title}}</text>
          <text class="article-author-test">作者：{{item.author}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能按钮 -->
  <view class="test-section">
    <text class="section-title">功能测试</text>
    <view class="button-grid">
      <button class="test-btn" bindtap="goToMedicines">中药材</button>
      <button class="test-btn" bindtap="goToProducts">文创产品</button>
      <button class="test-btn" bindtap="goToArticles">养生文章</button>
      <button class="test-btn" bindtap="goToEncyclopedia">中药图鉴</button>
      <button class="test-btn" bindtap="goToMap">门店地图</button>
      <button class="test-btn" bindtap="goToConsultation">在线咨询</button>
    </view>
  </view>

  <!-- 调试信息 -->
  <view class="test-section" wx:if="{{isAdmin}}">
    <text class="section-title">调试信息</text>
    <view class="debug-info">
      <text class="debug-item">页面加载时间：{{loadTime}}</text>
      <text class="debug-item">数据加载状态：{{dataLoaded ? '已加载' : '加载中'}}</text>
      <button class="test-btn" bindtap="reloadData">重新加载数据</button>
      <button class="test-btn" bindtap="goToDebug">系统诊断</button>
    </view>
  </view>
</view>

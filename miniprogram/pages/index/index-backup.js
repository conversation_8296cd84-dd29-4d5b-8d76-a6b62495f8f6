// 慧心制药首页 - 备份版本
const app = getApp();

Page({
  data: {
    // 用户信息
    userInfo: null,
    isAdmin: false,

    // 轮播图数据
    bannerList: [],

    // 统计数据
    stats: {
      medicineCount: 25,
      productCount: 25,
      articleCount: 234,
      userCount: 1567
    },

    // 推荐内容
    recommendMedicines: [],
    recommendProducts: [],
    recommendArticles: [],
    encyclopediaPreview: [],

    // 今日推荐
    todayRecommend: {
      title: '春季养生特惠',
      description: '精选道地药材，助您健康度春'
    },

    // 天气信息
    weather: {
      city: '北京',
      temperature: 18,
      icon: '☀️'
    },

    // 附近门店
    nearbyStores: [
      { id: 1, name: '慧心智药旗舰店', distance: 0.8 },
      { id: 2, name: '慧心智药分店', distance: 1.2 }
    ],

    // 在线医师
    onlineDoctors: [
      { id: 1, name: '张医师', title: '主治医师' },
      { id: 2, name: '李医师', title: '副主任医师' }
    ],

    // 咨询统计
    responseTime: 30
  },

  onLoad: function() {
    console.log('首页加载中...');
    this.loadUserInfo();
    this.loadAllData();
  },

  onShow: function() {
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo: function() {
    this.setData({
      userInfo: app.globalData.userInfo,
      isAdmin: app.globalData.isAdmin
    });
  },

  // 加载所有数据
  loadAllData: function() {
    this.loadBannerData();
    this.loadRecommendData();
    console.log('所有数据加载完成');
  },

  // 加载轮播图数据
  loadBannerData: function() {
    const bannerList = [
      {
        id: 1,
        title: '传承千年中医智慧',
        description: '精选道地药材，传承古法炮制工艺',
        icon: '🌿'
      },
      {
        id: 2,
        title: '精选道地药材',
        description: '源头直采，严格把控每一道工序',
        icon: '🍃'
      }
    ];

    this.setData({ bannerList });
    console.log('轮播图数据已加载:', bannerList.length, '张');
  },

  // 加载推荐数据
  loadRecommendData: function() {
    // 推荐中药材
    const recommendMedicines = [
      {
        id: '1',
        name: '人参',
        category: '补气药',
        price: 12.5,
        isHot: true,
        isQuality: true
      },
      {
        id: '2',
        name: '枸杞子',
        category: '补血药',
        price: 8.8,
        isHot: true,
        isQuality: true
      }
    ];

    // 推荐文创产品
    const recommendProducts = [
      {
        id: '1',
        name: '中医养生茶具套装',
        description: '精美的紫砂茶具，适合泡制各种养生茶',
        price: 168.0
      },
      {
        id: '2',
        name: '本草纲目典藏版',
        description: '李时珍经典著作，中医药学必读书籍',
        price: 58.0
      }
    ];

    // 推荐文章
    const recommendArticles = [
      {
        id: '1',
        title: '春季养生：顺应自然，调养身心',
        summary: '春季是万物复苏的季节，人体阳气开始升发。',
        author: '张医师',
        createTime: '2024-03-15',
        views: 1250
      }
    ];

    // 中药图鉴预览
    const encyclopediaPreview = [
      {
        id: 'enc_001',
        name: '人参',
        latinName: 'Panax ginseng'
      },
      {
        id: '1',
        name: '枸杞子',
        latinName: 'Lycium barbarum'
      }
    ];

    this.setData({
      recommendMedicines,
      recommendProducts,
      recommendArticles,
      encyclopediaPreview
    });

    console.log('推荐数据已加载');
  },

  // 轮播图点击事件
  onBannerTap: function(e) {
    const item = e.currentTarget.dataset.item;
    console.log('点击轮播图:', item);
  },

  // 跳转到今日推荐
  goToTodayRecommend: function() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 跳转到个人中心
  goToProfile: function() {
    if (!this.data.userInfo) {
      wx.navigateTo({
        url: '/pages/login/login'
      });
    } else {
      wx.switchTab({
        url: '/pages/profile/profile'
      });
    }
  },

  // 跳转到中药材页面
  goToMedicines: function() {
    wx.switchTab({
      url: '/pages/medicines/medicines'
    });
  },

  // 跳转到文创产品页面
  goToProducts: function() {
    wx.switchTab({
      url: '/pages/products/products'
    });
  },

  // 跳转到养生文章页面
  goToArticles: function() {
    wx.navigateTo({
      url: '/pages/article-detail/article-detail?id=article_001'
    });
  },

  // 跳转到中药材详情
  goToMedicineDetail: function(e) {
    var id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/medicine-detail/medicine-detail?id=' + id
    });
  },

  // 跳转到文创产品详情
  goToProductDetail: function(e) {
    var id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/product-detail/product-detail?id=' + id
    });
  },

  // 跳转到中药图鉴
  goToEncyclopedia: function() {
    wx.navigateTo({
      url: '/pages/encyclopedia/encyclopedia'
    });
  },

  // 跳转到门店地图
  goToMap: function() {
    wx.navigateTo({
      url: '/pages/store-map/store-map'
    });
  },

  // 跳转到文章详情
  goToArticleDetail: function(e) {
    var id = e.currentTarget.dataset.id || 'article_001';
    wx.navigateTo({
      url: '/pages/article-detail/article-detail?id=' + id
    });
  },

  // 跳转到中药图鉴详情
  goToEncyclopediaDetail: function(e) {
    var id = e.currentTarget.dataset.id || 'enc_001';
    wx.navigateTo({
      url: '/pages/encyclopedia-detail/encyclopedia-detail?id=' + id
    });
  },

  // 中医咨询跳转
  goToConsultation: function() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 开发工具方法
  goToTestIndex: function() {
    wx.navigateTo({
      url: '/pages/index/test-index'
    });
  },

  goToCartTest: function() {
    wx.navigateTo({
      url: '/pages/cart-test/cart-test'
    });
  },

  goToDataOverview: function() {
    wx.navigateTo({
      url: '/pages/data-overview/data-overview'
    });
  },

  goToDataTest: function() {
    wx.navigateTo({
      url: '/pages/data-test/data-test'
    });
  },

  goToDetailTest: function() {
    wx.navigateTo({
      url: '/pages/detail-test/detail-test'
    });
  },

  goToModuleTest: function() {
    wx.navigateTo({
      url: '/pages/module-test/module-test'
    });
  },

  goToLayoutPreview: function() {
    wx.navigateTo({
      url: '/pages/index/layout-preview'
    });
  },

  goToDebug: function() {
    wx.navigateTo({
      url: '/pages/debug/debug'
    });
  },

  goToAdmin: function() {
    if (!this.data.isAdmin) {
      wx.showModal({
        title: '管理员模式',
        content: '是否切换到管理员模式？（仅用于开发测试）',
        success: (res) => {
          if (res.confirm) {
            this.toggleAdminMode();
          }
        }
      });
    } else {
      wx.navigateTo({
        url: '/pages/admin/admin'
      });
    }
  },

  // 切换管理员模式
  toggleAdminMode: function() {
    const isCurrentlyAdmin = this.data.isAdmin;
    const newAdminStatus = !isCurrentlyAdmin;

    const testUserInfo = {
      id: 'test_admin_001',
      name: '测试管理员',
      avatar: '👑',
      role: newAdminStatus ? 'admin' : 'user'
    };

    app.setUserInfo(testUserInfo);

    this.setData({
      userInfo: testUserInfo,
      isAdmin: newAdminStatus
    });

    wx.showToast({
      title: newAdminStatus ? '已切换到管理员模式' : '已切换到普通用户模式',
      icon: 'success',
      duration: 2000
    });

    console.log('管理员模式切换:', newAdminStatus ? '开启' : '关闭');
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 空函数，用于阻止事件冒泡
  },

  // 分享给朋友
  onShareAppMessage: function() {
    return {
      title: '慧心智药 - 传承中医智慧',
      path: '/pages/index/index'
    };
  },

  // 分享到朋友圈
  onShareTimeline: function() {
    return {
      title: '慧心智药 - 传承中医智慧'
    };
  }
});

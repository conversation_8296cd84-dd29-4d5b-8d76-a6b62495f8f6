// 首页布局预览页面
Page({
  data: {
    
  },

  onLoad() {
    console.log('首页布局预览页面加载');
  },

  // 复制路径到剪贴板
  copyPath(e) {
    const path = e.currentTarget.dataset.path;
    if (path) {
      wx.setClipboardData({
        data: path,
        success: () => {
          wx.showToast({
            title: '路径已复制',
            icon: 'success'
          });
        }
      });
    }
  },

  // 返回首页
  goToIndex() {
    wx.navigateBack();
  }
});

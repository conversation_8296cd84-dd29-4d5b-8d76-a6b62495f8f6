// 慧心制药首页 - 修复版本
const app = getApp();

Page({
  data: {
    // 用户信息
    userInfo: null,
    isAdmin: false,

    // 轮播图数据
    bannerList: [],

    // 统计数据（管理员可见）
    stats: {
      medicineCount: 25,
      productCount: 25,
      articleCount: 234,
      userCount: 1567,
      // 增长率数据（管理员专用）
      medicineGrowth: 12,
      productGrowth: 8,
      articleGrowth: 15,
      userGrowth: 23
    },

    // 推荐内容
    recommendMedicines: [],
    recommendProducts: [],
    recommendArticles: [],
    encyclopediaPreview: [],

    // 今日推荐
    todayRecommend: {
      title: '春季养生特惠',
      description: '精选道地药材，助您健康度春'
    },

    // 天气信息
    weather: {
      city: '北京',
      temperature: 18,
      icon: '☀️'
    },

    // 附近门店
    nearbyStores: [
      { id: 1, name: '慧心智药旗舰店', distance: 0.8 },
      { id: 2, name: '慧心智药分店', distance: 1.2 },
      { id: 3, name: '慧心智药三店', distance: 2.1 },
      { id: 4, name: '慧心智药四店', distance: 2.8 },
      { id: 5, name: '慧心智药五店', distance: 3.2 }
    ],

    // 在线医师
    onlineDoctors: [
      { id: 1, name: '张医师', title: '主治医师' },
      { id: 2, name: '李医师', title: '副主任医师' },
      { id: 3, name: '王医师', title: '主任医师' },
      { id: 4, name: '赵医师', title: '主治医师' },
      { id: 5, name: '刘医师', title: '副主任医师' }
    ],

    // 咨询统计
    responseTime: 30
  },
  onLoad() {
    console.log('首页加载中...');
    this.loadUserInfo();
    this.loadAllData();
  },

  onShow() {
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo() {
    this.setData({
      userInfo: app.globalData.userInfo,
      isAdmin: app.globalData.isAdmin
    });
  },

  // 加载所有数据
  loadAllData() {
    this.loadBannerData();
    this.loadRecommendData();
    console.log('所有数据加载完成');
  },

  // 加载轮播图数据
  loadBannerData() {
    const bannerList = [
      {
        id: 1,
        title: '传承千年中医智慧',
        description: '精选道地药材，传承古法炮制工艺',
        imageUrl: '/images/轮播图/轮播图-传承千年中医智慧.jpg',
        icon: '🌿'
      },
      {
        id: 2,
        title: '精选道地药材',
        description: '源头直采，严格把控每一道工序',
        imageUrl: '/images/轮播图/轮播图-精选道地药材.jpg',
        icon: '🍃'
      },
      {
        id: 3,
        title: '精美文创产品',
        description: '融合传统文化与现代设计理念',
        imageUrl: '/images/轮播图/轮播图-弘扬养生文化.jpg',
        icon: '🎋'
      }
    ];

    this.setData({ bannerList });
    console.log('轮播图数据已加载:', bannerList.length, '张');
  },

  // 加载推荐数据
  loadRecommendData() {
    // 推荐中药材
    const recommendMedicines = [
      {
        id: 'med_001',
        name: '人参',
        category: '补气药',
        price: 12.5,
        imageUrl: '/images/中药材/人参.jpg',
        isHot: true,
        isQuality: true
      },
      {
        id: 'med_002',
        name: '枸杞子',
        category: '补血药',
        price: 8.8,
        imageUrl: '/images/中药材/枸杞子.jpg',
        isHot: true,
        isQuality: true
      },
      {
        id: 'med_003',
        name: '当归',
        category: '补血药',
        price: 15.6,
        imageUrl: '/images/中药材/当归.jpg',
        isHot: false,
        isQuality: true
      },
      {
        id: 'med_004',
        name: '黄芪',
        category: '补气药',
        price: 9.2,
        imageUrl: '/images/中药材/黄芪.jpg',
        isHot: true,
        isQuality: true
      },
      {
        id: 'med_005',
        name: '甘草',
        category: '补气药',
        price: 6.5,
        imageUrl: '/images/中药材/甘草.jpg',
        isHot: false,
        isQuality: false
      }
    ];

    // 推荐文创产品
    const recommendProducts = [
      {
        id: 'prod_001',
        name: '中医养生茶具套装',
        description: '精美的紫砂茶具，适合泡制各种养生茶',
        price: 168.0,
        imageUrl: '/images/文创/中医养生茶具套装.jpg'
      },
      {
        id: 'prod_002',
        name: '本草纲目典藏版',
        description: '李时珍经典著作，中医药学必读书籍',
        price: 58.0,
        imageUrl: '/images/文创/本草纲目典藏版.jpg'
      },
      {
        id: 'prod_003',
        name: '艾灸养生套装',
        description: '传统艾灸工具，居家养生必备',
        price: 128.0,
        imageUrl: '/images/文创/艾灸养生套装.jpg'
      },
      {
        id: 'prod_004',
        name: '中药香薰炉',
        description: '精美香薰炉，营造舒适养生环境',
        price: 88.0,
        imageUrl: '/images/文创/中药香薰炉.jpg'
      }
    ];

    // 推荐文章
    const recommendArticles = [
      {
        id: 'art_001',
        title: '春季养生：顺应自然，调养身心',
        summary: '春季是万物复苏的季节，人体阳气开始升发，此时养生应顺应自然规律。',
        author: '张医师',
        createTime: '2024-03-15',
        views: 1250
      },
      {
        id: 'art_002',
        title: '中医食疗：药食同源的智慧',
        summary: '中医认为"药食同源"，许多食物既是美味佳肴，又具有药用价值。',
        author: '李医师',
        createTime: '2024-03-12',
        views: 980
      },
      {
        id: 'art_003',
        title: '四季养生茶饮推荐',
        summary: '不同季节适合饮用不同的养生茶，春饮花茶，夏饮绿茶。',
        author: '王医师',
        createTime: '2024-03-10',
        views: 756
      }
    ];

    // 中药图鉴预览
    const encyclopediaPreview = [
      {
        id: 'enc_001',
        name: '人参',
        latinName: 'Panax ginseng',
        imageUrl: '/images/中药材/人参.jpg'
      },
      {
        id: 'enc_002',
        name: '枸杞子',
        latinName: 'Lycium barbarum',
        imageUrl: '/images/中药材/枸杞子.jpg'
      },
      {
        id: 'enc_003',
        name: '当归',
        latinName: 'Angelica sinensis',
        imageUrl: '/images/中药材/当归.jpg'
      },
      {
        id: 'enc_004',
        name: '黄芪',
        latinName: 'Astragalus membranaceus',
        imageUrl: '/images/中药材/黄芪.jpg'
      }
    ];

    this.setData({
      recommendMedicines,
      recommendProducts,
      recommendArticles,
      encyclopediaPreview
    });

    console.log('推荐数据已加载');
  },

  // 轮播图点击事件
  onBannerTap(e) {
    const item = e.currentTarget.dataset.item;
    console.log('点击轮播图:', item);
    // 可以根据轮播图类型跳转到不同页面
  },

  // 轮播图图片加载错误处理
  onBannerImageError: function(e) {
    var index = e.currentTarget.dataset.index;
    var bannerList = this.data.bannerList;
    if (bannerList[index]) {
      bannerList[index].imageError = true;
      this.setData({
        bannerList: bannerList
      });
      console.log('轮播图图片加载失败，使用备用图标:', bannerList[index].title);
    }
  },

  // 中药材图片加载错误处理
  onMedicineImageError: function(e) {
    var index = e.currentTarget.dataset.index;
    var recommendMedicines = this.data.recommendMedicines;
    if (recommendMedicines[index]) {
      recommendMedicines[index].imageError = true;
      this.setData({
        recommendMedicines: recommendMedicines
      });
      console.log('中药材图片加载失败，使用备用图标:', recommendMedicines[index].name);
    }
  },

  // 文创产品图片加载错误处理
  onProductImageError: function(e) {
    var index = e.currentTarget.dataset.index;
    var recommendProducts = this.data.recommendProducts;
    if (recommendProducts[index]) {
      recommendProducts[index].imageError = true;
      this.setData({
        recommendProducts: recommendProducts
      });
      console.log('文创产品图片加载失败，使用备用图标:', recommendProducts[index].name);
    }
  },

  // 中药图鉴图片加载错误处理
  onEncyclopediaImageError: function(e) {
    var index = e.currentTarget.dataset.index;
    var encyclopediaPreview = this.data.encyclopediaPreview;
    if (encyclopediaPreview[index]) {
      encyclopediaPreview[index].imageError = true;
      this.setData({
        encyclopediaPreview: encyclopediaPreview
      });
      console.log('中药图鉴图片加载失败，使用备用图标:', encyclopediaPreview[index].name);
    }
  },

  // 跳转到今日推荐
  goToTodayRecommend() {
    wx.navigateTo({
      url: '/pages/recommend/today'
    });
  },

  // 跳转到个人中心
  goToProfile() {
    if (!this.data.userInfo) {
      wx.navigateTo({
        url: '/pages/login/login'
      });
    } else {
      wx.switchTab({
        url: '/pages/profile/profile'
      });
    }
  },

  // 跳转到中药材页面
  goToMedicines() {
    wx.switchTab({
      url: '/pages/medicines/medicines'
    });
  },

  // 跳转到文创产品页面
  goToProducts() {
    wx.switchTab({
      url: '/pages/products/products'
    });
  },

  // 跳转到养生文章页面
  goToArticles: function() {
    wx.navigateTo({
      url: '/pages/article-detail/article-detail?id=article_001'
    });
  },



  // 跳转到中药材详情
  goToMedicineDetail: function(e) {
    var id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/medicine-detail/medicine-detail?id=' + id
    });
  },

  // 跳转到文创产品详情
  goToProductDetail: function(e) {
    var id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/product-detail/product-detail?id=' + id
    });
  },

  // 开发工具方法
  goToTestIndex: function() {
    wx.navigateTo({
      url: '/pages/index/test-index'
    });
  },

  goToCartTest: function() {
    wx.navigateTo({
      url: '/pages/cart-test/cart-test'
    });
  },

  goToDataOverview: function() {
    wx.navigateTo({
      url: '/pages/data-overview/data-overview'
    });
  },

  goToDataTest: function() {
    wx.navigateTo({
      url: '/pages/data-test/data-test'
    });
  },

  goToDetailTest: function() {
    wx.navigateTo({
      url: '/pages/detail-test/detail-test'
    });
  },

  // 跳转到中药图鉴
  goToEncyclopedia: function() {
    wx.navigateTo({
      url: '/pages/encyclopedia/encyclopedia'
    });
  },

  // 跳转到门店地图
  goToStoreMap: function() {
    wx.navigateTo({
      url: '/pages/store-map/store-map'
    });
  },

  // 跳转到文章详情
  goToArticleDetail: function(e) {
    var id = e.currentTarget.dataset.id || 'article_001';
    wx.navigateTo({
      url: '/pages/article-detail/article-detail?id=' + id
    });
  },

  // 跳转到中药图鉴详情
  goToEncyclopediaDetail: function(e) {
    var id = e.currentTarget.dataset.id || 'enc_001';
    wx.navigateTo({
      url: '/pages/encyclopedia-detail/encyclopedia-detail?id=' + id
    });
  },

  // 中医咨询跳转
  goToConsultation: function() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 跳转到模块测试
  goToModuleTest: function() {
    wx.navigateTo({
      url: '/pages/module-test/module-test'
    });
  },

  // 跳转到首页测试
  goToIndexTest: function() {
    wx.navigateTo({
      url: '/pages/index-test/index-test'
    });
  },

  // 跳转到模块修复测试
  goToModuleFixTest: function() {
    wx.navigateTo({
      url: '/pages/module-fix-test/module-fix-test'
    });
  },

  // 跳转到图片测试
  goToImageTest: function() {
    wx.navigateTo({
      url: '/pages/image-test/image-test'
    });
  },

  // 跳转到中药材图片测试
  goToMedicineImageTest: function() {
    wx.navigateTo({
      url: '/pages/medicine-image-test/medicine-image-test'
    });
  },

  goToLayoutPreview() {
    wx.navigateTo({
      url: '/pages/index/layout-preview'
    });
  },

  goToDebug() {
    wx.navigateTo({
      url: '/pages/debug/debug'
    });
  },

  goToAdmin() {
    // 如果还不是管理员，提供快速切换选项
    if (!this.data.isAdmin) {
      wx.showModal({
        title: '管理员模式',
        content: '是否切换到管理员模式？（仅用于开发测试）',
        success: (res) => {
          if (res.confirm) {
            this.toggleAdminMode();
          }
        }
      });
    } else {
      wx.navigateTo({
        url: '/pages/admin/admin'
      });
    }
  },

  // 切换管理员模式（开发测试用）
  toggleAdminMode() {
    const isCurrentlyAdmin = this.data.isAdmin;
    const newAdminStatus = !isCurrentlyAdmin;

    // 创建测试用户信息
    const testUserInfo = {
      id: 'test_admin_001',
      name: '测试管理员',
      avatar: '👑',
      role: newAdminStatus ? 'admin' : 'user'
    };

    // 更新全局状态
    app.setUserInfo(testUserInfo);

    // 更新页面状态
    this.setData({
      userInfo: testUserInfo,
      isAdmin: newAdminStatus
    });

    wx.showToast({
      title: newAdminStatus ? '已切换到管理员模式' : '已切换到普通用户模式',
      icon: 'success',
      duration: 2000
    });

    console.log('管理员模式切换:', newAdminStatus ? '开启' : '关闭');
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  // 分享给朋友
  onShareAppMessage() {
    return {
      title: '慧心智药 - 传承中医智慧',
      path: '/pages/index/index'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '慧心智药 - 传承中医智慧'
    };
  }
});

/**debug.wxss**/

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #E74C3C 0%, #C0392B 100%);
  color: white;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.page-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 快速操作 */
.quick-actions {
  margin-bottom: 30rpx;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  margin-top: 20rpx;
}

.action-btn {
  padding: 25rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: bold;
  border: none;
  margin: 0;
}

.action-btn.primary {
  background-color: #E74C3C;
  color: white;
}

.action-btn.secondary {
  background-color: #3498DB;
  color: white;
}

.action-btn:disabled {
  background-color: #BDC3C7;
  color: #7F8C8D;
}

/* 诊断结果 */
.diagnosis-results {
  margin-bottom: 30rpx;
}

.result-section {
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #ECF0F1;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2C3E50;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.status-badge.success {
  background-color: #27AE60;
  color: white;
}

.status-badge.error {
  background-color: #E74C3C;
  color: white;
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-item {
  font-size: 24rpx;
  color: #34495E;
  padding: 8rpx 0;
}

.info-label {
  font-size: 22rpx;
  color: #7F8C8D;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.error-message {
  font-size: 22rpx;
  color: #E74C3C;
  background-color: #FADBD8;
  padding: 12rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #E74C3C;
}

/* 集合列表 */
.collections-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.collection-item {
  font-size: 22rpx;
  color: #2980B9;
  background-color: #EBF3FD;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  display: inline-block;
  margin-right: 10rpx;
  margin-bottom: 8rpx;
}

/* 用户列表 */
.users-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.username {
  font-size: 24rpx;
  color: #2C3E50;
  font-weight: bold;
}

.role {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  color: white;
}

.role.admin {
  background-color: #E74C3C;
}

.role.user {
  background-color: #3498DB;
}

/* 管理员列表 */
.admin-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.admin-username {
  font-size: 22rpx;
  color: #E74C3C;
  background-color: #FADBD8;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  font-weight: bold;
}

/* 测试日志 */
.test-logs {
  margin-bottom: 30rpx;
}

.logs-content {
  max-height: 400rpx;
  overflow-y: auto;
}

.log-item {
  padding: 12rpx;
  margin-bottom: 8rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #BDC3C7;
}

.log-item.success {
  background-color: #D5EDDA;
  border-left-color: #27AE60;
}

.log-item.error {
  background-color: #FADBD8;
  border-left-color: #E74C3C;
}

.log-item.info {
  background-color: #D1ECF1;
  border-left-color: #3498DB;
}

.log-time {
  font-size: 20rpx;
  color: #7F8C8D;
  display: block;
  margin-bottom: 4rpx;
}

.log-message {
  font-size: 22rpx;
  color: #2C3E50;
}

.clear-btn {
  background-color: #95A5A6;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
  border: none;
}

/* 解决方案 */
.solutions {
  margin-bottom: 30rpx;
}

.solution-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  margin-top: 20rpx;
}

.solution-item {
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #F39C12;
}

.solution-item.error {
  background-color: #FADBD8;
  border-left-color: #E74C3C;
}

.solution-item.warning {
  background-color: #FCF3CF;
  border-left-color: #F39C12;
}

.solution-item.info {
  background-color: #D1ECF1;
  border-left-color: #3498DB;
}

.solution-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #2C3E50;
  display: block;
  margin-bottom: 8rpx;
}

.solution-desc {
  font-size: 22rpx;
  color: #34495E;
  line-height: 1.5;
  display: block;
  margin-bottom: 15rpx;
}

.solution-btn {
  background-color: #3498DB;
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  border: none;
}

/* 手动测试 */
.manual-test {
  margin-bottom: 30rpx;
}

.test-section {
  margin-top: 20rpx;
}

.test-label {
  font-size: 24rpx;
  color: #2C3E50;
  font-weight: bold;
  display: block;
  margin-bottom: 15rpx;
}

.test-inputs {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.test-input {
  padding: 20rpx;
  border: 2rpx solid #BDC3C7;
  border-radius: 8rpx;
  font-size: 24rpx;
  background-color: white;
}

.test-input:focus {
  border-color: #3498DB;
}

.test-btn {
  background-color: #27AE60;
  color: white;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
}

.test-btn:disabled {
  background-color: #BDC3C7;
  color: #7F8C8D;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .action-buttons {
    gap: 10rpx;
  }
  
  .user-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .role {
    margin-top: 8rpx;
  }
}

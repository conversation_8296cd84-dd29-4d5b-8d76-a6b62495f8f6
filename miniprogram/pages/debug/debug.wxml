<!--debug.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">系统诊断工具</text>
    <text class="page-desc">检查云函数、数据库和权限状态</text>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions card">
    <text class="section-title">快速操作</text>
    <view class="action-buttons">
      <button class="action-btn primary" bindtap="runDiagnosis" disabled="{{diagnosing}}">
        {{diagnosing ? '诊断中...' : '运行完整诊断'}}
      </button>
      <button class="action-btn secondary" bindtap="testCloudFunction" disabled="{{testing}}">
        {{testing ? '测试中...' : '测试云函数'}}
      </button>
      <button class="action-btn secondary" bindtap="forceInitUsers" disabled="{{initializing}}">
        {{initializing ? '初始化中...' : '强制初始化用户'}}
      </button>
    </view>
  </view>

  <!-- 诊断结果 -->
  <view class="diagnosis-results" wx:if="{{diagnosisData}}">
    <!-- 云函数状态 -->
    <view class="result-section card">
      <view class="section-header">
        <text class="section-title">云函数状态</text>
        <text class="status-badge {{diagnosisData.cloudFunction.status === 'running' ? 'success' : 'error'}}">
          {{diagnosisData.cloudFunction.status === 'running' ? '正常' : '异常'}}
        </text>
      </view>
      <view class="section-content">
        <text class="info-item">函数名称：{{diagnosisData.cloudFunction.name}}</text>
        <text class="info-item">检查时间：{{diagnosisData.timestamp}}</text>
      </view>
    </view>

    <!-- 数据库状态 -->
    <view class="result-section card">
      <view class="section-header">
        <text class="section-title">数据库状态</text>
        <text class="status-badge {{diagnosisData.database.status === 'connected' ? 'success' : 'error'}}">
          {{diagnosisData.database.status === 'connected' ? '已连接' : '连接失败'}}
        </text>
      </view>
      <view class="section-content">
        <text class="info-item">集合数量：{{diagnosisData.database.collections.length}}</text>
        <view class="collections-list">
          <text class="info-label">现有集合：</text>
          <text class="collection-item" wx:for="{{diagnosisData.database.collections}}" wx:key="*this">{{item}}</text>
        </view>
        <text class="error-message" wx:if="{{diagnosisData.database.error}}">错误：{{diagnosisData.database.error}}</text>
      </view>
    </view>

    <!-- 用户集合状态 -->
    <view class="result-section card">
      <view class="section-header">
        <text class="section-title">用户集合</text>
        <text class="status-badge {{diagnosisData.collections.users.exists ? 'success' : 'error'}}">
          {{diagnosisData.collections.users.exists ? '存在' : '不存在'}}
        </text>
      </view>
      <view class="section-content" wx:if="{{diagnosisData.collections.users.exists}}">
        <text class="info-item">用户数量：{{diagnosisData.collections.users.count}}</text>
        <view class="users-list" wx:if="{{diagnosisData.collections.users.data}}">
          <text class="info-label">用户列表：</text>
          <view class="user-item" wx:for="{{diagnosisData.collections.users.data}}" wx:key="username">
            <text class="username">{{item.username}}</text>
            <text class="role {{item.role === 'admin' ? 'admin' : 'user'}}">{{item.role}}</text>
          </view>
        </view>
        <text class="error-message" wx:if="{{diagnosisData.collections.users.error}}">错误：{{diagnosisData.collections.users.error}}</text>
      </view>
    </view>

    <!-- 管理员账号状态 -->
    <view class="result-section card">
      <view class="section-header">
        <text class="section-title">管理员账号</text>
        <text class="status-badge {{diagnosisData.users.admin.exists ? 'success' : 'error'}}">
          {{diagnosisData.users.admin.exists ? '存在' : '不存在'}}
        </text>
      </view>
      <view class="section-content">
        <text class="info-item">管理员数量：{{diagnosisData.users.admin.count || 0}}</text>
        <view class="admin-list" wx:if="{{diagnosisData.users.admin.usernames}}">
          <text class="info-label">管理员用户名：</text>
          <text class="admin-username" wx:for="{{diagnosisData.users.admin.usernames}}" wx:key="*this">{{item}}</text>
        </view>
        <text class="error-message" wx:if="{{diagnosisData.users.admin.error}}">错误：{{diagnosisData.users.admin.error}}</text>
      </view>
    </view>

    <!-- 权限状态 -->
    <view class="result-section card">
      <view class="section-header">
        <text class="section-title">数据库权限</text>
        <text class="status-badge {{diagnosisData.permissions.write ? 'success' : 'error'}}">
          {{diagnosisData.permissions.write ? '正常' : '异常'}}
        </text>
      </view>
      <view class="section-content">
        <text class="info-item">读权限：{{diagnosisData.permissions.read ? '正常' : '异常'}}</text>
        <text class="info-item">写权限：{{diagnosisData.permissions.write ? '正常' : '异常'}}</text>
        <text class="error-message" wx:if="{{diagnosisData.permissions.error}}">错误：{{diagnosisData.permissions.error}}</text>
      </view>
    </view>
  </view>

  <!-- 测试日志 -->
  <view class="test-logs card" wx:if="{{logs.length > 0}}">
    <view class="section-header">
      <text class="section-title">测试日志</text>
      <button class="clear-btn" bindtap="clearLogs">清空</button>
    </view>
    <view class="logs-content">
      <view class="log-item {{item.type}}" wx:for="{{logs}}" wx:key="time">
        <text class="log-time">{{item.time}}</text>
        <text class="log-message">{{item.message}}</text>
      </view>
    </view>
  </view>

  <!-- 解决方案建议 -->
  <view class="solutions card" wx:if="{{solutions.length > 0}}">
    <text class="section-title">解决方案建议</text>
    <view class="solution-list">
      <view class="solution-item {{item.type}}" wx:for="{{solutions}}" wx:key="title">
        <text class="solution-title">{{item.title}}</text>
        <text class="solution-desc">{{item.description}}</text>
        <button class="solution-btn" bindtap="applySolution" data-action="{{item.action}}" wx:if="{{item.action}}">
          {{item.actionText}}
        </button>
      </view>
    </view>
  </view>

  <!-- 手动测试区域 -->
  <view class="manual-test card">
    <text class="section-title">手动测试</text>
    <view class="test-section">
      <text class="test-label">测试管理员登录：</text>
      <view class="test-inputs">
        <input class="test-input" placeholder="用户名" value="admin" bindinput="onUsernameInput" />
        <input class="test-input" placeholder="密码" value="admin123" bindinput="onPasswordInput" />
        <button class="test-btn" bindtap="testAdminLogin" disabled="{{testing}}">测试登录</button>
      </view>
    </view>
  </view>
</view>

// debug.js
const app = getApp();

Page({
  data: {
    diagnosing: false,
    testing: false,
    initializing: false,
    diagnosisData: null,
    logs: [],
    solutions: [],
    testUsername: 'admin',
    testPassword: 'admin123'
  },

  onLoad() {
    this.addLog('诊断工具已启动', 'info');
  },

  // 运行完整诊断
  async runDiagnosis() {
    if (this.data.diagnosing) return;

    this.setData({ 
      diagnosing: true,
      diagnosisData: null,
      solutions: []
    });
    
    this.addLog('开始运行完整系统诊断...', 'info');

    try {
      const result = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'diagnoseSystem'
        }
      });

      console.log('诊断结果:', result);

      if (result.result.success) {
        this.setData({
          diagnosisData: result.result.data
        });
        this.addLog('系统诊断完成', 'success');
        this.analyzeDiagnosis(result.result.data);
      } else {
        this.addLog('诊断失败: ' + result.result.message, 'error');
        this.generateBasicSolutions();
      }
    } catch (error) {
      console.error('诊断失败:', error);
      this.addLog('诊断异常: ' + error.message, 'error');
      this.generateBasicSolutions();
    }

    this.setData({ diagnosing: false });
  },

  // 分析诊断结果并生成解决方案
  analyzeDiagnosis(data) {
    const solutions = [];

    // 检查数据库连接
    if (data.database.status !== 'connected') {
      solutions.push({
        type: 'error',
        title: '数据库连接失败',
        description: '无法连接到云数据库，请检查云开发环境配置',
        action: 'checkCloudConfig',
        actionText: '检查配置'
      });
    }

    // 检查 users 集合
    if (!data.collections.users || !data.collections.users.exists) {
      solutions.push({
        type: 'error',
        title: 'users 集合不存在',
        description: '需要创建 users 集合并初始化用户数据',
        action: 'forceInitUsers',
        actionText: '立即初始化'
      });
    }

    // 检查管理员账号
    if (!data.users.admin || !data.users.admin.exists) {
      solutions.push({
        type: 'warning',
        title: '管理员账号不存在',
        description: '需要创建管理员账号才能正常登录',
        action: 'forceInitUsers',
        actionText: '创建管理员'
      });
    }

    // 检查权限
    if (data.permissions.error || !data.permissions.write) {
      solutions.push({
        type: 'error',
        title: '数据库权限异常',
        description: '云函数没有足够的数据库操作权限',
        action: 'checkPermissions',
        actionText: '检查权限'
      });
    }

    // 如果一切正常
    if (solutions.length === 0) {
      solutions.push({
        type: 'info',
        title: '系统状态正常',
        description: '所有组件运行正常，可以正常使用管理员登录功能',
        action: 'testAdminLogin',
        actionText: '测试登录'
      });
    }

    this.setData({ solutions });
  },

  // 生成基础解决方案
  generateBasicSolutions() {
    const solutions = [
      {
        type: 'error',
        title: '云函数调用失败',
        description: '无法调用云函数，请检查云函数是否正确部署',
        action: 'checkCloudFunction',
        actionText: '检查云函数'
      },
      {
        type: 'warning',
        title: '强制初始化用户数据',
        description: '尝试强制创建管理员账号',
        action: 'forceInitUsers',
        actionText: '强制初始化'
      }
    ];

    this.setData({ solutions });
  },

  // 测试云函数
  async testCloudFunction() {
    if (this.data.testing) return;

    this.setData({ testing: true });
    this.addLog('测试云函数连接...', 'info');

    try {
      const result = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'checkDatabase'
        }
      });

      console.log('云函数测试结果:', result);

      if (result.result) {
        this.addLog('云函数调用成功', 'success');
        this.addLog('返回数据: ' + JSON.stringify(result.result), 'info');
      } else {
        this.addLog('云函数返回异常', 'error');
      }
    } catch (error) {
      console.error('云函数测试失败:', error);
      this.addLog('云函数调用失败: ' + error.message, 'error');
    }

    this.setData({ testing: false });
  },

  // 强制初始化用户
  async forceInitUsers() {
    if (this.data.initializing) return;

    this.setData({ initializing: true });
    this.addLog('开始强制初始化用户数据...', 'info');

    try {
      const result = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'initTestUsers'
        }
      });

      console.log('初始化结果:', result);

      if (result.result.success) {
        this.addLog('用户数据初始化成功', 'success');
        this.addLog(result.result.message, 'success');
        
        // 重新运行诊断
        setTimeout(() => {
          this.runDiagnosis();
        }, 1000);
      } else {
        this.addLog('初始化失败: ' + result.result.message, 'error');
        if (result.result.error) {
          this.addLog('详细错误: ' + result.result.error, 'error');
        }
      }
    } catch (error) {
      console.error('初始化失败:', error);
      this.addLog('初始化异常: ' + error.message, 'error');
    }

    this.setData({ initializing: false });
  },

  // 测试管理员登录
  async testAdminLogin() {
    if (this.data.testing) return;

    this.setData({ testing: true });
    this.addLog(`测试管理员登录: ${this.data.testUsername}`, 'info');

    try {
      const result = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'adminLogin',
          username: this.data.testUsername,
          password: this.data.testPassword
        }
      });

      console.log('登录测试结果:', result);

      if (result.result.success) {
        this.addLog('管理员登录测试成功', 'success');
        this.addLog('用户信息: ' + JSON.stringify(result.result.data), 'success');
      } else {
        this.addLog('登录测试失败: ' + result.result.message, 'error');
      }
    } catch (error) {
      console.error('登录测试失败:', error);
      this.addLog('登录测试异常: ' + error.message, 'error');
    }

    this.setData({ testing: false });
  },

  // 应用解决方案
  applySolution(e) {
    const action = e.currentTarget.dataset.action;
    
    switch (action) {
      case 'forceInitUsers':
        this.forceInitUsers();
        break;
      case 'testAdminLogin':
        this.testAdminLogin();
        break;
      case 'checkCloudFunction':
        this.testCloudFunction();
        break;
      case 'checkCloudConfig':
        this.showCloudConfigHelp();
        break;
      case 'checkPermissions':
        this.showPermissionsHelp();
        break;
      default:
        this.addLog('未知操作: ' + action, 'error');
    }
  },

  // 显示云配置帮助
  showCloudConfigHelp() {
    wx.showModal({
      title: '云开发配置检查',
      content: '请检查以下配置：\n\n1. 云开发环境是否已开通\n2. app.js 中的环境ID是否正确\n3. 云函数是否已正确部署\n4. 网络连接是否正常',
      showCancel: false
    });
  },

  // 显示权限帮助
  showPermissionsHelp() {
    wx.showModal({
      title: '数据库权限检查',
      content: '请在云开发控制台检查：\n\n1. 数据库权限设置\n2. 云函数是否有数据库访问权限\n3. 安全规则配置是否正确',
      showCancel: false
    });
  },

  // 输入事件
  onUsernameInput(e) {
    this.setData({
      testUsername: e.detail.value
    });
  },

  onPasswordInput(e) {
    this.setData({
      testPassword: e.detail.value
    });
  },

  // 添加日志
  addLog(message, type = 'info') {
    const now = new Date();
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
    
    const logs = this.data.logs;
    logs.unshift({
      time,
      message,
      type
    });

    // 只保留最近50条日志
    if (logs.length > 50) {
      logs.splice(50);
    }

    this.setData({ logs });
  },

  // 清空日志
  clearLogs() {
    this.setData({ logs: [] });
  }
});

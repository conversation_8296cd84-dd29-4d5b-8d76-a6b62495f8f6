// 中药材图片测试页面
Page({
  data: {
    testImages: [
      { id: '1', name: '人参', path: '/images/中药材/人参.jpg', status: 'testing' },
      { id: '2', name: '黄芪', path: '/images/中药材/黄芪.jpg', status: 'testing' },
      { id: '3', name: '甘草', path: '/images/中药材/甘草.jpg', status: 'testing' },
      { id: '4', name: '白术', path: '/images/中药材/白术.jpg', status: 'testing' },
      { id: '5', name: '党参', path: '/images/中药材/党参.jpg', status: 'testing' },
      { id: '6', name: '当归', path: '/images/中药材/当归.jpg', status: 'testing' },
      { id: '7', name: '枸杞子', path: '/images/中药材/枸杞子.jpg', status: 'testing' },
      { id: '8', name: '熟地黄', path: '/images/中药材/熟地黄.jpg', status: 'testing' },
      { id: '9', name: '白芍', path: '/images/中药材/白芍.jpg', status: 'testing' },
      { id: '10', name: '何首乌', path: '/images/中药材/何首乌.jpg', status: 'testing' },
      { id: '11', name: '金银花', path: '/images/中药材/金银花.jpg', status: 'testing' },
      { id: '12', name: '连翘', path: '/images/中药材/连翘.jpg', status: 'testing' },
      { id: '13', name: '板蓝根', path: '/images/中药材/板蓝根.jpg', status: 'testing' },
      { id: '14', name: '黄连', path: '/images/中药材/黄连.jpg', status: 'testing' },
      { id: '15', name: '黄芩', path: '/images/中药材/黄芩.jpg', status: 'testing' },
      { id: '16', name: '麻黄', path: '/images/中药材/麻黄.jpg', status: 'testing' },
      { id: '17', name: '桂枝', path: '/images/中药材/桂枝.jpg', status: 'testing' },
      { id: '18', name: '薄荷', path: '/images/中药材/薄荷.jpg', status: 'testing' },
      { id: '19', name: '陈皮', path: '/images/中药材/陈皮.jpg', status: 'testing' },
      { id: '20', name: '半夏', path: '/images/中药材/半夏.jpg', status: 'testing' },
      { id: '21', name: '丹参', path: '/images/中药材/丹参.jpg', status: 'testing' },
      { id: '22', name: '红花', path: '/images/中药材/红花.jpg', status: 'testing' },
      { id: '23', name: '酸枣仁', path: '/images/中药材/酸枣仁.jpg', status: 'testing' },
      { id: '24', name: '远志', path: '/images/中药材/远志.jpg', status: 'testing' },
      { id: '25', name: '山楂', path: '/images/中药材/山楂.jpg', status: 'testing' },
      { id: '26', name: '川芎', path: '/images/中药材/川芎.jpg', status: 'testing' },
      { id: '27', name: '天麻', path: '/images/中药材/天麻.jpg', status: 'testing' }
    ],
    successCount: 0,
    errorCount: 0,
    testingCount: 0
  },

  onLoad: function() {
    console.log('中药材图片测试页面加载');
    this.updateCounts();
  },

  // 图片加载成功
  onImageLoad: function(e) {
    var id = e.currentTarget.dataset.id;
    var testImages = this.data.testImages;
    
    for (var i = 0; i < testImages.length; i++) {
      if (testImages[i].id === id) {
        testImages[i].status = 'success';
        break;
      }
    }
    
    this.setData({ testImages: testImages });
    this.updateCounts();
    console.log('图片加载成功:', id);
  },

  // 图片加载失败
  onImageError: function(e) {
    var id = e.currentTarget.dataset.id;
    var testImages = this.data.testImages;
    
    for (var i = 0; i < testImages.length; i++) {
      if (testImages[i].id === id) {
        testImages[i].status = 'error';
        break;
      }
    }
    
    this.setData({ testImages: testImages });
    this.updateCounts();
    console.log('图片加载失败:', id, testImages.find(function(item) { return item.id === id; }).path);
  },

  // 更新统计数据
  updateCounts: function() {
    var testImages = this.data.testImages;
    var successCount = 0;
    var errorCount = 0;
    var testingCount = 0;
    
    for (var i = 0; i < testImages.length; i++) {
      if (testImages[i].status === 'success') {
        successCount++;
      } else if (testImages[i].status === 'error') {
        errorCount++;
      } else {
        testingCount++;
      }
    }
    
    this.setData({
      successCount: successCount,
      errorCount: errorCount,
      testingCount: testingCount
    });
  },

  // 重新测试
  retryTest: function() {
    var testImages = this.data.testImages.map(function(item) {
      return Object.assign({}, item, { status: 'testing' });
    });
    
    this.setData({ testImages: testImages });
    this.updateCounts();
  },

  // 跳转到中药材页面
  goToMedicines: function() {
    wx.switchTab({
      url: '/pages/medicines/medicines'
    });
  },

  // 返回首页
  goHome: function() {
    wx.navigateBack();
  }
});

<!--中药材图片测试页面-->
<view class="container">
  <view class="header">
    <text class="title">🌿 中药材图片测试</text>
    <text class="subtitle">检测中药材图片是否能正常加载</text>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item total">
        <text class="stat-number">{{testImages.length}}</text>
        <text class="stat-label">总数</text>
      </view>
      <view class="stat-item success">
        <text class="stat-number">{{successCount}}</text>
        <text class="stat-label">成功</text>
      </view>
      <view class="stat-item error">
        <text class="stat-number">{{errorCount}}</text>
        <text class="stat-label">失败</text>
      </view>
      <view class="stat-item testing">
        <text class="stat-number">{{testingCount}}</text>
        <text class="stat-label">测试中</text>
      </view>
    </view>
  </view>

  <!-- 图片测试网格 -->
  <view class="test-section">
    <view class="section-title">
      <text class="title-icon">🧪</text>
      <text class="title-text">图片测试结果</text>
    </view>
    
    <view class="image-grid">
      <view class="image-item" wx:for="{{testImages}}" wx:key="id">
        <view class="image-container">
          <image 
            class="test-image" 
            src="{{item.path}}" 
            mode="aspectFill"
            bindload="onImageLoad"
            binderror="onImageError"
            data-id="{{item.id}}">
          </image>
          <view class="image-overlay">
            <text class="status-icon {{item.status}}">
              {{item.status === 'success' ? '✅' : item.status === 'error' ? '❌' : '⏳'}}
            </text>
          </view>
          <view class="placeholder" wx:if="{{item.status === 'error'}}">
            <text class="placeholder-icon">🌿</text>
          </view>
        </view>
        <view class="image-info">
          <text class="image-name">{{item.name}}</text>
          <text class="image-id">ID: {{item.id}}</text>
          <text class="image-path">{{item.path}}</text>
          <text class="image-status {{item.status}}">
            {{item.status === 'success' ? '加载成功' : item.status === 'error' ? '加载失败' : '测试中...'}}
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="retry-btn" bindtap="retryTest">
      <text class="btn-icon">🔄</text>
      <text class="btn-text">重新测试</text>
    </button>
    <button class="medicines-btn" bindtap="goToMedicines">
      <text class="btn-icon">🌿</text>
      <text class="btn-text">中药材页面</text>
    </button>
    <button class="back-btn" bindtap="goHome">
      <text class="btn-icon">🏠</text>
      <text class="btn-text">返回首页</text>
    </button>
  </view>
</view>

<!--模块修复测试页面-->
<view class="container">
  <view class="header">
    <text class="title">🔧 模块修复测试</text>
    <text class="subtitle">测试修复后的页面是否能正常加载</text>
  </view>

  <view class="test-section">
    <view class="section-title">
      <text class="title-icon">🧪</text>
      <text class="title-text">测试结果</text>
      <text class="loading-text" wx:if="{{testing}}">测试中...</text>
    </view>
    
    <view class="test-list">
      <view class="test-item" wx:for="{{testResults}}" wx:key="name">
        <view class="test-info">
          <text class="test-name">{{item.name}}</text>
          <text class="test-url">{{item.url}}</text>
        </view>
        
        <view class="test-status">
          <text class="status-icon {{item.status}}">
            {{item.status === 'success' ? '✅' : item.status === 'testing' ? '⏳' : '❌'}}
          </text>
          <text class="status-text">{{item.message || item.status}}</text>
        </view>
        
        <button class="test-btn" bindtap="testPage" data-url="{{item.url}}">
          测试
        </button>
      </view>
    </view>
  </view>

  <view class="info-section">
    <view class="section-title">
      <text class="title-icon">ℹ️</text>
      <text class="title-text">修复说明</text>
    </view>
    
    <view class="info-content">
      <view class="info-item">
        <text class="info-icon">🔧</text>
        <text class="info-text">修复了ES6语法兼容性问题</text>
      </view>
      <view class="info-item">
        <text class="info-icon">🔧</text>
        <text class="info-text">替换了箭头函数为普通函数</text>
      </view>
      <view class="info-item">
        <text class="info-icon">🔧</text>
        <text class="info-text">修复了扩展运算符语法</text>
      </view>
      <view class="info-item">
        <text class="info-icon">🔧</text>
        <text class="info-text">替换了includes方法为indexOf</text>
      </view>
    </view>
  </view>

  <view class="action-section">
    <button class="back-btn" bindtap="goHome">
      <text class="btn-icon">🏠</text>
      <text class="btn-text">返回首页</text>
    </button>
  </view>
</view>

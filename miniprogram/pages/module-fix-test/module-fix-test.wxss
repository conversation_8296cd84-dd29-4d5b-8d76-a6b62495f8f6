/* 模块修复测试页面样式 */
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 8rpx;
}

.subtitle {
  font-size: 20rpx;
  color: #666;
}

.test-section,
.info-section,
.action-section {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-icon {
  font-size: 24rpx;
}

.title-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
}

.loading-text {
  font-size: 18rpx;
  color: #999;
  margin-left: auto;
}

.test-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.test-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 15rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.test-item:hover {
  border-color: #8B4513;
}

.test-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.test-name {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
}

.test-url {
  font-size: 16rpx;
  color: #999;
  font-family: monospace;
  background: rgba(139, 69, 19, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  align-self: flex-start;
}

.test-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  min-width: 80rpx;
}

.status-icon {
  font-size: 24rpx;
}

.status-icon.success {
  color: #228B22;
}

.status-icon.testing {
  color: #FFA500;
}

.status-icon.error {
  color: #E74C3C;
}

.status-text {
  font-size: 16rpx;
  color: #666;
  text-align: center;
}

.test-btn {
  width: 100rpx;
  height: 60rpx;
  border-radius: 30rpx;
  font-size: 20rpx;
  font-weight: bold;
  border: none;
  background: linear-gradient(135deg, #228B22, #32CD32);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(34, 139, 34, 0.3);
}

.test-btn:active {
  transform: scale(0.95);
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.info-icon {
  font-size: 20rpx;
  color: #228B22;
  width: 30rpx;
  text-align: center;
}

.info-text {
  font-size: 20rpx;
  color: #666;
  flex: 1;
  line-height: 1.4;
}

.back-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 18rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  border: none;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.3);
  transition: all 0.3s ease;
}

.back-btn:active {
  transform: translateY(-2rpx);
  opacity: 0.9;
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 22rpx;
}

/* 动画效果 */
.test-item {
  animation: slideInUp 0.3s ease-out;
}

.test-item:nth-child(1) { animation-delay: 0.1s; }
.test-item:nth-child(2) { animation-delay: 0.2s; }
.test-item:nth-child(3) { animation-delay: 0.3s; }
.test-item:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 中药图鉴详情页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 药材头部 */
.herb-header {
  background: white;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.herb-image-section {
  position: relative;
  padding: 30rpx;
  text-align: center;
}

.main-image {
  width: 200rpx;
  height: 200rpx;
  margin: 0 auto 20rpx;
  background: linear-gradient(135deg, #228B22, #32CD32);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(34, 139, 34, 0.3);
}

.image-placeholder {
  font-size: 80rpx;
  color: white;
}

.favorite-btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.favorite-btn.favorited {
  background: rgba(255, 182, 193, 0.9);
}

.favorite-icon {
  font-size: 24rpx;
}

.herb-info-section {
  padding: 0 30rpx 30rpx;
}

.herb-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 8rpx;
  text-align: center;
}

.latin-name {
  font-size: 20rpx;
  color: #666;
  font-style: italic;
  display: block;
  text-align: center;
  margin-bottom: 15rpx;
}

.herb-category {
  font-size: 22rpx;
  color: #228B22;
  background: rgba(34, 139, 34, 0.1);
  padding: 6rpx 15rpx;
  border-radius: 15rpx;
  display: inline-block;
  margin-bottom: 15rpx;
}

.herb-alias {
  font-size: 20rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.herb-description {
  font-size: 22rpx;
  color: #333;
  line-height: 1.6;
  display: block;
}

/* 详情标签页 */
.detail-tabs {
  background: white;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.08);
}

.tab-bar {
  display: flex;
  background: #f8f8f8;
  border-radius: 15rpx 15rpx 0 0;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  padding: 20rpx 10rpx;
  text-align: center;
  background: #f8f8f8;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: white;
  border-bottom: 3rpx solid #228B22;
}

.tab-text {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: #228B22;
  font-weight: bold;
}

.tab-content {
  padding: 25rpx 30rpx;
  min-height: 400rpx;
}

/* 信息区块 */
.info-section {
  margin-bottom: 25rpx;
}

.section-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 15rpx;
  border-left: 4rpx solid #228B22;
  padding-left: 15rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 10rpx;
}

.info-label {
  font-size: 20rpx;
  color: #666;
  min-width: 80rpx;
  font-weight: 500;
}

.info-value {
  font-size: 20rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

.info-text {
  font-size: 20rpx;
  color: #333;
  line-height: 1.6;
  text-align: justify;
}

.effect-text {
  font-size: 22rpx;
  color: #228B22;
  font-weight: bold;
  line-height: 1.6;
  background: rgba(34, 139, 34, 0.05);
  padding: 15rpx;
  border-radius: 10rpx;
  border-left: 4rpx solid #228B22;
}

.warning-text {
  font-size: 20rpx;
  color: #E74C3C;
  line-height: 1.6;
  background: rgba(231, 76, 60, 0.05);
  padding: 15rpx;
  border-radius: 10rpx;
  border-left: 4rpx solid #E74C3C;
}

/* 性味归经网格 */
.property-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.property-item {
  background: #f8f8f8;
  border-radius: 15rpx;
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.property-item:nth-child(1) {
  border-color: rgba(255, 99, 71, 0.3);
  background: rgba(255, 99, 71, 0.05);
}

.property-item:nth-child(2) {
  border-color: rgba(34, 139, 34, 0.3);
  background: rgba(34, 139, 34, 0.05);
}

.property-item:nth-child(3) {
  border-color: rgba(65, 105, 225, 0.3);
  background: rgba(65, 105, 225, 0.05);
}

.property-item:nth-child(4) {
  border-color: rgba(255, 165, 0, 0.3);
  background: rgba(255, 165, 0, 0.05);
}

.property-label {
  font-size: 18rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.property-value {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
}

/* 相关推荐 */
.related-section {
  background: white;
  margin-bottom: 20rpx;
  padding: 25rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.08);
}

.related-scroll {
  white-space: nowrap;
  margin-top: 15rpx;
}

.related-item {
  display: inline-block;
  width: 120rpx;
  margin-right: 20rpx;
  text-align: center;
  vertical-align: top;
}

.related-image {
  display: block;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #228B22, #32CD32);
  border-radius: 15rpx;
  font-size: 40rpx;
  color: white;
  line-height: 80rpx;
  margin: 0 auto 8rpx;
}

.related-name {
  font-size: 18rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 4rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-category {
  font-size: 16rpx;
  color: #228B22;
  background: rgba(34, 139, 34, 0.1);
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  display: inline-block;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
  z-index: 1000;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 22rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.favorite-action-btn {
  background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
  color: #8B4513;
  box-shadow: 0 4rpx 16rpx rgba(255, 182, 193, 0.3);
}

.favorite-action-btn.favorited {
  background: linear-gradient(135deg, #FF69B4, #FF1493);
  color: white;
}

.purchase-btn {
  background: linear-gradient(135deg, #228B22, #32CD32);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(34, 139, 34, 0.3);
}

.action-btn:active {
  transform: translateY(-2rpx);
  opacity: 0.9;
}

.btn-icon {
  font-size: 18rpx;
}

.btn-text {
  font-size: 22rpx;
}

.bottom-placeholder {
  height: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f5f5f5;
}

.loading-content {
  text-align: center;
}

.loading-icon {
  font-size: 60rpx;
  display: block;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .property-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-bar {
    flex-wrap: wrap;
  }
  
  .tab-item {
    min-width: 120rpx;
  }
  
  .bottom-actions {
    flex-direction: column;
    gap: 15rpx;
  }
}

/* 动画效果 */
.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

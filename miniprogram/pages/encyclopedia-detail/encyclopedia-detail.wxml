<!--中药图鉴详情页面-->
<view class="container" wx:if="{{!loading}}">
  <!-- 药材头部信息 -->
  <view class="herb-header">
    <view class="herb-image-section">
      <view class="main-image">
        <image class="encyclopedia-detail-image" src="{{encyclopedia.imageUrl}}" mode="aspectFill" wx:if="{{encyclopedia.imageUrl && !imageError}}" binderror="onImageError" bindload="onImageLoad"></image>
        <view class="image-placeholder" wx:if="{{!encyclopedia.imageUrl || imageError}}">
          <text class="placeholder-icon">📖</text>
          <text class="placeholder-text">{{encyclopedia.name}}</text>
        </view>
      </view>
      <button class="favorite-btn {{isFavorite ? 'favorited' : ''}}" bindtap="toggleFavorite">
        <text class="favorite-icon">{{isFavorite ? '❤️' : '🤍'}}</text>
      </button>
    </view>
    
    <view class="herb-info-section">
      <text class="herb-name">{{encyclopedia.name}}</text>
      <text class="latin-name">{{encyclopedia.latinName}}</text>
      <text class="herb-category">{{encyclopedia.category}}</text>
      <text class="herb-alias">别名：{{encyclopedia.alias}}</text>
      <text class="herb-description">{{encyclopedia.description}}</text>
    </view>
  </view>

  <!-- 详情标签页 -->
  <view class="detail-tabs">
    <view class="tab-bar">
      <view class="tab-item {{currentTab === index ? 'active' : ''}}" 
            wx:for="{{tabs}}" wx:key="*this" wx:for-index="index"
            bindtap="switchTab" data-index="{{index}}">
        <text class="tab-text">{{item}}</text>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="tab-content" wx:if="{{currentTab === 0}}">
      <view class="info-section">
        <view class="section-title">植物学信息</view>
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">科属：</text>
            <text class="info-value">{{encyclopedia.family}} {{encyclopedia.genus}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">产地：</text>
            <text class="info-value">{{encyclopedia.origin}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">生境：</text>
            <text class="info-value">{{encyclopedia.habitat}}</text>
          </view>
        </view>
      </view>
      
      <view class="info-section">
        <view class="section-title">形态特征</view>
        <text class="info-text">{{encyclopedia.morphology}}</text>
      </view>
    </view>

    <!-- 性味归经 -->
    <view class="tab-content" wx:if="{{currentTab === 1}}">
      <view class="property-grid">
        <view class="property-item">
          <text class="property-label">性</text>
          <text class="property-value">{{encyclopedia.nature}}</text>
        </view>
        <view class="property-item">
          <text class="property-label">味</text>
          <text class="property-value">{{encyclopedia.taste}}</text>
        </view>
        <view class="property-item">
          <text class="property-label">归经</text>
          <text class="property-value">{{encyclopedia.meridian}}</text>
        </view>
        <view class="property-item">
          <text class="property-label">毒性</text>
          <text class="property-value">{{encyclopedia.toxicity}}</text>
        </view>
      </view>
    </view>

    <!-- 功效主治 -->
    <view class="tab-content" wx:if="{{currentTab === 2}}">
      <view class="info-section">
        <view class="section-title">主要功效</view>
        <text class="effect-text">{{encyclopedia.effect}}</text>
      </view>
      
      <view class="info-section">
        <view class="section-title">临床应用</view>
        <text class="info-text">{{encyclopedia.clinicalApplication}}</text>
      </view>
    </view>

    <!-- 用法用量 -->
    <view class="tab-content" wx:if="{{currentTab === 3}}">
      <view class="info-section">
        <view class="section-title">用法用量</view>
        <text class="info-text">{{encyclopedia.usage}}</text>
      </view>
      
      <view class="info-section">
        <view class="section-title">炮制方法</view>
        <text class="info-text">{{encyclopedia.processing}}</text>
      </view>
      
      <view class="info-section">
        <view class="section-title">使用注意</view>
        <text class="warning-text">{{encyclopedia.contraindications}}</text>
      </view>
    </view>

    <!-- 现代研究 -->
    <view class="tab-content" wx:if="{{currentTab === 4}}">
      <view class="info-section">
        <view class="section-title">化学成分</view>
        <text class="info-text">{{encyclopedia.chemicalComposition}}</text>
      </view>
      
      <view class="info-section">
        <view class="section-title">药理作用</view>
        <text class="info-text">{{encyclopedia.pharmacology}}</text>
      </view>
      
      <view class="info-section">
        <view class="section-title">临床研究</view>
        <text class="info-text">{{encyclopedia.clinicalResearch}}</text>
      </view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section" wx:if="{{relatedItems.length > 0}}">
    <view class="section-title">相关药材</view>
    <scroll-view class="related-scroll" scroll-x="true">
      <view class="related-item" wx:for="{{relatedItems}}" wx:key="id"
            bindtap="viewRelated" data-id="{{item.id}}">
        <text class="related-image">{{item.imageUrl}}</text>
        <text class="related-name">{{item.name}}</text>
        <text class="related-category">{{item.category}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="action-btn favorite-action-btn {{isFavorite ? 'favorited' : ''}}" bindtap="toggleFavorite">
      <text class="btn-icon">{{isFavorite ? '❤️' : '🤍'}}</text>
      <text class="btn-text">{{isFavorite ? '已收藏' : '收藏'}}</text>
    </button>
    <button class="action-btn purchase-btn" bindtap="goToMedicine">
      <text class="btn-icon">🛒</text>
      <text class="btn-text">查看商品</text>
    </button>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-content">
    <text class="loading-icon">⏳</text>
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 占位，防止底部按钮遮挡内容 -->
<view class="bottom-placeholder"></view>

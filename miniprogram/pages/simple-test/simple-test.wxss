/* 简单测试页面样式 */
.container {
  padding: 30rpx;
  background: linear-gradient(135deg, #f5f2e8, #faf8f3);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(139, 69, 19, 0.1);
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 15rpx;
}

.status {
  display: block;
  font-size: 24rpx;
  color: #666;
  padding: 10rpx 20rpx;
  background: #f0f0f0;
  border-radius: 10rpx;
}

.test-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(139, 69, 19, 0.1);
}

.test-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 20rpx;
  text-align: center;
}

.image-container {
  width: 300rpx;
  height: 300rpx;
  margin: 0 auto 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.3);
  position: relative;
}

.test-image {
  width: 100%;
  height: 100%;
}

.placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}

.placeholder-icon {
  font-size: 80rpx;
  color: white;
}

.placeholder-text {
  font-size: 24rpx;
  color: white;
  opacity: 0.9;
}

.image-path {
  display: block;
  font-size: 20rpx;
  color: #666;
  text-align: center;
  word-break: break-all;
}

.actions {
  margin-bottom: 30rpx;
}

.action-btn {
  width: 100%;
  margin-bottom: 15rpx;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 26rpx;
  border: none;
}

.action-btn.primary {
  background: #8B4513;
  color: white;
}

.action-btn.secondary {
  background: #A0522D;
  color: white;
}

.action-btn.home {
  background: #228B22;
  color: white;
}

.info {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(139, 69, 19, 0.1);
}

.info-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 20rpx;
  text-align: center;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
  padding: 10rpx 0;
}

.info-icon {
  font-size: 24rpx;
  margin-right: 15rpx;
  margin-top: 2rpx;
}

.info-text {
  flex: 1;
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
}

<!--简单测试页面-->
<view class="container">
  <view class="header">
    <text class="title">🔧 图片修复验证</text>
    <text class="status">{{testStatus}}</text>
  </view>

  <view class="test-section">
    <view class="test-title">人参图片测试</view>
    <view class="image-container">
      <image 
        class="test-image" 
        src="{{testImage}}" 
        mode="aspectFill"
        bindload="onImageLoad"
        binderror="onImageError"
        wx:if="{{!imageError}}"
      />
      <view class="placeholder" wx:if="{{imageError}}">
        <text class="placeholder-icon">🌿</text>
        <text class="placeholder-text">人参</text>
      </view>
    </view>
    <text class="image-path">图片路径: {{testImage}}</text>
  </view>

  <view class="actions">
    <button class="action-btn primary" bindtap="goToMedicineDetail">
      测试中药材详情页
    </button>
    <button class="action-btn secondary" bindtap="goToMedicineList">
      测试中药材列表页
    </button>
    <button class="action-btn home" bindtap="goHome">
      返回首页
    </button>
  </view>

  <view class="info">
    <view class="info-title">修复说明</view>
    <view class="info-item">
      <text class="info-icon">✅</text>
      <text class="info-text">修复了详情页面显示图片路径文本的问题</text>
    </view>
    <view class="info-item">
      <text class="info-icon">✅</text>
      <text class="info-text">现在会显示真实的中药材图片</text>
    </view>
    <view class="info-item">
      <text class="info-icon">✅</text>
      <text class="info-text">图片加载失败时显示美观的占位符</text>
    </view>
  </view>
</view>

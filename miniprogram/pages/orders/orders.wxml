<!--orders.wxml-->
<view class="container">
  <!-- 订单状态筛选 -->
  <view class="status-filter">
    <scroll-view class="status-scroll" scroll-x="true">
      <view class="status-item {{selectedStatus === '' ? 'active' : ''}}" bindtap="filterByStatus" data-status="">
        全部
      </view>
      <view class="status-item {{selectedStatus === item.value ? 'active' : ''}}" 
            wx:for="{{statusOptions}}" wx:key="value" 
            bindtap="filterByStatus" data-status="{{item.value}}">
        {{item.label}}
      </view>
    </scroll-view>
  </view>

  <!-- 订单列表 -->
  <view class="order-list" wx:if="{{orderList.length > 0}}">
    <view class="order-item" wx:for="{{orderList}}" wx:key="id" bindtap="viewOrderDetail" data-id="{{item.id}}">
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="order-info">
          <text class="order-number">订单号：{{item.id}}</text>
          <text class="order-time">{{item.createTime}}</text>
        </view>
        <view class="order-status">
          <text class="status-text {{item.status}}">{{item.statusText}}</text>
        </view>
      </view>

      <!-- 商品列表 -->
      <view class="order-goods">
        <view class="goods-item" wx:for="{{item.items}}" wx:key="id" wx:for-item="goods">
          <image class="goods-image" src="{{goods.imageUrl}}" mode="aspectFill"></image>
          <view class="goods-info">
            <text class="goods-name">{{goods.name}}</text>
            <text class="goods-spec" wx:if="{{goods.spec}}">{{goods.spec}}</text>
            <view class="goods-price-row">
              <text class="goods-price">¥{{goods.price}}</text>
              <text class="goods-quantity">×{{goods.quantity}}</text>
            </view>
          </view>
        </view>
        <!-- 更多商品提示 -->
        <view class="more-goods" wx:if="{{item.items.length > 2}}">
          <text class="more-text">共{{item.items.length}}件商品</text>
        </view>
      </view>

      <!-- 订单金额 -->
      <view class="order-amount">
        <text class="amount-label">实付款：</text>
        <text class="amount-value">¥{{item.totalAmount}}</text>
      </view>

      <!-- 订单操作 -->
      <view class="order-actions">
        <button class="action-btn secondary" bindtap="contactService" data-id="{{item.id}}" catchtap="stopPropagation">
          联系客服
        </button>
        
        <!-- 待付款状态 -->
        <block wx:if="{{item.status === 'pending_payment'}}">
          <button class="action-btn outline" bindtap="cancelOrder" data-id="{{item.id}}" catchtap="stopPropagation">
            取消订单
          </button>
          <button class="action-btn primary" bindtap="payOrder" data-id="{{item.id}}" catchtap="stopPropagation">
            立即付款
          </button>
        </block>
        
        <!-- 待发货状态 -->
        <block wx:elif="{{item.status === 'paid'}}">
          <button class="action-btn outline" bindtap="refundOrder" data-id="{{item.id}}" catchtap="stopPropagation">
            申请退款
          </button>
        </block>
        
        <!-- 待收货状态 -->
        <block wx:elif="{{item.status === 'shipped'}}">
          <button class="action-btn outline" bindtap="viewLogistics" data-id="{{item.id}}" catchtap="stopPropagation">
            查看物流
          </button>
          <button class="action-btn primary" bindtap="confirmReceive" data-id="{{item.id}}" catchtap="stopPropagation">
            确认收货
          </button>
        </block>
        
        <!-- 已完成状态 -->
        <block wx:elif="{{item.status === 'completed'}}">
          <button class="action-btn outline" bindtap="buyAgain" data-id="{{item.id}}" catchtap="stopPropagation">
            再次购买
          </button>
          <button class="action-btn primary" bindtap="evaluateOrder" data-id="{{item.id}}" catchtap="stopPropagation">
            评价
          </button>
        </block>
        
        <!-- 已取消状态 -->
        <block wx:elif="{{item.status === 'cancelled'}}">
          <button class="action-btn primary" bindtap="buyAgain" data-id="{{item.id}}" catchtap="stopPropagation">
            再次购买
          </button>
        </block>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <image class="empty-image" src="/images/empty-order.png" mode="aspectFit"></image>
    <text class="empty-text">{{selectedStatus ? '暂无相关订单' : '还没有订单'}}</text>
    <text class="empty-desc">{{selectedStatus ? '换个状态看看吧' : '快去挑选心仪的商品吧'}}</text>
    <button class="btn btn-primary" bindtap="goShopping" wx:if="{{!selectedStatus}}">去购物</button>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && orderList.length > 0}}">
    <button class="btn btn-outline" bindtap="loadMore" disabled="{{loading}}">
      {{loading ? '加载中...' : '加载更多'}}
    </button>
  </view>
</view>

<!-- 取消订单确认弹窗 -->
<view class="modal-overlay" wx:if="{{showCancelModal}}" bindtap="hideCancelModal">
  <view class="modal-content cancel-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">取消订单</text>
      <text class="modal-close" bindtap="hideCancelModal">×</text>
    </view>
    <view class="modal-body">
      <text class="cancel-tip">请选择取消原因：</text>
      <radio-group bindchange="onCancelReasonChange">
        <label class="cancel-reason" wx:for="{{cancelReasons}}" wx:key="*this">
          <radio value="{{item}}" />
          <text class="reason-text">{{item}}</text>
        </label>
      </radio-group>
    </view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="hideCancelModal">取消</button>
      <button class="btn btn-primary" bindtap="confirmCancel">确定</button>
    </view>
  </view>
</view>

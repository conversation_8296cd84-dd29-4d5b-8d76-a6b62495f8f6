// orders.js
const app = getApp();

Page({
  data: {
    selectedStatus: '',
    statusOptions: [
      { value: 'pending_payment', label: '待付款' },
      { value: 'paid', label: '待发货' },
      { value: 'shipped', label: '待收货' },
      { value: 'completed', label: '已完成' },
      { value: 'cancelled', label: '已取消' }
    ],
    orderList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 取消订单
    showCancelModal: false,
    cancelOrderId: '',
    cancelReason: '',
    cancelReasons: [
      '不想要了',
      '商品信息填错了',
      '地址信息填错了',
      '商品降价了',
      '其他原因'
    ]
  },

  onLoad(options) {
    // 检查是否传入了状态参数
    if (options.status) {
      this.setData({
        selectedStatus: options.status
      });
    }
    
    this.loadOrders();
  },

  onShow() {
    // 每次显示页面时刷新订单列表
    this.loadOrders(true);
  },

  // 加载订单列表
  async loadOrders(refresh = true) {
    if (this.data.loading) return;

    // 检查登录状态
    if (!app.globalData.userInfo) {
      wx.showModal({
        title: '请先登录',
        content: '登录后才能查看订单',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    this.setData({ loading: true });

    if (refresh) {
      this.setData({
        page: 1,
        orderList: [],
        hasMore: true
      });
    }

    try {
      // 调用云函数获取订单列表
      const result = await wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: 'getOrders',
          userId: app.globalData.userInfo.id,
          status: this.data.selectedStatus,
          page: this.data.page,
          pageSize: this.data.pageSize
        }
      });

      if (result.result.success) {
        const newOrders = result.result.data.map(order => ({
          ...order,
          statusText: this.getStatusText(order.status),
          createTime: this.formatTime(order.createTime)
        }));

        const orderList = refresh ? newOrders : [...this.data.orderList, ...newOrders];

        this.setData({
          orderList,
          hasMore: newOrders.length === this.data.pageSize,
          page: this.data.page + 1
        });
      } else {
        throw new Error(result.result.message || '获取订单失败');
      }
    } catch (error) {
      console.error('加载订单失败:', error);
      
      // 使用模拟数据
      this.loadMockOrders(refresh);
    }

    this.setData({ loading: false });
  },

  // 加载模拟订单数据
  loadMockOrders(refresh) {
    const mockOrders = [
      {
        id: 'order_1',
        status: 'pending_payment',
        statusText: '待付款',
        createTime: '2024-03-15 14:30',
        items: [
          {
            id: 'item_1',
            name: '人参',
            spec: '50g装',
            price: 12.5,
            quantity: 2,
            imageUrl: '/images/medicine1.jpg'
          }
        ],
        totalAmount: '25.00'
      },
      {
        id: 'order_2',
        status: 'shipped',
        statusText: '待收货',
        createTime: '2024-03-14 10:20',
        items: [
          {
            id: 'item_2',
            name: '中医养生茶具套装',
            spec: '紫砂材质',
            price: 168,
            quantity: 1,
            imageUrl: '/images/product1.jpg'
          },
          {
            id: 'item_3',
            name: '枸杞子',
            spec: '100g装',
            price: 8.8,
            quantity: 1,
            imageUrl: '/images/medicine2.jpg'
          }
        ],
        totalAmount: '176.80'
      },
      {
        id: 'order_3',
        status: 'completed',
        statusText: '已完成',
        createTime: '2024-03-10 16:45',
        items: [
          {
            id: 'item_4',
            name: '本草纲目典藏版',
            spec: '精装版',
            price: 58,
            quantity: 1,
            imageUrl: '/images/product2.jpg'
          }
        ],
        totalAmount: '58.00'
      }
    ];

    // 根据状态筛选
    let filteredOrders = mockOrders;
    if (this.data.selectedStatus) {
      filteredOrders = mockOrders.filter(order => order.status === this.data.selectedStatus);
    }

    this.setData({
      orderList: refresh ? filteredOrders : [...this.data.orderList, ...filteredOrders],
      hasMore: false
    });
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending_payment': '待付款',
      'paid': '待发货',
      'shipped': '待收货',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知状态';
  },

  // 格式化时间
  formatTime(time) {
    if (typeof time === 'string') return time;
    
    const date = new Date(time);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  // 按状态筛选
  filterByStatus(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      selectedStatus: status
    });
    this.loadOrders(true);
  },

  // 加载更多
  loadMore() {
    this.loadOrders(false);
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${id}`
    });
  },

  // 联系客服
  contactService(e) {
    const id = e.currentTarget.dataset.id;
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
      showCancel: false
    });
  },

  // 取消订单
  cancelOrder(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      showCancelModal: true,
      cancelOrderId: id,
      cancelReason: ''
    });
  },

  // 隐藏取消订单弹窗
  hideCancelModal() {
    this.setData({
      showCancelModal: false
    });
  },

  // 选择取消原因
  onCancelReasonChange(e) {
    this.setData({
      cancelReason: e.detail.value
    });
  },

  // 确认取消订单
  async confirmCancel() {
    if (!this.data.cancelReason) {
      app.showError('请选择取消原因');
      return;
    }

    try {
      app.showLoading('取消中...');

      const result = await wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: 'cancelOrder',
          orderId: this.data.cancelOrderId,
          reason: this.data.cancelReason
        }
      });

      if (result.result.success) {
        app.showSuccess('订单已取消');
        this.hideCancelModal();
        this.loadOrders(true);
      } else {
        throw new Error(result.result.message || '取消失败');
      }
    } catch (error) {
      console.error('取消订单失败:', error);
      app.showError('取消失败，请重试');
    }

    app.hideLoading();
  },

  // 立即付款
  async payOrder(e) {
    const id = e.currentTarget.dataset.id;
    
    try {
      app.showLoading('发起支付...');
      
      // 模拟支付流程
      setTimeout(() => {
        app.hideLoading();
        app.showSuccess('支付成功');
        this.loadOrders(true);
      }, 2000);
    } catch (error) {
      console.error('支付失败:', error);
      app.hideLoading();
      app.showError('支付失败，请重试');
    }
  },

  // 申请退款
  refundOrder(e) {
    const id = e.currentTarget.dataset.id;
    wx.showModal({
      title: '申请退款',
      content: '确定要申请退款吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            app.showLoading('申请中...');
            
            // 调用退款接口
            setTimeout(() => {
              app.hideLoading();
              app.showSuccess('退款申请已提交');
              this.loadOrders(true);
            }, 1500);
          } catch (error) {
            console.error('申请退款失败:', error);
            app.hideLoading();
            app.showError('申请失败，请重试');
          }
        }
      }
    });
  },

  // 查看物流
  viewLogistics(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/logistics/logistics?orderId=${id}`
    });
  },

  // 确认收货
  confirmReceive(e) {
    const id = e.currentTarget.dataset.id;
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            app.showLoading('确认中...');
            
            const result = await wx.cloud.callFunction({
              name: 'quickstartFunctions',
              data: {
                type: 'confirmReceive',
                orderId: id
              }
            });

            if (result.result.success) {
              app.showSuccess('确认收货成功');
              this.loadOrders(true);
            } else {
              throw new Error(result.result.message || '确认失败');
            }
          } catch (error) {
            console.error('确认收货失败:', error);
            app.showError('确认失败，请重试');
          }
          
          app.hideLoading();
        }
      }
    });
  },

  // 再次购买
  buyAgain(e) {
    const id = e.currentTarget.dataset.id;
    const order = this.data.orderList.find(order => order.id === id);
    
    if (order) {
      // 将订单商品添加到购物车
      const cartItems = order.items.map(item => ({
        id: `cart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        productId: item.id,
        name: item.name,
        price: item.price,
        imageUrl: item.imageUrl,
        type: item.type || 'product',
        category: item.category || '商品',
        quantity: item.quantity,
        selected: true,
        spec: item.spec || '',
        unit: item.unit || '',
        totalPrice: (item.price * item.quantity).toFixed(2)
      }));

      // 保存到购物车
      const userInfo = app.globalData.userInfo;
      if (userInfo) {
        const existingCart = wx.getStorageSync(`cart_${userInfo.id}`) || [];
        const newCart = [...existingCart, ...cartItems];
        wx.setStorageSync(`cart_${userInfo.id}`, newCart);
      }

      app.showSuccess('商品已添加到购物车');
      
      // 跳转到购物车
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/cart/cart'
        });
      }, 1500);
    }
  },

  // 评价订单
  evaluateOrder(e) {
    const id = e.currentTarget.dataset.id;
    wx.showModal({
      title: '商品评价',
      content: '评价功能正在开发中...',
      showCancel: false
    });
  },

  // 去购物
  goShopping() {
    wx.switchTab({
      url: '/pages/products/products'
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});

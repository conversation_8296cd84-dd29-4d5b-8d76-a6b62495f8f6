/**orders.wxss**/

/* 状态筛选 */
.status-filter {
  background-color: white;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  border-radius: 15rpx;
}

.status-scroll {
  white-space: nowrap;
  padding: 0 30rpx;
}

.status-item {
  display: inline-block;
  padding: 15rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #F5F5F5;
}

.status-item.active {
  background-color: #A0522D;
  color: white;
}

/* 订单列表 */
.order-list {
  margin-bottom: 30rpx;
}

.order-item {
  background-color: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(160, 82, 45, 0.08);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 2rpx solid #F0F0F0;
}

.order-info {
  display: flex;
  flex-direction: column;
}

.order-number {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.order-time {
  font-size: 22rpx;
  color: #999;
}

.order-status {
  text-align: right;
}

.status-text {
  font-size: 26rpx;
  font-weight: bold;
}

.status-text.pending_payment {
  color: #FF9500;
}

.status-text.paid {
  color: #007AFF;
}

.status-text.shipped {
  color: #34C759;
}

.status-text.completed {
  color: #A0522D;
}

.status-text.cancelled {
  color: #999;
}

/* 商品列表 */
.order-goods {
  padding: 25rpx 30rpx;
  border-bottom: 2rpx solid #F0F0F0;
}

.goods-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.goods-item:last-child {
  margin-bottom: 0;
}

.goods-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.goods-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-spec {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.goods-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 24rpx;
  color: #E74C3C;
  font-weight: bold;
}

.goods-quantity {
  font-size: 22rpx;
  color: #666;
}

.more-goods {
  text-align: center;
  padding: 15rpx 0;
  border-top: 1rpx dashed #E0E0E0;
  margin-top: 15rpx;
}

.more-text {
  font-size: 22rpx;
  color: #999;
}

/* 订单金额 */
.order-amount {
  padding: 20rpx 30rpx;
  text-align: right;
  border-bottom: 2rpx solid #F0F0F0;
}

.amount-label {
  font-size: 24rpx;
  color: #666;
}

.amount-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #E74C3C;
}

/* 订单操作 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 25rpx 30rpx;
  gap: 15rpx;
}

.action-btn {
  padding: 15rpx 30rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  margin: 0;
}

.action-btn.primary {
  background-color: #A0522D;
  color: white;
}

.action-btn.secondary {
  background-color: #228B22;
  color: white;
}

.action-btn.outline {
  background-color: transparent;
  border: 2rpx solid #A0522D;
  color: #A0522D;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 150rpx 50rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 40rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-bottom: 30rpx;
}

/* 取消订单弹窗 */
.cancel-modal {
  width: 80%;
  max-width: 500rpx;
}

.cancel-tip {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.cancel-reason {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.cancel-reason:last-child {
  border-bottom: none;
}

.reason-text {
  font-size: 26rpx;
  color: #333;
  margin-left: 15rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .order-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .order-status {
    margin-top: 10rpx;
    text-align: left;
  }
  
  .order-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .action-btn {
    margin: 5rpx;
  }
  
  .goods-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .goods-image {
    margin-bottom: 15rpx;
    margin-right: 0;
  }
  
  .goods-price-row {
    width: 100%;
  }
}

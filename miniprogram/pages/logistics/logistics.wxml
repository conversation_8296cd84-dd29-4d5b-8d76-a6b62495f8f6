<!--logistics.wxml-->
<view class="container">
  <!-- 物流头部信息 -->
  <view class="logistics-header card">
    <view class="header-info">
      <view class="logistics-status">
        <image class="status-icon" src="{{logisticsInfo.statusIcon}}" mode="aspectFit"></image>
        <text class="status-text">{{logisticsInfo.statusText}}</text>
      </view>
      <text class="status-desc">{{logisticsInfo.statusDesc}}</text>
    </view>
    <view class="logistics-company">
      <image class="company-logo" src="{{logisticsInfo.companyLogo}}" mode="aspectFit"></image>
      <view class="company-info">
        <text class="company-name">{{logisticsInfo.companyName}}</text>
        <text class="tracking-number">运单号：{{logisticsInfo.trackingNumber}}</text>
      </view>
      <button class="copy-btn" bindtap="copyTrackingNumber">复制</button>
    </view>
  </view>

  <!-- 预计送达时间 -->
  <view class="delivery-time card" wx:if="{{logisticsInfo.estimatedTime}}">
    <view class="time-header">
      <image class="time-icon" src="/images/clock-icon.png" mode="aspectFit"></image>
      <text class="time-title">预计送达时间</text>
    </view>
    <text class="estimated-time">{{logisticsInfo.estimatedTime}}</text>
    <text class="time-note">具体时间可能因天气等因素有所调整</text>
  </view>

  <!-- 收货地址 -->
  <view class="address-info card">
    <view class="address-header">
      <image class="address-icon" src="/images/location-icon.png" mode="aspectFit"></image>
      <text class="address-title">收货地址</text>
    </view>
    <view class="address-content">
      <view class="receiver-info">
        <text class="receiver-name">{{addressInfo.name}}</text>
        <text class="receiver-phone">{{addressInfo.phone}}</text>
      </view>
      <text class="address-detail">{{addressInfo.fullAddress}}</text>
    </view>
  </view>

  <!-- 物流轨迹 -->
  <view class="logistics-track card">
    <view class="track-header">
      <text class="track-title">物流轨迹</text>
      <text class="track-count">共{{trackList.length}}条记录</text>
    </view>
    <view class="track-list">
      <view class="track-item {{index === 0 ? 'current' : ''}}" wx:for="{{trackList}}" wx:key="time">
        <view class="track-time">
          <text class="time-date">{{item.date}}</text>
          <text class="time-clock">{{item.time}}</text>
        </view>
        <view class="track-line">
          <view class="track-dot {{index === 0 ? 'active' : ''}}"></view>
          <view class="track-connector" wx:if="{{index < trackList.length - 1}}"></view>
        </view>
        <view class="track-content">
          <text class="track-desc">{{item.description}}</text>
          <text class="track-location" wx:if="{{item.location}}">{{item.location}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系方式 -->
  <view class="contact-section card">
    <text class="section-title">联系方式</text>
    <view class="contact-list">
      <view class="contact-item" bindtap="callPhone" data-phone="{{logisticsInfo.servicePhone}}">
        <image class="contact-icon" src="/images/phone-icon.png" mode="aspectFit"></image>
        <view class="contact-info">
          <text class="contact-title">联系快递员</text>
          <text class="contact-desc">{{logisticsInfo.servicePhone}}</text>
        </view>
        <image class="arrow-icon" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="contact-item" bindtap="contactService">
        <image class="contact-icon" src="/images/service-icon.png" mode="aspectFit"></image>
        <view class="contact-info">
          <text class="contact-title">联系客服</text>
          <text class="contact-desc">在线客服为您服务</text>
        </view>
        <image class="arrow-icon" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions" wx:if="{{showActions}}">
    <button class="action-btn outline" bindtap="viewOrderDetail">查看订单</button>
    <button class="action-btn primary" bindtap="confirmReceive" wx:if="{{canConfirmReceive}}">确认收货</button>
  </view>
</view>

<!-- 确认收货弹窗 -->
<view class="modal-overlay" wx:if="{{showConfirmModal}}" bindtap="hideConfirmModal">
  <view class="modal-content confirm-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">确认收货</text>
      <text class="modal-close" bindtap="hideConfirmModal">×</text>
    </view>
    <view class="modal-body">
      <text class="confirm-tip">请确认您已收到商品并验收无误</text>
      <view class="goods-preview">
        <view class="goods-item" wx:for="{{orderGoods}}" wx:key="id">
          <image class="goods-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
          <text class="goods-name">{{item.name}}</text>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="hideConfirmModal">取消</button>
      <button class="btn btn-primary" bindtap="doConfirmReceive">确认收货</button>
    </view>
  </view>
</view>

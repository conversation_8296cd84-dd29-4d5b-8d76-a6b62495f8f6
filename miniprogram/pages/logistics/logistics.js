// logistics.js
const app = getApp();

Page({
  data: {
    orderId: '',
    logisticsInfo: {},
    addressInfo: {},
    trackList: [],
    orderGoods: [],
    showActions: true,
    canConfirmReceive: false,
    showConfirmModal: false
  },

  onLoad(options) {
    if (options.orderId) {
      this.setData({
        orderId: options.orderId
      });
      this.loadLogisticsInfo();
    } else {
      wx.showModal({
        title: '参数错误',
        content: '订单ID不能为空',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  // 加载物流信息
  async loadLogisticsInfo() {
    try {
      app.showLoading('加载中...');

      // 调用云函数获取物流信息
      const result = await wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: 'getLogisticsInfo',
          orderId: this.data.orderId
        }
      });

      if (result.result.success) {
        this.processLogisticsData(result.result.data);
      } else {
        throw new Error(result.result.message || '获取物流信息失败');
      }
    } catch (error) {
      console.error('加载物流信息失败:', error);
      
      // 使用模拟数据
      this.loadMockLogisticsInfo();
    }

    app.hideLoading();
  },

  // 加载模拟物流信息
  loadMockLogisticsInfo() {
    const mockData = {
      logisticsInfo: {
        statusIcon: '/images/shipping-icon.png',
        statusText: '运输中',
        statusDesc: '您的包裹正在配送途中，请耐心等待',
        companyName: '顺丰速运',
        companyLogo: '/images/sf-logo.png',
        trackingNumber: 'SF1234567890123',
        servicePhone: '95338',
        estimatedTime: '今天 18:00 前送达'
      },
      addressInfo: {
        name: '张三',
        phone: '138****8888',
        fullAddress: '北京市朝阳区三里屯街道某某小区1号楼101室'
      },
      trackList: [
        {
          date: '03-16',
          time: '14:30',
          description: '快件已到达【北京朝阳三里屯营业点】，正在派送中',
          location: '北京朝阳三里屯营业点'
        },
        {
          date: '03-16',
          time: '09:15',
          description: '快件已到达【北京转运中心】',
          location: '北京转运中心'
        },
        {
          date: '03-15',
          time: '22:45',
          description: '快件已从【上海转运中心】发出，下一站【北京转运中心】',
          location: '上海转运中心'
        },
        {
          date: '03-15',
          time: '18:20',
          description: '快件已到达【上海转运中心】',
          location: '上海转运中心'
        },
        {
          date: '03-15',
          time: '15:30',
          description: '快件已从【上海浦东营业点】发出，下一站【上海转运中心】',
          location: '上海浦东营业点'
        },
        {
          date: '03-15',
          time: '14:00',
          description: '商家已发货，快件已交给顺丰速运',
          location: '上海浦东营业点'
        }
      ],
      orderGoods: [
        {
          id: 'item_1',
          name: '人参',
          imageUrl: '/images/medicine1.jpg'
        },
        {
          id: 'item_2',
          name: '枸杞子',
          imageUrl: '/images/medicine2.jpg'
        }
      ],
      canConfirmReceive: true
    };

    this.processLogisticsData(mockData);
  },

  // 处理物流数据
  processLogisticsData(data) {
    this.setData({
      logisticsInfo: data.logisticsInfo,
      addressInfo: data.addressInfo,
      trackList: data.trackList,
      orderGoods: data.orderGoods || [],
      canConfirmReceive: data.canConfirmReceive || false
    });
  },

  // 复制运单号
  copyTrackingNumber() {
    wx.setClipboardData({
      data: this.data.logisticsInfo.trackingNumber,
      success: () => {
        app.showSuccess('运单号已复制');
      }
    });
  },

  // 拨打电话
  callPhone(e) {
    const phone = e.currentTarget.dataset.phone;
    wx.makePhoneCall({
      phoneNumber: phone,
      fail: (error) => {
        console.error('拨打电话失败:', error);
        app.showError('拨打电话失败');
      }
    });
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：************\n工作时间：9:00-18:00\n\n或者您可以通过小程序内的在线客服功能联系我们。',
      showCancel: false
    });
  },

  // 查看订单详情
  viewOrderDetail() {
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${this.data.orderId}`
    });
  },

  // 确认收货
  confirmReceive() {
    this.setData({
      showConfirmModal: true
    });
  },

  // 隐藏确认收货弹窗
  hideConfirmModal() {
    this.setData({
      showConfirmModal: false
    });
  },

  // 执行确认收货
  async doConfirmReceive() {
    try {
      app.showLoading('确认中...');

      const result = await wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: 'confirmReceive',
          orderId: this.data.orderId
        }
      });

      if (result.result.success) {
        app.showSuccess('确认收货成功');
        this.hideConfirmModal();
        
        // 更新页面状态
        this.setData({
          canConfirmReceive: false,
          'logisticsInfo.statusText': '已签收',
          'logisticsInfo.statusDesc': '包裹已成功签收，感谢您的购买'
        });

        // 在轨迹列表前添加签收记录
        const now = new Date();
        const newTrack = {
          date: this.formatDate(now),
          time: this.formatTime(now),
          description: '包裹已签收，感谢使用顺丰速运',
          location: this.data.addressInfo.fullAddress
        };

        const trackList = [newTrack, ...this.data.trackList];
        this.setData({ trackList });

        // 延迟跳转到订单详情页
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/order-detail/order-detail?id=${this.data.orderId}`
          });
        }, 2000);
      } else {
        throw new Error(result.result.message || '确认失败');
      }
    } catch (error) {
      console.error('确认收货失败:', error);
      app.showError('确认失败，请重试');
    }

    app.hideLoading();
  },

  // 格式化日期
  formatDate(date) {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${month}-${day}`;
  },

  // 格式化时间
  formatTime(date) {
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    return `${hour}:${minute}`;
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadLogisticsInfo().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '物流信息 - 慧心制药',
      path: `/pages/logistics/logistics?orderId=${this.data.orderId}`,
      imageUrl: '/images/share-logistics.png'
    };
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});

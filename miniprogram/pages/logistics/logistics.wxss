/**logistics.wxss**/

/* 物流头部 */
.logistics-header {
  margin-bottom: 20rpx;
}

.header-info {
  margin-bottom: 25rpx;
}

.logistics-status {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.status-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 15rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #A0522D;
}

.status-desc {
  font-size: 24rpx;
  color: #666;
  margin-left: 65rpx;
}

.logistics-company {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #F8F8F8;
  border-radius: 10rpx;
}

.company-logo {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.company-info {
  flex: 1;
}

.company-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.tracking-number {
  font-size: 22rpx;
  color: #666;
}

.copy-btn {
  background-color: #A0522D;
  color: white;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  border: none;
}

/* 预计送达时间 */
.delivery-time {
  margin-bottom: 20rpx;
}

.time-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.time-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.time-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
}

.estimated-time {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.time-note {
  font-size: 22rpx;
  color: #999;
}

/* 收货地址 */
.address-info {
  margin-bottom: 20rpx;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.address-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.address-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
}

.address-content {
  padding-left: 55rpx;
}

.receiver-info {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.receiver-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.receiver-phone {
  font-size: 24rpx;
  color: #666;
}

.address-detail {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 物流轨迹 */
.logistics-track {
  margin-bottom: 20rpx;
}

.track-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.track-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
}

.track-count {
  font-size: 22rpx;
  color: #999;
}

.track-list {
  position: relative;
}

.track-item {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
}

.track-item:last-child {
  margin-bottom: 0;
}

.track-item.current .track-desc {
  color: #A0522D;
  font-weight: bold;
}

.track-time {
  width: 120rpx;
  flex-shrink: 0;
  text-align: center;
}

.time-date {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.time-clock {
  font-size: 20rpx;
  color: #999;
}

.track-line {
  width: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 20rpx;
}

.track-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #E0E0E0;
  border: 4rpx solid #F0F0F0;
  flex-shrink: 0;
}

.track-dot.active {
  background-color: #A0522D;
  border-color: #FFF8F5;
  box-shadow: 0 0 0 4rpx #A0522D;
}

.track-connector {
  width: 2rpx;
  height: 60rpx;
  background-color: #E0E0E0;
  margin-top: 10rpx;
}

.track-content {
  flex: 1;
  padding-top: 5rpx;
}

.track-desc {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  display: block;
  margin-bottom: 5rpx;
}

.track-location {
  font-size: 22rpx;
  color: #999;
}

/* 联系方式 */
.contact-section {
  margin-bottom: 120rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
  display: block;
  margin-bottom: 20rpx;
}

.contact-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 25rpx 20rpx;
  background-color: #F8F8F8;
  border-radius: 10rpx;
}

.contact-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.contact-info {
  flex: 1;
}

.contact-title {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.contact-desc {
  font-size: 22rpx;
  color: #666;
}

.arrow-icon {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.5;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #F0F0F0;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.action-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  border: none;
  margin: 0;
}

.action-btn.primary {
  background-color: #A0522D;
  color: white;
}

.action-btn.outline {
  background-color: transparent;
  border: 2rpx solid #A0522D;
  color: #A0522D;
}

/* 确认收货弹窗 */
.confirm-modal {
  width: 80%;
  max-width: 500rpx;
}

.confirm-tip {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  display: block;
  margin-bottom: 25rpx;
}

.goods-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  justify-content: center;
}

.goods-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120rpx;
}

.goods-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  margin-bottom: 8rpx;
}

.goods-name {
  font-size: 20rpx;
  color: #666;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .logistics-company {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .company-logo {
    margin-bottom: 15rpx;
    margin-right: 0;
  }
  
  .copy-btn {
    margin-top: 15rpx;
    align-self: flex-end;
  }
  
  .track-item {
    flex-direction: column;
  }
  
  .track-time {
    width: auto;
    text-align: left;
    margin-bottom: 10rpx;
  }
  
  .track-line {
    display: none;
  }
  
  .track-content {
    padding-top: 0;
  }
  
  .bottom-actions {
    flex-direction: column;
  }
}

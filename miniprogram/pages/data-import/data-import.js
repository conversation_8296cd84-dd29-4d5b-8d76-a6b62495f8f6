// data-import.js
const app = getApp();

Page({
  data: {
    dbStatus: {
      connected: false,
      userCount: 0,
      hasAdmin: false
    },
    importing: false,
    logs: []
  },

  onLoad() {
    // 检查是否为管理员
    if (!app.globalData.userInfo || app.globalData.userInfo.role !== 'admin') {
      wx.showModal({
        title: '权限不足',
        content: '只有管理员才能使用数据导入功能',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    this.checkStatus();
  },

  // 检查数据库状态
  async checkStatus() {
    try {
      this.addLog('检查数据库状态...', 'info');

      const result = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'checkDatabase'
        }
      });

      if (result.result.success) {
        const data = result.result.data;
        this.setData({
          dbStatus: {
            connected: true,
            userCount: data.totalUsers || 0,
            hasAdmin: data.hasAdmin
          }
        });
        this.addLog('数据库状态检查完成', 'success');
      } else {
        this.setData({
          dbStatus: {
            connected: false,
            userCount: 0,
            hasAdmin: false
          }
        });
        this.addLog('数据库连接失败: ' + result.result.message, 'error');
      }
    } catch (error) {
      console.error('检查数据库状态失败:', error);
      this.addLog('检查数据库状态失败: ' + error.message, 'error');
    }
  },

  // 导入用户数据
  async importUsers() {
    if (this.data.importing) return;

    this.setData({ importing: true });
    this.addLog('开始导入用户数据...', 'info');

    try {
      const result = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'initTestUsers'
        }
      });

      if (result.result.success) {
        this.addLog('用户数据导入成功: ' + result.result.message, 'success');
        app.showSuccess('用户数据导入成功');
        
        // 刷新状态
        setTimeout(() => {
          this.checkStatus();
        }, 1000);
      } else {
        this.addLog('用户数据导入失败: ' + result.result.message, 'error');
        app.showError('导入失败');
      }
    } catch (error) {
      console.error('导入用户数据失败:', error);
      this.addLog('导入用户数据失败: ' + error.message, 'error');
      app.showError('导入失败');
    }

    this.setData({ importing: false });
  },

  // 导入中药材数据
  async importMedicines() {
    if (this.data.importing) return;

    this.setData({ importing: true });
    this.addLog('开始导入中药材数据...', 'info');

    try {
      const result = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'initMedicinesData'
        }
      });

      if (result.result.success) {
        this.addLog('中药材数据导入成功: ' + result.result.message, 'success');
        app.showSuccess('中药材数据导入成功');
      } else {
        this.addLog('中药材数据导入失败: ' + result.result.message, 'error');
        app.showError('导入失败');
      }
    } catch (error) {
      console.error('导入中药材数据失败:', error);
      this.addLog('导入中药材数据失败: ' + error.message, 'error');
      app.showError('导入失败');
    }

    this.setData({ importing: false });
  },

  // 导入文创产品数据
  async importProducts() {
    if (this.data.importing) return;

    this.setData({ importing: true });
    this.addLog('开始导入文创产品数据...', 'info');

    try {
      // 模拟导入过程
      await this.simulateImport('文创产品');
      this.addLog('文创产品数据导入成功', 'success');
      app.showSuccess('文创产品数据导入成功');
    } catch (error) {
      console.error('导入文创产品数据失败:', error);
      this.addLog('导入文创产品数据失败: ' + error.message, 'error');
      app.showError('导入失败');
    }

    this.setData({ importing: false });
  },

  // 导入养生文章数据
  async importArticles() {
    if (this.data.importing) return;

    this.setData({ importing: true });
    this.addLog('开始导入养生文章数据...', 'info');

    try {
      // 模拟导入过程
      await this.simulateImport('养生文章');
      this.addLog('养生文章数据导入成功', 'success');
      app.showSuccess('养生文章数据导入成功');
    } catch (error) {
      console.error('导入养生文章数据失败:', error);
      this.addLog('导入养生文章数据失败: ' + error.message, 'error');
      app.showError('导入失败');
    }

    this.setData({ importing: false });
  },

  // 一键导入所有数据
  async importAllData() {
    if (this.data.importing) return;

    wx.showModal({
      title: '确认导入',
      content: '将导入所有测试数据，包括用户、中药材、产品和文章数据，确定继续吗？',
      success: async (res) => {
        if (res.confirm) {
          this.setData({ importing: true });
          this.addLog('开始一键导入所有数据...', 'info');

          try {
            // 依次导入各类数据
            await this.importUsers();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await this.importMedicines();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await this.importProducts();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await this.importArticles();

            this.addLog('所有数据导入完成！', 'success');
            app.showSuccess('所有数据导入完成');
            
            // 刷新状态
            setTimeout(() => {
              this.checkStatus();
            }, 1000);
          } catch (error) {
            console.error('一键导入失败:', error);
            this.addLog('一键导入失败: ' + error.message, 'error');
            app.showError('导入失败');
          }

          this.setData({ importing: false });
        }
      }
    });
  },

  // 模拟导入过程（用于暂未实现的功能）
  simulateImport(dataType) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log(`${dataType}数据导入模拟完成`);
        resolve();
      }, 2000);
    });
  },

  // 添加日志
  addLog(message, type = 'info') {
    const now = new Date();
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
    
    const logs = this.data.logs;
    logs.unshift({
      time,
      message,
      type
    });

    // 只保留最近20条日志
    if (logs.length > 20) {
      logs.splice(20);
    }

    this.setData({ logs });
  },

  // 清空日志
  clearLogs() {
    this.setData({ logs: [] });
  }
});

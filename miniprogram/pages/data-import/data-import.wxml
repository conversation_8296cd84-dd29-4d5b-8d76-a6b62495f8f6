<!--data-import.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">数据导入工具</text>
    <text class="page-desc">快速导入测试数据，便于开发和测试</text>
  </view>

  <!-- 数据库状态 -->
  <view class="status-section card">
    <view class="status-header">
      <text class="status-title">数据库状态</text>
      <button class="refresh-btn" bindtap="checkStatus">刷新</button>
    </view>
    <view class="status-content">
      <view class="status-item">
        <text class="status-label">数据库连接：</text>
        <text class="status-value {{dbStatus.connected ? 'success' : 'error'}}">
          {{dbStatus.connected ? '正常' : '异常'}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">用户数据：</text>
        <text class="status-value">{{dbStatus.userCount}}条记录</text>
      </view>
      <view class="status-item">
        <text class="status-label">管理员账号：</text>
        <text class="status-value {{dbStatus.hasAdmin ? 'success' : 'error'}}">
          {{dbStatus.hasAdmin ? '已创建' : '未创建'}}
        </text>
      </view>
    </view>
  </view>

  <!-- 数据导入选项 -->
  <view class="import-section">
    <text class="section-title">数据导入选项</text>
    
    <!-- 用户数据导入 -->
    <view class="import-card card">
      <view class="import-header">
        <image class="import-icon" src="/images/user-icon.png" mode="aspectFit"></image>
        <view class="import-info">
          <text class="import-title">用户数据</text>
          <text class="import-desc">导入管理员和测试用户账号</text>
        </view>
      </view>
      <view class="import-content">
        <text class="import-detail">包含内容：</text>
        <text class="import-item">• 管理员账号：admin / admin123</text>
        <text class="import-item">• 测试用户：test / test123</text>
      </view>
      <view class="import-actions">
        <button class="import-btn primary" bindtap="importUsers" disabled="{{importing}}">
          {{importing ? '导入中...' : '导入用户数据'}}
        </button>
      </view>
    </view>

    <!-- 中药材数据导入 -->
    <view class="import-card card">
      <view class="import-header">
        <image class="import-icon" src="/images/medicine-icon.png" mode="aspectFit"></image>
        <view class="import-info">
          <text class="import-title">中药材数据</text>
          <text class="import-desc">导入常用中药材信息</text>
        </view>
      </view>
      <view class="import-content">
        <text class="import-detail">包含内容：</text>
        <text class="import-item">• 人参、枸杞子、当归等5种中药材</text>
        <text class="import-item">• 包含功效、价格、库存等信息</text>
      </view>
      <view class="import-actions">
        <button class="import-btn secondary" bindtap="importMedicines" disabled="{{importing}}">
          {{importing ? '导入中...' : '导入中药材数据'}}
        </button>
      </view>
    </view>

    <!-- 文创产品数据导入 -->
    <view class="import-card card">
      <view class="import-header">
        <image class="import-icon" src="/images/product-icon.png" mode="aspectFit"></image>
        <view class="import-info">
          <text class="import-title">文创产品数据</text>
          <text class="import-desc">导入中医文创产品信息</text>
        </view>
      </view>
      <view class="import-content">
        <text class="import-detail">包含内容：</text>
        <text class="import-item">• 茶具、书籍、养生用品等6种产品</text>
        <text class="import-item">• 包含价格、库存、销量等信息</text>
      </view>
      <view class="import-actions">
        <button class="import-btn secondary" bindtap="importProducts" disabled="{{importing}}">
          {{importing ? '导入中...' : '导入产品数据'}}
        </button>
      </view>
    </view>

    <!-- 养生文章数据导入 -->
    <view class="import-card card">
      <view class="import-header">
        <image class="import-icon" src="/images/article-icon.png" mode="aspectFit"></image>
        <view class="import-info">
          <text class="import-title">养生文章数据</text>
          <text class="import-desc">导入中医养生文章内容</text>
        </view>
      </view>
      <view class="import-content">
        <text class="import-detail">包含内容：</text>
        <text class="import-item">• 春季养生、食疗养生等4篇文章</text>
        <text class="import-item">• 包含完整内容和配图信息</text>
      </view>
      <view class="import-actions">
        <button class="import-btn secondary" bindtap="importArticles" disabled="{{importing}}">
          {{importing ? '导入中...' : '导入文章数据'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 一键导入 -->
  <view class="quick-import-section">
    <button class="quick-import-btn" bindtap="importAllData" disabled="{{importing}}">
      {{importing ? '导入中...' : '一键导入所有数据'}}
    </button>
    <text class="quick-import-tip">将导入所有测试数据，适合首次使用</text>
  </view>

  <!-- 操作日志 -->
  <view class="log-section card" wx:if="{{logs.length > 0}}">
    <text class="section-title">操作日志</text>
    <view class="log-list">
      <view class="log-item {{item.type}}" wx:for="{{logs}}" wx:key="time">
        <text class="log-time">{{item.time}}</text>
        <text class="log-message">{{item.message}}</text>
      </view>
    </view>
    <button class="clear-log-btn" bindtap="clearLogs">清空日志</button>
  </view>

  <!-- 帮助信息 -->
  <view class="help-section card">
    <text class="section-title">使用说明</text>
    <view class="help-content">
      <text class="help-item">1. 首次使用建议点击"一键导入所有数据"</text>
      <text class="help-item">2. 也可以根据需要单独导入某类数据</text>
      <text class="help-item">3. 导入前会自动检查数据是否已存在</text>
      <text class="help-item">4. 重复导入不会创建重复数据</text>
      <text class="help-item">5. 导入完成后可在对应页面查看数据</text>
    </view>
  </view>
</view>

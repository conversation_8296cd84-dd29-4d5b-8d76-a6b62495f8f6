/**data-import.wxss**/

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #A0522D 0%, #CD853F 100%);
  color: white;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.page-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 状态区域 */
.status-section {
  margin-bottom: 30rpx;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
}

.refresh-btn {
  background-color: #A0522D;
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  border: none;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 26rpx;
  color: #333;
}

.status-value {
  font-size: 26rpx;
  font-weight: bold;
}

.status-value.success {
  color: #228B22;
}

.status-value.error {
  color: #E74C3C;
}

/* 导入区域 */
.import-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
  display: block;
  margin-bottom: 20rpx;
}

.import-card {
  margin-bottom: 20rpx;
  padding: 25rpx;
}

.import-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.import-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.import-info {
  flex: 1;
}

.import-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.import-desc {
  font-size: 22rpx;
  color: #666;
}

.import-content {
  margin-bottom: 20rpx;
  padding: 15rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
}

.import-detail {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.import-item {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
  line-height: 1.4;
}

.import-actions {
  display: flex;
  justify-content: flex-end;
}

.import-btn {
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  border: none;
  margin: 0;
}

.import-btn.primary {
  background-color: #A0522D;
  color: white;
}

.import-btn.secondary {
  background-color: #228B22;
  color: white;
}

.import-btn:disabled {
  background-color: #CCC;
  color: #999;
}

/* 一键导入 */
.quick-import-section {
  text-align: center;
  margin-bottom: 30rpx;
}

.quick-import-btn {
  width: 80%;
  padding: 30rpx;
  background: linear-gradient(135deg, #A0522D 0%, #CD853F 100%);
  color: white;
  border-radius: 25rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  margin-bottom: 15rpx;
}

.quick-import-btn:disabled {
  background: #CCC;
  color: #999;
}

.quick-import-tip {
  font-size: 22rpx;
  color: #666;
}

/* 操作日志 */
.log-section {
  margin-bottom: 30rpx;
}

.log-list {
  max-height: 400rpx;
  overflow-y: auto;
  margin-bottom: 20rpx;
}

.log-item {
  padding: 15rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #CCC;
}

.log-item.success {
  background-color: #F0F8F0;
  border-left-color: #228B22;
}

.log-item.error {
  background-color: #FFF0F0;
  border-left-color: #E74C3C;
}

.log-item.info {
  background-color: #F0F8FF;
  border-left-color: #4169E1;
}

.log-time {
  font-size: 20rpx;
  color: #999;
  display: block;
  margin-bottom: 5rpx;
}

.log-message {
  font-size: 24rpx;
  color: #333;
}

.clear-log-btn {
  background-color: #666;
  color: white;
  padding: 15rpx 30rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  border: none;
}

/* 帮助信息 */
.help-section {
  margin-bottom: 30rpx;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.help-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  padding: 8rpx 0;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .import-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .import-icon {
    margin-bottom: 15rpx;
    margin-right: 0;
  }
  
  .import-actions {
    justify-content: center;
  }
  
  .status-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .status-value {
    margin-top: 5rpx;
  }
}

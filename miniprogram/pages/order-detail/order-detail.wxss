/**order-detail.wxss**/

/* 订单状态 */
.status-section {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #A0522D 0%, #CD853F 100%);
  color: white;
}

.status-icon {
  margin-right: 25rpx;
}

.status-image {
  width: 80rpx;
  height: 80rpx;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.status-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 物流信息 */
.logistics-section {
  margin-bottom: 20rpx;
}

.logistics-header {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
}

.logistics-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}

.logistics-info {
  flex: 1;
}

.logistics-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
  display: block;
  margin-bottom: 8rpx;
}

.logistics-desc {
  font-size: 24rpx;
  color: #666;
}

.arrow-icon {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.5;
}

/* 收货地址 */
.address-section {
  margin-bottom: 20rpx;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.address-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.address-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
}

.address-content {
  padding-left: 55rpx;
}

.address-info {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.receiver-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.receiver-phone {
  font-size: 24rpx;
  color: #666;
}

.address-detail {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 商品信息 */
.goods-section {
  margin-bottom: 20rpx;
}

.goods-header {
  margin-bottom: 25rpx;
}

.goods-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
}

.goods-list {
  border-top: 2rpx solid #F0F0F0;
  padding-top: 25rpx;
}

.goods-item {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  padding-bottom: 25rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.goods-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.goods-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-spec {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.goods-price-row {
  display: flex;
  align-items: baseline;
}

.goods-price {
  font-size: 24rpx;
  color: #E74C3C;
  font-weight: bold;
  margin-right: 8rpx;
}

.goods-unit {
  font-size: 20rpx;
  color: #999;
  margin-right: 15rpx;
}

.goods-quantity {
  font-size: 22rpx;
  color: #666;
}

.goods-total {
  font-size: 26rpx;
  color: #E74C3C;
  font-weight: bold;
  margin-left: 20rpx;
}

/* 订单信息 */
.order-info-section {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
  display: block;
  margin-bottom: 25rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  width: 150rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

.info-value-row {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.copy-btn {
  background-color: #A0522D;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  margin-left: 15rpx;
  border: none;
}

/* 费用明细 */
.cost-section {
  margin-bottom: 120rpx;
}

.cost-list {
  margin-top: 25rpx;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.cost-item:last-child {
  border-bottom: none;
}

.cost-item.total {
  padding-top: 20rpx;
  border-top: 2rpx solid #E0E0E0;
  margin-top: 10rpx;
}

.cost-label {
  font-size: 26rpx;
  color: #333;
}

.cost-value {
  font-size: 26rpx;
  color: #333;
}

.cost-value.discount {
  color: #E74C3C;
}

.cost-value.total-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #E74C3C;
}

/* 底部操作 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #F0F0F0;
  z-index: 100;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 15rpx;
}

.action-btn {
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  border: none;
  margin: 0;
}

.action-btn.primary {
  background-color: #A0522D;
  color: white;
}

.action-btn.secondary {
  background-color: #228B22;
  color: white;
}

.action-btn.outline {
  background-color: transparent;
  border: 2rpx solid #A0522D;
  color: #A0522D;
}

/* 取消订单弹窗 */
.cancel-modal {
  width: 80%;
  max-width: 500rpx;
}

.cancel-tip {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.cancel-reason {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.cancel-reason:last-child {
  border-bottom: none;
}

.reason-text {
  font-size: 26rpx;
  color: #333;
  margin-left: 15rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .status-section {
    flex-direction: column;
    text-align: center;
  }
  
  .status-icon {
    margin-right: 0;
    margin-bottom: 20rpx;
  }
  
  .goods-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .goods-image {
    margin-bottom: 15rpx;
    margin-right: 0;
  }
  
  .goods-total {
    margin-left: 0;
    margin-top: 10rpx;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-label {
    width: auto;
    margin-bottom: 8rpx;
  }
  
  .info-value {
    text-align: left;
  }
  
  .action-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .info-value-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .copy-btn {
    margin-left: 0;
    margin-top: 10rpx;
  }
}

<!--order-detail.wxml-->
<view class="container">
  <!-- 订单状态 -->
  <view class="status-section card">
    <view class="status-icon">
      <image class="status-image" src="{{statusConfig.icon}}" mode="aspectFit"></image>
    </view>
    <view class="status-info">
      <text class="status-title">{{statusConfig.title}}</text>
      <text class="status-desc">{{statusConfig.description}}</text>
    </view>
  </view>

  <!-- 物流信息 -->
  <view class="logistics-section card" wx:if="{{orderDetail.status === 'shipped' || orderDetail.status === 'completed'}}" bindtap="viewLogistics">
    <view class="logistics-header">
      <image class="logistics-icon" src="/images/truck-icon.png" mode="aspectFit"></image>
      <view class="logistics-info">
        <text class="logistics-title">{{orderDetail.logistics ? orderDetail.logistics.company : '正在配送中'}}</text>
        <text class="logistics-desc">{{orderDetail.logistics ? orderDetail.logistics.status : '商品正在路上，请耐心等待'}}</text>
      </view>
      <image class="arrow-icon" src="/images/arrow-right.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 收货地址 -->
  <view class="address-section card">
    <view class="address-header">
      <image class="address-icon" src="/images/location-icon.png" mode="aspectFit"></image>
      <text class="address-title">收货地址</text>
    </view>
    <view class="address-content">
      <view class="address-info">
        <text class="receiver-name">{{orderDetail.address.name}}</text>
        <text class="receiver-phone">{{orderDetail.address.phone}}</text>
      </view>
      <text class="address-detail">{{orderDetail.address.province}} {{orderDetail.address.city}} {{orderDetail.address.district}} {{orderDetail.address.detail}}</text>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="goods-section card">
    <view class="goods-header">
      <text class="goods-title">商品信息</text>
    </view>
    <view class="goods-list">
      <view class="goods-item" wx:for="{{orderDetail.items}}" wx:key="id">
        <image class="goods-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
        <view class="goods-info">
          <text class="goods-name">{{item.name}}</text>
          <text class="goods-spec" wx:if="{{item.spec}}">规格：{{item.spec}}</text>
          <view class="goods-price-row">
            <text class="goods-price">¥{{item.price}}</text>
            <text class="goods-unit" wx:if="{{item.unit}}">{{item.unit}}</text>
            <text class="goods-quantity">×{{item.quantity}}</text>
          </view>
        </view>
        <text class="goods-total">¥{{item.totalPrice}}</text>
      </view>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="order-info-section card">
    <text class="section-title">订单信息</text>
    <view class="info-list">
      <view class="info-item">
        <text class="info-label">订单编号</text>
        <view class="info-value-row">
          <text class="info-value">{{orderDetail.id}}</text>
          <button class="copy-btn" bindtap="copyOrderId">复制</button>
        </view>
      </view>
      <view class="info-item">
        <text class="info-label">下单时间</text>
        <text class="info-value">{{orderDetail.createTime}}</text>
      </view>
      <view class="info-item" wx:if="{{orderDetail.payTime}}">
        <text class="info-label">付款时间</text>
        <text class="info-value">{{orderDetail.payTime}}</text>
      </view>
      <view class="info-item" wx:if="{{orderDetail.shipTime}}">
        <text class="info-label">发货时间</text>
        <text class="info-value">{{orderDetail.shipTime}}</text>
      </view>
      <view class="info-item" wx:if="{{orderDetail.completeTime}}">
        <text class="info-label">完成时间</text>
        <text class="info-value">{{orderDetail.completeTime}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">配送方式</text>
        <text class="info-value">{{orderDetail.delivery.name}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">支付方式</text>
        <text class="info-value">{{orderDetail.payment.name}}</text>
      </view>
      <view class="info-item" wx:if="{{orderDetail.remark}}">
        <text class="info-label">订单备注</text>
        <text class="info-value">{{orderDetail.remark}}</text>
      </view>
    </view>
  </view>

  <!-- 费用明细 -->
  <view class="cost-section card">
    <text class="section-title">费用明细</text>
    <view class="cost-list">
      <view class="cost-item">
        <text class="cost-label">商品总价</text>
        <text class="cost-value">¥{{orderDetail.goodsTotal}}</text>
      </view>
      <view class="cost-item">
        <text class="cost-label">运费</text>
        <text class="cost-value">{{orderDetail.deliveryFee > 0 ? '¥' + orderDetail.deliveryFee : '免费'}}</text>
      </view>
      <view class="cost-item" wx:if="{{orderDetail.couponDiscount > 0}}">
        <text class="cost-label">优惠券</text>
        <text class="cost-value discount">-¥{{orderDetail.couponDiscount}}</text>
      </view>
      <view class="cost-item total">
        <text class="cost-label">实付款</text>
        <text class="cost-value total-price">¥{{orderDetail.totalAmount}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="action-section" wx:if="{{actionButtons.length > 0}}">
    <view class="action-buttons">
      <button class="action-btn {{item.type}}" 
              wx:for="{{actionButtons}}" wx:key="action"
              bindtap="{{item.action}}" data-id="{{orderDetail.id}}">
        {{item.text}}
      </button>
    </view>
  </view>
</view>

<!-- 取消订单确认弹窗 -->
<view class="modal-overlay" wx:if="{{showCancelModal}}" bindtap="hideCancelModal">
  <view class="modal-content cancel-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">取消订单</text>
      <text class="modal-close" bindtap="hideCancelModal">×</text>
    </view>
    <view class="modal-body">
      <text class="cancel-tip">请选择取消原因：</text>
      <radio-group bindchange="onCancelReasonChange">
        <label class="cancel-reason" wx:for="{{cancelReasons}}" wx:key="*this">
          <radio value="{{item}}" />
          <text class="reason-text">{{item}}</text>
        </label>
      </radio-group>
    </view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="hideCancelModal">取消</button>
      <button class="btn btn-primary" bindtap="confirmCancel">确定</button>
    </view>
  </view>
</view>

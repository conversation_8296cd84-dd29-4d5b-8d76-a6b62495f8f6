// order-detail.js
const app = getApp();

Page({
  data: {
    orderId: '',
    orderDetail: {},
    statusConfig: {},
    actionButtons: [],
    
    // 取消订单
    showCancelModal: false,
    cancelReason: '',
    cancelReasons: [
      '不想要了',
      '商品信息填错了',
      '地址信息填错了',
      '商品降价了',
      '其他原因'
    ]
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail();
    } else {
      wx.showModal({
        title: '参数错误',
        content: '订单ID不能为空',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  // 加载订单详情
  async loadOrderDetail() {
    try {
      app.showLoading('加载中...');

      // 调用云函数获取订单详情
      const result = await wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: 'getOrderDetail',
          orderId: this.data.orderId
        }
      });

      if (result.result.success) {
        const orderDetail = result.result.data;
        this.processOrderDetail(orderDetail);
      } else {
        throw new Error(result.result.message || '获取订单详情失败');
      }
    } catch (error) {
      console.error('加载订单详情失败:', error);
      
      // 使用模拟数据
      this.loadMockOrderDetail();
    }

    app.hideLoading();
  },

  // 加载模拟订单详情
  loadMockOrderDetail() {
    const mockOrderDetail = {
      id: this.data.orderId,
      status: 'shipped',
      userId: 'user_1',
      items: [
        {
          id: 'item_1',
          name: '人参',
          spec: '50g装',
          price: 12.5,
          quantity: 2,
          imageUrl: '/images/medicine1.jpg',
          totalPrice: '25.00'
        },
        {
          id: 'item_2',
          name: '枸杞子',
          spec: '100g装',
          price: 8.8,
          quantity: 1,
          imageUrl: '/images/medicine2.jpg',
          totalPrice: '8.80'
        }
      ],
      address: {
        name: '张三',
        phone: '138****8888',
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        detail: '三里屯街道某某小区1号楼101室'
      },
      delivery: {
        name: '标准配送',
        description: '5-7个工作日送达'
      },
      payment: {
        name: '微信支付'
      },
      remark: '请尽快发货，谢谢！',
      goodsTotal: '33.80',
      deliveryFee: 0,
      couponDiscount: 0,
      totalAmount: '33.80',
      createTime: '2024-03-15 14:30:25',
      payTime: '2024-03-15 14:32:10',
      shipTime: '2024-03-16 09:15:30',
      logistics: {
        company: '顺丰速运',
        trackingNumber: 'SF1234567890',
        status: '运输中'
      }
    };

    this.processOrderDetail(mockOrderDetail);
  },

  // 处理订单详情数据
  processOrderDetail(orderDetail) {
    // 格式化时间
    orderDetail.createTime = this.formatTime(orderDetail.createTime);
    if (orderDetail.payTime) {
      orderDetail.payTime = this.formatTime(orderDetail.payTime);
    }
    if (orderDetail.shipTime) {
      orderDetail.shipTime = this.formatTime(orderDetail.shipTime);
    }
    if (orderDetail.completeTime) {
      orderDetail.completeTime = this.formatTime(orderDetail.completeTime);
    }

    // 设置状态配置
    const statusConfig = this.getStatusConfig(orderDetail.status);
    
    // 设置操作按钮
    const actionButtons = this.getActionButtons(orderDetail.status);

    this.setData({
      orderDetail,
      statusConfig,
      actionButtons
    });
  },

  // 获取状态配置
  getStatusConfig(status) {
    const statusConfigs = {
      'pending_payment': {
        icon: '/images/pending-payment.png',
        title: '等待买家付款',
        description: '请在24小时内完成支付，超时订单将自动取消'
      },
      'paid': {
        icon: '/images/paid.png',
        title: '买家已付款',
        description: '商家正在准备发货，请耐心等待'
      },
      'shipped': {
        icon: '/images/shipped.png',
        title: '商家已发货',
        description: '商品正在配送中，请注意查收'
      },
      'completed': {
        icon: '/images/completed.png',
        title: '交易完成',
        description: '感谢您的购买，欢迎再次光临'
      },
      'cancelled': {
        icon: '/images/cancelled.png',
        title: '订单已取消',
        description: '订单已取消，如有疑问请联系客服'
      }
    };

    return statusConfigs[status] || {
      icon: '/images/unknown.png',
      title: '未知状态',
      description: '订单状态异常，请联系客服'
    };
  },

  // 获取操作按钮
  getActionButtons(status) {
    const buttonConfigs = {
      'pending_payment': [
        { text: '取消订单', type: 'outline', action: 'cancelOrder' },
        { text: '立即付款', type: 'primary', action: 'payOrder' }
      ],
      'paid': [
        { text: '申请退款', type: 'outline', action: 'refundOrder' }
      ],
      'shipped': [
        { text: '查看物流', type: 'outline', action: 'viewLogistics' },
        { text: '确认收货', type: 'primary', action: 'confirmReceive' }
      ],
      'completed': [
        { text: '再次购买', type: 'outline', action: 'buyAgain' },
        { text: '评价', type: 'primary', action: 'evaluateOrder' }
      ],
      'cancelled': [
        { text: '再次购买', type: 'primary', action: 'buyAgain' }
      ]
    };

    return buttonConfigs[status] || [];
  },

  // 格式化时间
  formatTime(time) {
    if (typeof time === 'string') return time;
    
    const date = new Date(time);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    const second = date.getSeconds().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  },

  // 复制订单号
  copyOrderId() {
    wx.setClipboardData({
      data: this.data.orderDetail.id,
      success: () => {
        app.showSuccess('订单号已复制');
      }
    });
  },

  // 查看物流
  viewLogistics() {
    wx.navigateTo({
      url: `/pages/logistics/logistics?orderId=${this.data.orderId}`
    });
  },

  // 取消订单
  cancelOrder() {
    this.setData({
      showCancelModal: true,
      cancelReason: ''
    });
  },

  // 隐藏取消订单弹窗
  hideCancelModal() {
    this.setData({
      showCancelModal: false
    });
  },

  // 选择取消原因
  onCancelReasonChange(e) {
    this.setData({
      cancelReason: e.detail.value
    });
  },

  // 确认取消订单
  async confirmCancel() {
    if (!this.data.cancelReason) {
      app.showError('请选择取消原因');
      return;
    }

    try {
      app.showLoading('取消中...');

      const result = await wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: 'cancelOrder',
          orderId: this.data.orderId,
          reason: this.data.cancelReason
        }
      });

      if (result.result.success) {
        app.showSuccess('订单已取消');
        this.hideCancelModal();
        
        // 重新加载订单详情
        setTimeout(() => {
          this.loadOrderDetail();
        }, 1500);
      } else {
        throw new Error(result.result.message || '取消失败');
      }
    } catch (error) {
      console.error('取消订单失败:', error);
      app.showError('取消失败，请重试');
    }

    app.hideLoading();
  },

  // 立即付款
  async payOrder() {
    try {
      app.showLoading('发起支付...');
      
      // 模拟支付流程
      setTimeout(() => {
        app.hideLoading();
        app.showSuccess('支付成功');
        
        // 重新加载订单详情
        setTimeout(() => {
          this.loadOrderDetail();
        }, 1500);
      }, 2000);
    } catch (error) {
      console.error('支付失败:', error);
      app.hideLoading();
      app.showError('支付失败，请重试');
    }
  },

  // 申请退款
  refundOrder() {
    wx.showModal({
      title: '申请退款',
      content: '确定要申请退款吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            app.showLoading('申请中...');
            
            // 调用退款接口
            setTimeout(() => {
              app.hideLoading();
              app.showSuccess('退款申请已提交');
              
              // 重新加载订单详情
              setTimeout(() => {
                this.loadOrderDetail();
              }, 1500);
            }, 1500);
          } catch (error) {
            console.error('申请退款失败:', error);
            app.hideLoading();
            app.showError('申请失败，请重试');
          }
        }
      }
    });
  },

  // 确认收货
  confirmReceive() {
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            app.showLoading('确认中...');
            
            const result = await wx.cloud.callFunction({
              name: 'quickstartFunctions',
              data: {
                type: 'confirmReceive',
                orderId: this.data.orderId
              }
            });

            if (result.result.success) {
              app.showSuccess('确认收货成功');
              
              // 重新加载订单详情
              setTimeout(() => {
                this.loadOrderDetail();
              }, 1500);
            } else {
              throw new Error(result.result.message || '确认失败');
            }
          } catch (error) {
            console.error('确认收货失败:', error);
            app.showError('确认失败，请重试');
          }
          
          app.hideLoading();
        }
      }
    });
  },

  // 再次购买
  buyAgain() {
    const orderDetail = this.data.orderDetail;
    
    // 将订单商品添加到购物车
    const cartItems = orderDetail.items.map(item => ({
      id: `cart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      productId: item.id,
      name: item.name,
      price: item.price,
      imageUrl: item.imageUrl,
      type: item.type || 'product',
      category: item.category || '商品',
      quantity: item.quantity,
      selected: true,
      spec: item.spec || '',
      unit: item.unit || '',
      totalPrice: (item.price * item.quantity).toFixed(2)
    }));

    // 保存到购物车
    const userInfo = app.globalData.userInfo;
    if (userInfo) {
      const existingCart = wx.getStorageSync(`cart_${userInfo.id}`) || [];
      const newCart = [...existingCart, ...cartItems];
      wx.setStorageSync(`cart_${userInfo.id}`, newCart);
    }

    app.showSuccess('商品已添加到购物车');
    
    // 跳转到购物车
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/cart/cart'
      });
    }, 1500);
  },

  // 评价订单
  evaluateOrder() {
    wx.showModal({
      title: '商品评价',
      content: '评价功能正在开发中...',
      showCancel: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});

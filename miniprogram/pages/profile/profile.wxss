/**profile.wxss**/

/* 用户信息区域 */
.user-section {
  margin-bottom: 30rpx;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  position: relative;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 25rpx;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 8rpx;
}

.user-role {
  font-size: 24rpx;
  color: #228B22;
  margin-bottom: 5rpx;
}

.user-id {
  font-size: 20rpx;
  color: #999;
}

.user-badge {
  position: absolute;
  top: 0;
  right: 0;
}

.badge-text {
  background-color: #228B22;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 30rpx;
  border-top: 2rpx solid #F0F0F0;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 未登录提示 */
.login-prompt {
  text-align: center;
  padding: 60rpx 40rpx;
  margin-bottom: 30rpx;
}

.prompt-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.prompt-text {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 30rpx;
}

/* 管理员区域 */
.admin-section {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
}

.section-icon {
  width: 40rpx;
  height: 40rpx;
}

.admin-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.admin-item {
  background-color: #F5F2E8;
  border-radius: 15rpx;
  padding: 30rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.admin-icon {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 15rpx;
}

.admin-text {
  font-size: 24rpx;
  color: #333;
}

/* 菜单区域 */
.menu-section {
  margin-bottom: 30rpx;
}

.menu-group {
  margin-bottom: 25rpx;
}

.menu-header {
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #F0F0F0;
  margin-bottom: 20rpx;
}

.menu-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
}

/* 订单状态行 */
.order-status-row {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-top: 2rpx solid #F0F0F0;
  margin-top: 20rpx;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.status-icon {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 8rpx;
}

.status-text {
  font-size: 22rpx;
  color: #666;
}

.status-count {
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  background-color: #E74C3C;
  color: white;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #F5F5F5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.menu-count {
  font-size: 22rpx;
  color: #999;
  margin-right: 15rpx;
}

.menu-badge {
  background-color: #228B22;
  color: white;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-right: 15rpx;
}

.menu-arrow {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.5;
}

/* 退出登录 */
.logout-section {
  margin-bottom: 30rpx;
}

.logout-btn {
  width: 100%;
  padding: 30rpx;
  font-size: 28rpx;
  color: #E74C3C;
  border-color: #E74C3C;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .admin-grid {
    grid-template-columns: 1fr;
  }
  
  .user-header {
    flex-direction: column;
    text-align: center;
  }
  
  .user-avatar {
    margin-right: 0;
    margin-bottom: 20rpx;
  }
  
  .user-badge {
    position: static;
    margin-top: 15rpx;
  }
  
  .user-stats {
    justify-content: space-between;
    padding: 30rpx 20rpx 0;
  }
}

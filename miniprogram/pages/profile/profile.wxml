<!--profile.wxml-->
<view class="container">
  <!-- 用户信息 -->
  <view class="user-section card" wx:if="{{userInfo}}">
    <view class="user-header">
      <image class="user-avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="user-info">
        <text class="user-name">{{userInfo.nickName}}</text>
        <text class="user-role">{{userInfo.role === 'admin' ? '管理员' : userInfo.role === 'guest' ? '游客用户' : '普通用户'}}</text>
        <text class="user-id">ID: {{userInfo.id}}</text>
      </view>
      <view class="user-badge" wx:if="{{userInfo.role === 'admin'}}">
        <text class="badge-text">管理员</text>
      </view>
    </view>
    
    <view class="user-stats">
      <view class="stat-item">
        <text class="stat-number">{{userStats.favoriteCount}}</text>
        <text class="stat-label">收藏</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{userStats.viewCount}}</text>
        <text class="stat-label">浏览</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{userStats.shareCount}}</text>
        <text class="stat-label">分享</text>
      </view>
    </view>
  </view>

  <!-- 未登录状态 -->
  <view class="login-prompt card" wx:else>
    <image class="prompt-icon" src="/images/login-prompt.png" mode="aspectFit"></image>
    <text class="prompt-text">请先登录以享受更多功能</text>
    <button class="btn btn-primary" bindtap="goToLogin">立即登录</button>
  </view>

  <!-- 管理员快捷入口 -->
  <view class="admin-section card" wx:if="{{isAdmin}}">
    <view class="section-header">
      <text class="section-title">管理功能</text>
      <image class="section-icon" src="/images/admin-icon.png" mode="aspectFit"></image>
    </view>
    <view class="admin-grid">
      <view class="admin-item" bindtap="goToAdmin">
        <image class="admin-icon" src="/images/dashboard-icon.png" mode="aspectFit"></image>
        <text class="admin-text">管理后台</text>
      </view>
      <view class="admin-item" bindtap="manageData">
        <image class="admin-icon" src="/images/data-icon.png" mode="aspectFit"></image>
        <text class="admin-text">数据管理</text>
      </view>
      <view class="admin-item" bindtap="viewStats">
        <image class="admin-icon" src="/images/stats-icon.png" mode="aspectFit"></image>
        <text class="admin-text">统计分析</text>
      </view>
      <view class="admin-item" bindtap="systemSettings">
        <image class="admin-icon" src="/images/settings-icon.png" mode="aspectFit"></image>
        <text class="admin-text">系统设置</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group card">
      <view class="menu-header">
        <text class="menu-title">我的订单</text>
      </view>
      <view class="menu-item" bindtap="viewOrders" data-status="">
        <image class="menu-icon" src="/images/order-icon.png" mode="aspectFit"></image>
        <text class="menu-text">全部订单</text>
        <text class="menu-count">{{orderStats.total}}</text>
        <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="order-status-row">
        <view class="status-item" bindtap="viewOrders" data-status="pending_payment">
          <image class="status-icon" src="/images/pending-payment.png" mode="aspectFit"></image>
          <text class="status-text">待付款</text>
          <text class="status-count" wx:if="{{orderStats.pendingPayment > 0}}">{{orderStats.pendingPayment}}</text>
        </view>
        <view class="status-item" bindtap="viewOrders" data-status="paid">
          <image class="status-icon" src="/images/paid.png" mode="aspectFit"></image>
          <text class="status-text">待发货</text>
          <text class="status-count" wx:if="{{orderStats.paid > 0}}">{{orderStats.paid}}</text>
        </view>
        <view class="status-item" bindtap="viewOrders" data-status="shipped">
          <image class="status-icon" src="/images/shipped.png" mode="aspectFit"></image>
          <text class="status-text">待收货</text>
          <text class="status-count" wx:if="{{orderStats.shipped > 0}}">{{orderStats.shipped}}</text>
        </view>
        <view class="status-item" bindtap="viewOrders" data-status="completed">
          <image class="status-icon" src="/images/completed.png" mode="aspectFit"></image>
          <text class="status-text">已完成</text>
          <text class="status-count" wx:if="{{orderStats.completed > 0}}">{{orderStats.completed}}</text>
        </view>
      </view>
    </view>

    <view class="menu-group card">
      <view class="menu-header">
        <text class="menu-title">我的收藏</text>
      </view>
      <view class="menu-item" bindtap="viewFavorites" data-type="medicines">
        <image class="menu-icon" src="/images/medicine-icon.png" mode="aspectFit"></image>
        <text class="menu-text">收藏的中药材</text>
        <text class="menu-count">{{favoriteStats.medicines}}</text>
        <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="menu-item" bindtap="viewFavorites" data-type="products">
        <image class="menu-icon" src="/images/product-icon.png" mode="aspectFit"></image>
        <text class="menu-text">收藏的文创产品</text>
        <text class="menu-count">{{favoriteStats.products}}</text>
        <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="menu-item" bindtap="viewFavorites" data-type="articles">
        <image class="menu-icon" src="/images/article-icon.png" mode="aspectFit"></image>
        <text class="menu-text">收藏的养生文章</text>
        <text class="menu-count">{{favoriteStats.articles}}</text>
        <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>

    <view class="menu-group card">
      <view class="menu-header">
        <text class="menu-title">浏览记录</text>
      </view>
      <view class="menu-item" bindtap="viewHistory">
        <image class="menu-icon" src="/images/history-icon.png" mode="aspectFit"></image>
        <text class="menu-text">最近浏览</text>
        <text class="menu-count">{{historyCount}}</text>
        <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="menu-item" bindtap="clearHistory">
        <image class="menu-icon" src="/images/clear-icon.png" mode="aspectFit"></image>
        <text class="menu-text">清除记录</text>
        <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 管理员工具 -->
    <view class="menu-group card" wx:if="{{isAdmin}}">
      <view class="menu-header">
        <text class="menu-title">管理员工具</text>
      </view>
      <view class="menu-item" bindtap="goToDataImport">
        <image class="menu-icon" src="/images/import-icon.png" mode="aspectFit"></image>
        <text class="menu-text">数据导入工具</text>
        <text class="menu-badge">推荐</text>
        <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="menu-item" bindtap="goToAdminPanel">
        <image class="menu-icon" src="/images/admin-icon.png" mode="aspectFit"></image>
        <text class="menu-text">管理后台</text>
        <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>

    <view class="menu-group card">
      <view class="menu-header">
        <text class="menu-title">设置与帮助</text>
      </view>
      <view class="menu-item" bindtap="editProfile">
        <image class="menu-icon" src="/images/edit-icon.png" mode="aspectFit"></image>
        <text class="menu-text">编辑资料</text>
        <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="menu-item" bindtap="appSettings">
        <image class="menu-icon" src="/images/settings-icon.png" mode="aspectFit"></image>
        <text class="menu-text">应用设置</text>
        <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="menu-item" bindtap="aboutApp">
        <image class="menu-icon" src="/images/about-icon.png" mode="aspectFit"></image>
        <text class="menu-text">关于慧心制药</text>
        <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="menu-item" bindtap="feedback">
        <image class="menu-icon" src="/images/feedback-icon.png" mode="aspectFit"></image>
        <text class="menu-text">意见反馈</text>
        <image class="menu-arrow" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{userInfo}}">
    <button class="btn btn-outline logout-btn" bindtap="logout">退出登录</button>
  </view>
</view>

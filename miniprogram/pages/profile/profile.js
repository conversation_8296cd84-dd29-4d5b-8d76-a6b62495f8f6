// profile.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    isAdmin: false,
    userStats: {
      favoriteCount: 0,
      viewCount: 0,
      shareCount: 0
    },
    favoriteStats: {
      medicines: 0,
      products: 0,
      articles: 0
    },
    orderStats: {
      total: 0,
      pendingPayment: 0,
      paid: 0,
      shipped: 0,
      completed: 0
    },
    historyCount: 0
  },

  onLoad() {
    this.loadUserData();
  },

  onShow() {
    this.loadUserData();
  },

  // 加载用户数据
  loadUserData() {
    this.setData({
      userInfo: app.globalData.userInfo,
      isAdmin: app.globalData.isAdmin
    });
    
    if (app.globalData.userInfo) {
      this.loadUserStats();
      this.loadFavoriteStats();
      this.loadOrderStats();
      this.loadHistoryCount();
    }
  },

  // 加载用户统计数据
  async loadUserStats() {
    try {
      // 这里应该从云数据库获取用户的统计数据
      // 暂时使用模拟数据
      this.setData({
        userStats: {
          favoriteCount: 15,
          viewCount: 128,
          shareCount: 8
        }
      });
    } catch (error) {
      console.error('加载用户统计数据失败:', error);
    }
  },

  // 加载收藏统计数据
  async loadFavoriteStats() {
    try {
      // 这里应该从云数据库获取用户的收藏统计
      // 暂时使用模拟数据
      this.setData({
        favoriteStats: {
          medicines: 8,
          products: 5,
          articles: 12
        }
      });
    } catch (error) {
      console.error('加载收藏统计数据失败:', error);
    }
  },

  // 加载订单统计数据
  async loadOrderStats() {
    try {
      // 这里应该从云数据库获取用户的订单统计
      // 暂时使用模拟数据
      this.setData({
        orderStats: {
          total: 15,
          pendingPayment: 2,
          paid: 1,
          shipped: 3,
          completed: 9
        }
      });
    } catch (error) {
      console.error('加载订单统计数据失败:', error);
    }
  },

  // 加载浏览记录数量
  async loadHistoryCount() {
    try {
      // 这里应该从本地存储或云数据库获取浏览记录数量
      const history = wx.getStorageSync('browseHistory') || [];
      this.setData({
        historyCount: history.length
      });
    } catch (error) {
      console.error('加载浏览记录数量失败:', error);
    }
  },

  // 跳转到登录页
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 跳转到管理后台
  goToAdmin() {
    wx.navigateTo({
      url: '/pages/admin/admin'
    });
  },

  // 数据管理
  manageData() {
    wx.showActionSheet({
      itemList: ['中药材管理', '文创产品管理', '养生文章管理'],
      success: (res) => {
        const pages = [
          '/pages/medicines/medicines?mode=admin',
          '/pages/products/products?mode=admin',
          '/pages/articles/articles?mode=admin'
        ];
        
        wx.navigateTo({
          url: pages[res.tapIndex]
        });
      }
    });
  },

  // 查看统计
  viewStats() {
    wx.showModal({
      title: '统计分析',
      content: '统计分析功能正在开发中...',
      showCancel: false
    });
  },

  // 系统设置
  systemSettings() {
    wx.showModal({
      title: '系统设置',
      content: '系统设置功能正在开发中...',
      showCancel: false
    });
  },

  // 查看订单
  viewOrders(e) {
    const status = e.currentTarget.dataset.status;

    if (!this.data.userInfo) {
      this.goToLogin();
      return;
    }

    let url = '/pages/orders/orders';
    if (status) {
      url += `?status=${status}`;
    }

    wx.navigateTo({
      url: url
    });
  },

  // 查看收藏
  viewFavorites(e) {
    const type = e.currentTarget.dataset.type;

    if (!this.data.userInfo) {
      this.goToLogin();
      return;
    }

    wx.showModal({
      title: '收藏功能',
      content: '收藏功能正在开发中...',
      showCancel: false
    });
  },

  // 查看浏览记录
  viewHistory() {
    if (!this.data.userInfo) {
      this.goToLogin();
      return;
    }
    
    wx.showModal({
      title: '浏览记录',
      content: '浏览记录功能正在开发中...',
      showCancel: false
    });
  },

  // 清除浏览记录
  clearHistory() {
    if (!this.data.userInfo) {
      this.goToLogin();
      return;
    }
    
    wx.showModal({
      title: '清除记录',
      content: '确定要清除所有浏览记录吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('browseHistory');
            this.setData({
              historyCount: 0
            });
            app.showSuccess('浏览记录已清除');
          } catch (error) {
            console.error('清除浏览记录失败:', error);
            app.showError('清除失败');
          }
        }
      }
    });
  },

  // 编辑资料
  editProfile() {
    if (!this.data.userInfo) {
      this.goToLogin();
      return;
    }
    
    wx.showModal({
      title: '编辑资料',
      content: '编辑资料功能正在开发中...',
      showCancel: false
    });
  },

  // 应用设置
  appSettings() {
    wx.showActionSheet({
      itemList: ['通知设置', '隐私设置', '缓存清理', '字体大小'],
      success: (res) => {
        const settings = ['通知设置', '隐私设置', '缓存清理', '字体大小'];
        
        if (res.tapIndex === 2) {
          // 缓存清理
          this.clearCache();
        } else {
          wx.showModal({
            title: settings[res.tapIndex],
            content: `${settings[res.tapIndex]}功能正在开发中...`,
            showCancel: false
          });
        }
      }
    });
  },

  // 清理缓存
  clearCache() {
    wx.showModal({
      title: '清理缓存',
      content: '确定要清理应用缓存吗？这将清除临时文件和图片缓存。',
      success: (res) => {
        if (res.confirm) {
          app.showLoading('清理中...');
          
          setTimeout(() => {
            app.hideLoading();
            app.showSuccess('缓存清理完成');
          }, 1500);
        }
      }
    });
  },

  // 关于应用
  aboutApp() {
    wx.showModal({
      title: '关于慧心制药',
      content: '慧心制药 v1.0.0\n\n传承中医智慧，守护健康生活\n\n一款专注于中医药文化传播和养生知识分享的小程序。\n\n开发团队：慧心科技\n联系邮箱：<EMAIL>',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 意见反馈
  feedback() {
    wx.showActionSheet({
      itemList: ['功能建议', '问题反馈', '内容纠错', '其他意见'],
      success: (res) => {
        const types = ['功能建议', '问题反馈', '内容纠错', '其他意见'];
        
        wx.showModal({
          title: types[res.tapIndex],
          content: '感谢您的反馈！您可以通过以下方式联系我们：\n\n邮箱：<EMAIL>\n微信：huixin_support\n\n我们会认真对待每一条反馈意见。',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    });
  },

  // 数据导入工具
  goToDataImport() {
    if (!this.data.userInfo || this.data.userInfo.role !== 'admin') {
      app.showError('权限不足');
      return;
    }

    wx.navigateTo({
      url: '/pages/data-import/data-import'
    });
  },

  // 管理后台
  goToAdminPanel() {
    if (!this.data.userInfo || this.data.userInfo.role !== 'admin') {
      app.showError('权限不足');
      return;
    }

    wx.showModal({
      title: '管理后台',
      content: '管理后台功能正在开发中...',
      showCancel: false
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.clearUserInfo();
          this.setData({
            userInfo: null,
            isAdmin: false,
            userStats: {
              favoriteCount: 0,
              viewCount: 0,
              shareCount: 0
            },
            favoriteStats: {
              medicines: 0,
              products: 0,
              articles: 0
            },
            historyCount: 0
          });
          
          app.showSuccess('已退出登录');
          
          // 跳转到登录页
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }, 1500);
        }
      }
    });
  }
});

/* 中药材详情页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 商品头部 */
.product-header {
  background: white;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.product-image-section {
  position: relative;
  padding: 30rpx;
  text-align: center;
}

.main-image {
  width: 300rpx;
  height: 300rpx;
  margin: 0 auto 20rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.3);
  overflow: hidden;
  position: relative;
}

.medicine-detail-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}

.placeholder-icon {
  font-size: 80rpx;
  color: white;
}

.placeholder-text {
  font-size: 24rpx;
  color: white;
  opacity: 0.9;
}

.image-badges {
  display: flex;
  justify-content: center;
  gap: 15rpx;
}

.badge {
  padding: 6rpx 15rpx;
  border-radius: 15rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.badge.hot {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: white;
}

.badge.quality {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
}

.product-info-section {
  padding: 0 30rpx 30rpx;
}

.product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 10rpx;
}

.product-category {
  font-size: 22rpx;
  color: #228B22;
  background: rgba(34, 139, 34, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
  margin-bottom: 15rpx;
}

.product-effect {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: block;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.current-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #E74C3C;
}

.price-unit {
  font-size: 20rpx;
  color: #999;
}

.stock-info {
  font-size: 20rpx;
  color: #999;
  margin-left: auto;
}

.basic-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 22rpx;
  color: #666;
  width: 80rpx;
}

.info-value {
  font-size: 22rpx;
  color: #333;
  flex: 1;
}

/* 通用区块样式 */
.spec-section,
.quantity-section,
.detail-section,
.review-section,
.related-section {
  background: white;
  margin-bottom: 20rpx;
  padding: 25rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.08);
}

.section-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #8B4513;
  padding-left: 15rpx;
}

/* 规格选择 */
.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.spec-item {
  padding: 12rpx 24rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 25rpx;
  background: #f8f8f8;
  transition: all 0.3s ease;
}

.spec-item.selected {
  border-color: #8B4513;
  background: rgba(139, 69, 19, 0.1);
}

.spec-text {
  font-size: 22rpx;
  color: #333;
}

.spec-item.selected .spec-text {
  color: #8B4513;
  font-weight: bold;
}

/* 数量选择 */
.quantity-selector {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 50%;
  background: white;
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:active {
  background: #f0f0f0;
}

.quantity-input {
  width: 100rpx;
  height: 60rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  text-align: center;
  font-size: 24rpx;
  background: white;
}

.quantity-unit {
  font-size: 20rpx;
  color: #666;
}

/* 详细信息 */
.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-label {
  font-size: 22rpx;
  font-weight: bold;
  color: #8B4513;
}

.detail-text {
  font-size: 22rpx;
  color: #666;
  line-height: 1.6;
  padding-left: 15rpx;
  border-left: 3rpx solid #E0E0E0;
}

/* 用户评价 */
.review-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.review-item {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 15rpx;
}

.review-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 10rpx;
}

.reviewer-avatar {
  font-size: 32rpx;
}

.reviewer-info {
  flex: 1;
}

.reviewer-name {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.review-rating {
  display: flex;
  gap: 2rpx;
}

.star {
  font-size: 16rpx;
  color: #ddd;
}

.star.filled {
  color: #FFD700;
}

.review-date {
  font-size: 18rpx;
  color: #999;
}

.review-content {
  font-size: 22rpx;
  color: #666;
  line-height: 1.5;
}

/* 相关推荐 */
.related-scroll {
  white-space: nowrap;
}

.related-item {
  display: inline-block;
  width: 150rpx;
  margin-right: 20rpx;
  text-align: center;
  vertical-align: top;
}

.related-image {
  display: block;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border-radius: 15rpx;
  font-size: 50rpx;
  color: white;
  line-height: 120rpx;
  margin: 0 auto 10rpx;
}

.related-name {
  font-size: 20rpx;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-price {
  font-size: 18rpx;
  color: #E74C3C;
  font-weight: bold;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
  z-index: 1000;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.cart-btn {
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.3);
}

.buy-btn {
  background: linear-gradient(135deg, #E74C3C, #C0392B);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(231, 76, 60, 0.3);
}

.action-btn:active {
  transform: translateY(-2rpx);
  opacity: 0.9;
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 24rpx;
}

.bottom-placeholder {
  height: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f5f5f5;
}

.loading-content {
  text-align: center;
}

.loading-icon {
  font-size: 60rpx;
  display: block;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .spec-options {
    justify-content: center;
  }
  
  .quantity-selector {
    justify-content: center;
  }
  
  .bottom-actions {
    flex-direction: column;
    gap: 15rpx;
  }
}

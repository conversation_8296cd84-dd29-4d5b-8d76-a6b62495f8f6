// 中药材详情页面
var app = getApp();

Page({
  data: {
    medicineId: '',
    medicine: null,
    loading: true,
    quantity: 1,
    selectedSpec: '1克',
    specs: ['1克', '5克', '10克', '50克', '100克'],

    // 图片错误状态
    imageError: false,

    // 相关推荐
    relatedMedicines: [],

    // 用户评价
    reviews: [
      {
        id: '1',
        userName: '中医爱好者',
        avatar: '👨‍⚕️',
        rating: 5,
        content: '品质很好，药效明显，包装也很精美。',
        date: '2024-01-15',
        images: []
      },
      {
        id: '2',
        userName: '养生达人',
        avatar: '🧘‍♀️',
        rating: 4,
        content: '正宗的中药材，效果不错，会继续购买。',
        date: '2024-01-10',
        images: []
      }
    ]
  },

  onLoad: function(options) {
    console.log('中药材详情页面加载，参数:', options);
    if (options.id) {
      this.setData({
        medicineId: options.id
      });
      this.loadMedicineDetail(options.id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onShow: function() {
    // 页面显示时更新购物车数量
    if (app.updateCartBadge) {
      app.updateCartBadge();
    }
  },

  // 加载中药材详情
  loadMedicineDetail: function(id) {
    var that = this;
    that.setData({ loading: true });

    // 模拟从数据库或缓存中获取详细数据
    var medicineData = that.getMedicineData(id);
    
    if (medicineData) {
      that.setData({
        medicine: medicineData,
        loading: false,
        imageError: false
      });
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: medicineData.name + ' - 中药材详情'
      });
      
      // 加载相关推荐
      that.loadRelatedMedicines(medicineData.category);
    } else {
      that.setData({ loading: false });
      wx.showModal({
        title: '提示',
        content: '未找到该中药材信息',
        showCancel: false,
        success: function() {
          wx.navigateBack();
        }
      });
    }
  },

  // 获取中药材数据（模拟数据库查询）
  getMedicineData: function(id) {
    var medicines = {
      '1': {
        id: '1',
        name: '人参',
        category: '补气药',
        effect: '大补元气，复脉固脱，补脾益肺，生津止渴，安神益智',
        description: '人参为五加科植物人参的干燥根和根茎。具有大补元气，复脉固脱，补脾益肺，生津止渴，安神益智的功效。',
        detailedDescription: '人参，被誉为"百草之王"，是珍贵的中药材。主产于中国东北、朝鲜、韩国、日本、俄罗斯东部。人参的主要成分包括人参皂苷、多糖、氨基酸、维生素等多种活性物质。',
        price: 12.5,
        stock: 500,
        imageUrl: '/images/中药材/人参.jpg',
        viewCount: 1250,
        isHot: true,
        isQuality: true,
        origin: '长白山',
        specification: '特级',
        storageMethod: '密封，置阴凉干燥处',
        shelfLife: '36个月',
        usage: '煎汤，3-9g；研末，1-3g；或入丸、散',
        contraindications: '实证、热证忌服；不宜与藜芦同用',
        pharmacology: '具有抗疲劳、抗衰老、增强免疫力、调节血糖等作用',
        clinicalApplication: '用于体虚欲脱，肢冷脉微，脾虚食少，肺虚喘咳，津伤口渴，内热消渴，久病虚羸，惊悸失眠，阳痿宫冷'
      },
      '2': {
        id: '2',
        name: '黄芪',
        category: '补气药',
        effect: '补气升阳，固表止汗，利水消肿，生津养血',
        description: '黄芪为豆科植物蒙古黄芪或膜荚黄芪的干燥根。具有补气升阳，固表止汗，利水消肿，生津养血的功效。',
        detailedDescription: '黄芪，又名绵芪，是常用的补气中药。主产于内蒙古、山西、甘肃、黑龙江等地。黄芪含有黄芪甲苷、黄芪多糖、异黄酮类化合物等多种活性成分。',
        price: 9.2,
        stock: 800,
        imageUrl: '/images/中药材/黄芪.jpg',
        viewCount: 654,
        isHot: true,
        isQuality: true,
        origin: '内蒙古',
        specification: '一级',
        storageMethod: '密封，置通风干燥处',
        shelfLife: '24个月',
        usage: '煎汤，9-30g；或入丸、散',
        contraindications: '表实邪盛，气滞湿阻，食积停滞，痈疽初起或溃后热毒尚盛等实证忌服',
        pharmacology: '具有增强免疫功能、保肝、利尿、抗衰老、抗应激、降压和较广泛的抗菌作用',
        clinicalApplication: '用于气虚乏力，食少便溏，中气下陷，久泻脱肛，便血崩漏，表虚自汗，气虚水肿'
      },
      '7': {
        id: '7',
        name: '枸杞子',
        category: '补血药',
        effect: '滋补肝肾，益精明目',
        description: '枸杞子为茄科植物宁夏枸杞的干燥成熟果实。具有滋补肝肾，益精明目的功效。',
        detailedDescription: '枸杞子，又称枸杞果，是宁夏的特产。含有枸杞多糖、甜菜碱、胡萝卜素、维生素A、维生素C等多种营养成分。',
        price: 8.8,
        stock: 1000,
        imageUrl: '/images/中药材/枸杞子.jpg',
        viewCount: 980,
        isHot: true,
        isQuality: true,
        origin: '宁夏中宁',
        specification: '特优级',
        storageMethod: '密封，置阴凉干燥处，防蛀',
        shelfLife: '18个月',
        usage: '煎汤，6-12g；或入丸、散，或泡茶，或浸酒',
        contraindications: '外邪实热，脾虚有湿及泄泻者忌服',
        pharmacology: '具有免疫调节、抗氧化、抗衰老、抗肿瘤、抗疲劳、降血脂、降血糖、降血压、补肾、保肝、明目、养颜、健脑、排毒、保护生殖系统、抗辐射损伤等功能',
        clinicalApplication: '用于肝肾阴虚，腰膝酸软，头晕，目眩，目昏多泪，虚劳咳嗽，消渴，遗精'
      },
      // 添加更多中药材详情数据
      '3': {
        id: '3',
        name: '甘草',
        category: '补气药',
        effect: '补脾益气，清热解毒，祛痰止咳，缓急止痛，调和诸药',
        description: '甘草为豆科植物甘草、胀果甘草或光果甘草的干燥根和根茎。具有补脾益气，清热解毒，祛痰止咳，缓急止痛，调和诸药的功效。',
        detailedDescription: '甘草，又名国老，是最常用的中药材之一。主产于内蒙古、新疆、甘肃等地。甘草含有甘草酸、甘草次酸、甘草黄酮等多种活性成分。',
        price: 6.5,
        stock: 600,
        imageUrl: '/images/中药材/甘草.jpg',
        viewCount: 456,
        isHot: false,
        isQuality: false,
        origin: '内蒙古',
        specification: '一级',
        storageMethod: '密封，置通风干燥处',
        shelfLife: '36个月',
        usage: '煎汤，2-10g；或入丸、散',
        contraindications: '不宜与京大戟、芫花、甘遂、海藻同用',
        pharmacology: '具有抗炎、抗过敏、保肝、调节免疫等作用',
        clinicalApplication: '用于脾胃虚弱，倦怠乏力，心悸气短，咳嗽痰多，脘腹、四肢挛急疼痛，痈肿疮毒，缓解药物毒性、烈性'
      },
      '4': {
        id: '4',
        name: '白术',
        category: '补气药',
        effect: '健脾益气，燥湿利水，止汗，安胎',
        description: '白术为菊科植物白术的干燥根茎。具有健脾益气，燥湿利水，止汗，安胎的功效。',
        detailedDescription: '白术，又名于术，是健脾的要药。主产于浙江、安徽、湖北等地。白术含有白术内酯、白术多糖等活性成分。',
        price: 11.8,
        stock: 350,
        imageUrl: '/images/中药材/白术.jpg',
        viewCount: 234,
        isHot: false,
        isQuality: true,
        origin: '浙江',
        specification: '特级',
        storageMethod: '密封，置阴凉干燥处',
        shelfLife: '24个月',
        usage: '煎汤，6-12g；或入丸、散',
        contraindications: '阴虚燥渴，气滞胀闷者忌服',
        pharmacology: '具有调节胃肠运动、抗溃疡、保肝、利尿、抗氧化等作用',
        clinicalApplication: '用于脾虚食少，腹胀泄泻，痰饮眩悸，水肿，自汗，胎动不安'
      },
      '11': {
        id: '11',
        name: '金银花',
        category: '清热药',
        effect: '清热解毒，疏散风热',
        description: '金银花为忍冬科植物忍冬的干燥花蕾或带初开的花。具有清热解毒，疏散风热的功效。',
        detailedDescription: '金银花，又名忍冬花，是清热解毒的良药。主产于山东、河南、河北等地。金银花含有绿原酸、异绿原酸、木犀草苷等活性成分。',
        price: 16.8,
        stock: 420,
        imageUrl: '/images/中药材/金银花.jpg',
        viewCount: 890,
        isHot: true,
        isQuality: true,
        origin: '山东',
        specification: '特级',
        storageMethod: '密封，置阴凉干燥处',
        shelfLife: '24个月',
        usage: '煎汤，6-15g；或入丸、散',
        contraindications: '脾胃虚寒及气虚疮疡脓清者忌服',
        pharmacology: '具有抗菌、抗病毒、抗炎、解热等作用',
        clinicalApplication: '用于痈肿疔疮，喉痹，丹毒，热毒血痢，风热感冒，温病发热'
      },
      '5': {
        id: '5',
        name: '党参',
        category: '补气药',
        effect: '健脾益肺，养血生津',
        description: '党参为桔梗科植物党参、素花党参或川党参的干燥根。具有健脾益肺，养血生津的功效。',
        detailedDescription: '党参，又名防风党参、黄参，是常用的补气中药。主产于山西、陕西、甘肃、四川等地。',
        price: 8.9,
        stock: 450,
        imageUrl: '/images/中药材/党参.jpg',
        viewCount: 389,
        isHot: false,
        isQuality: true,
        origin: '山西',
        specification: '一级',
        storageMethod: '密封，置通风干燥处',
        shelfLife: '24个月',
        usage: '煎汤，9-30g；或入丸、散',
        contraindications: '气滞、怒火盛者忌服',
        pharmacology: '具有增强免疫功能、抗疲劳、抗缺氧等作用',
        clinicalApplication: '用于脾肺气虚，食少倦怠，咳嗽虚喘，气血不足，面色萎黄，心悸气短'
      },
      '6': {
        id: '6',
        name: '当归',
        category: '补血药',
        effect: '补血活血，调经止痛，润肠通便',
        description: '当归为伞形科植物当归的干燥根。具有补血活血，调经止痛，润肠通便的功效。',
        detailedDescription: '当归，被誉为"血中圣药"，是补血活血的要药。主产于甘肃、云南、四川、青海、陕西、湖北等地。',
        price: 15.6,
        stock: 300,
        imageUrl: '/images/中药材/当归.jpg',
        viewCount: 756,
        isHot: false,
        isQuality: true,
        origin: '甘肃',
        specification: '特级',
        storageMethod: '密封，置阴凉干燥处',
        shelfLife: '24个月',
        usage: '煎汤，6-12g；或入丸、散',
        contraindications: '湿盛中满、大便溏泄者慎服',
        pharmacology: '具有补血、活血、调经、镇痛、润肠等作用',
        clinicalApplication: '用于血虚萎黄，眩晕心悸，月经不调，经闭痛经，虚寒腹痛，肠燥便秘，风湿痹痛，跌扑损伤，痈疽疮疡'
      }
    };

    // 如果没有找到对应的详情数据，返回一个通用的数据结构
    if (!medicines[id]) {
      return {
        id: id,
        name: '中药材详情',
        category: '其他',
        effect: '功效待补充',
        description: '该中药材的详细信息正在完善中...',
        detailedDescription: '更多详细信息请咨询专业中医师。',
        price: 0,
        stock: 0,
        imageUrl: '/images/中药材/默认.jpg',
        viewCount: 0,
        isHot: false,
        isQuality: false,
        origin: '待补充',
        specification: '待补充',
        storageMethod: '密封，置阴凉干燥处',
        shelfLife: '24个月',
        usage: '请遵医嘱',
        contraindications: '请咨询医师',
        pharmacology: '待补充',
        clinicalApplication: '请咨询专业中医师'
      };
    }

    return medicines[id];
  },

  // 加载相关推荐
  loadRelatedMedicines: function(category) {
    // 模拟相关推荐数据
    var related = [
      {
        id: '2',
        name: '黄芪',
        category: '补气药',
        price: 9.2,
        imageUrl: '/images/中药材/黄芪.jpg'
      },
      {
        id: '3',
        name: '甘草',
        category: '补气药',
        price: 6.5,
        imageUrl: '/images/中药材/甘草.jpg'
      },
      {
        id: '4',
        name: '白术',
        category: '补气药',
        price: 11.8,
        imageUrl: '/images/中药材/白术.jpg'
      }
    ];
    
    this.setData({
      relatedMedicines: related
    });
  },

  // 选择规格
  selectSpec: function(e) {
    var spec = e.currentTarget.dataset.spec;
    this.setData({
      selectedSpec: spec
    });
  },

  // 数量减少
  decreaseQuantity: function() {
    var quantity = this.data.quantity;
    if (quantity > 1) {
      this.setData({
        quantity: quantity - 1
      });
    }
  },

  // 数量增加
  increaseQuantity: function() {
    var quantity = this.data.quantity;
    var stock = this.data.medicine.stock;
    if (quantity < stock) {
      this.setData({
        quantity: quantity + 1
      });
    } else {
      wx.showToast({
        title: '库存不足',
        icon: 'none'
      });
    }
  },

  // 输入数量
  inputQuantity: function(e) {
    var value = parseInt(e.detail.value) || 1;
    var stock = this.data.medicine.stock;
    if (value > stock) {
      value = stock;
      wx.showToast({
        title: '超出库存限制',
        icon: 'none'
      });
    }
    this.setData({
      quantity: value
    });
  },

  // 添加到购物车
  addToCart: function() {
    var that = this;
    var medicine = that.data.medicine;
    var quantity = that.data.quantity;
    var selectedSpec = that.data.selectedSpec;

    if (!medicine) {
      wx.showToast({
        title: '商品信息错误',
        icon: 'none'
      });
      return;
    }

    // 确保有用户信息
    that.ensureUserInfo();

    // 获取用户信息
    var userInfo = app.globalData.userInfo;
    if (!userInfo) {
      wx.showToast({
        title: '用户信息错误',
        icon: 'none'
      });
      return;
    }

    // 创建购物车商品
    var cartItem = {
      id: 'cart_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      productId: medicine.id,
      name: medicine.name,
      price: medicine.price,
      imageUrl: medicine.imageUrl,
      type: 'medicine',
      category: medicine.category,
      quantity: quantity,
      selected: true,
      spec: selectedSpec,
      unit: '/克',
      totalPrice: (medicine.price * quantity).toFixed(2)
    };

    // 获取当前购物车数据
    var cartKey = 'cart_' + userInfo.id;
    var cartData = wx.getStorageSync(cartKey) || [];

    // 检查是否已存在相同商品和规格
    var existingIndex = -1;
    for (var i = 0; i < cartData.length; i++) {
      if (cartData[i].productId === medicine.id && cartData[i].spec === selectedSpec) {
        existingIndex = i;
        break;
      }
    }

    if (existingIndex >= 0) {
      // 如果已存在，增加数量
      cartData[existingIndex].quantity += quantity;
      cartData[existingIndex].totalPrice = (cartData[existingIndex].price * cartData[existingIndex].quantity).toFixed(2);
    } else {
      // 如果不存在，添加新商品
      cartData.push(cartItem);
    }

    try {
      // 保存到本地存储
      wx.setStorageSync(cartKey, cartData);

      // 显示成功提示
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      });

      // 更新购物车徽章
      if (app.updateCartBadge) {
        app.updateCartBadge();
      }

    } catch (error) {
      console.error('保存购物车数据失败:', error);
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      });
    }
  },

  // 立即购买
  buyNow: function() {
    // 先添加到购物车
    this.addToCart();
    
    // 跳转到购物车页面
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/cart/cart'
      });
    }, 1000);
  },

  // 查看相关商品
  viewRelated: function(e) {
    var id = e.currentTarget.dataset.id;
    wx.redirectTo({
      url: '/pages/medicine-detail/medicine-detail?id=' + id
    });
  },

  // 确保用户信息存在
  ensureUserInfo: function() {
    if (!app.globalData.userInfo) {
      var guestUser = {
        id: 'guest_' + Date.now(),
        name: '游客用户',
        avatar: '👤',
        role: 'user',
        isGuest: true
      };
      app.setUserInfo(guestUser);
    }
  },

  // 图片加载成功
  onImageLoad: function(e) {
    console.log('中药材详情图片加载成功');
    this.setData({
      imageError: false
    });
  },

  // 图片加载失败
  onImageError: function(e) {
    console.log('中药材详情图片加载失败:', e);
    this.setData({
      imageError: true
    });
  },

  // 分享功能
  onShareAppMessage: function() {
    var medicine = this.data.medicine;
    return {
      title: medicine ? medicine.name + ' - 优质中药材' : '慧心制药 - 中药材详情',
      path: '/pages/medicine-detail/medicine-detail?id=' + this.data.medicineId,
      imageUrl: medicine ? medicine.imageUrl : ''
    };
  }
});

<!--中药材详情页面-->
<view class="container" wx:if="{{!loading}}">
  <!-- 商品主图和基本信息 -->
  <view class="product-header">
    <view class="product-image-section">
      <view class="main-image">
        <image class="medicine-detail-image" src="{{medicine.imageUrl}}" mode="aspectFill" wx:if="{{medicine.imageUrl && !imageError}}" binderror="onImageError" bindload="onImageLoad"></image>
        <view class="image-placeholder" wx:if="{{!medicine.imageUrl || imageError}}">
          <text class="placeholder-icon">🌿</text>
          <text class="placeholder-text">{{medicine.name}}</text>
        </view>
      </view>
      <view class="image-badges" wx:if="{{medicine.isHot || medicine.isQuality}}">
        <text class="badge hot" wx:if="{{medicine.isHot}}">🔥 热销</text>
        <text class="badge quality" wx:if="{{medicine.isQuality}}">✨ 优质</text>
      </view>
    </view>
    
    <view class="product-info-section">
      <text class="product-name">{{medicine.name}}</text>
      <text class="product-category">{{medicine.category}}</text>
      <text class="product-effect">{{medicine.effect}}</text>
      
      <view class="price-section">
        <text class="current-price">¥{{medicine.price}}</text>
        <text class="price-unit">/克</text>
        <text class="stock-info">库存：{{medicine.stock}}克</text>
      </view>
      
      <view class="basic-info">
        <view class="info-item">
          <text class="info-label">产地：</text>
          <text class="info-value">{{medicine.origin}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">规格：</text>
          <text class="info-value">{{medicine.specification}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">浏览：</text>
          <text class="info-value">{{medicine.viewCount}}次</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 规格选择 -->
  <view class="spec-section">
    <view class="section-title">选择规格</view>
    <view class="spec-options">
      <view class="spec-item {{selectedSpec === item ? 'selected' : ''}}" 
            wx:for="{{specs}}" wx:key="*this"
            bindtap="selectSpec" data-spec="{{item}}">
        <text class="spec-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 数量选择 -->
  <view class="quantity-section">
    <view class="section-title">购买数量</view>
    <view class="quantity-selector">
      <button class="quantity-btn decrease" bindtap="decreaseQuantity">-</button>
      <input class="quantity-input" type="number" value="{{quantity}}" bindinput="inputQuantity" />
      <button class="quantity-btn increase" bindtap="increaseQuantity">+</button>
      <text class="quantity-unit">{{selectedSpec}}</text>
    </view>
  </view>

  <!-- 详细信息 -->
  <view class="detail-section">
    <view class="section-title">详细信息</view>
    <view class="detail-content">
      <view class="detail-item">
        <text class="detail-label">药材描述</text>
        <text class="detail-text">{{medicine.detailedDescription}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">功效主治</text>
        <text class="detail-text">{{medicine.clinicalApplication}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">用法用量</text>
        <text class="detail-text">{{medicine.usage}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">注意事项</text>
        <text class="detail-text">{{medicine.contraindications}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">药理作用</text>
        <text class="detail-text">{{medicine.pharmacology}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">贮藏方法</text>
        <text class="detail-text">{{medicine.storageMethod}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">有效期</text>
        <text class="detail-text">{{medicine.shelfLife}}</text>
      </view>
    </view>
  </view>

  <!-- 用户评价 -->
  <view class="review-section">
    <view class="section-title">用户评价</view>
    <view class="review-list">
      <view class="review-item" wx:for="{{reviews}}" wx:key="id">
        <view class="review-header">
          <text class="reviewer-avatar">{{item.avatar}}</text>
          <view class="reviewer-info">
            <text class="reviewer-name">{{item.userName}}</text>
            <view class="review-rating">
              <text class="star {{index < item.rating ? 'filled' : ''}}" 
                    wx:for="{{[1,2,3,4,5]}}" wx:key="*this" wx:for-index="index">⭐</text>
            </view>
          </view>
          <text class="review-date">{{item.date}}</text>
        </view>
        <text class="review-content">{{item.content}}</text>
      </view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section" wx:if="{{relatedMedicines.length > 0}}">
    <view class="section-title">相关推荐</view>
    <scroll-view class="related-scroll" scroll-x="true">
      <view class="related-item" wx:for="{{relatedMedicines}}" wx:key="id"
            bindtap="viewRelated" data-id="{{item.id}}">
        <text class="related-image">{{item.imageUrl}}</text>
        <text class="related-name">{{item.name}}</text>
        <text class="related-price">¥{{item.price}}/克</text>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="action-btn cart-btn" bindtap="addToCart">
      <text class="btn-icon">🛒</text>
      <text class="btn-text">加入购物车</text>
    </button>
    <button class="action-btn buy-btn" bindtap="buyNow">
      <text class="btn-icon">💰</text>
      <text class="btn-text">立即购买</text>
    </button>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-content">
    <text class="loading-icon">⏳</text>
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 占位，防止底部按钮遮挡内容 -->
<view class="bottom-placeholder"></view>

// 布局预览页面
Page({
  data: {
    currentTab: 0,
    tabs: ['中药材布局', '文创产品布局'],
    
    // 中药材示例数据
    sampleMedicines: [
      {
        id: '1',
        name: '人参',
        category: '补气药',
        effect: '大补元气，复脉固脱，补脾益肺，生津止渴',
        price: 12.5,
        stock: 500,
        imageUrl: '🌿',
        isHot: true,
        isQuality: true
      },
      {
        id: '2',
        name: '枸杞子',
        category: '补血药',
        effect: '滋补肝肾，益精明目',
        price: 8.8,
        stock: 1000,
        imageUrl: '🍇',
        isHot: true,
        isQuality: true
      },
      {
        id: '3',
        name: '金银花',
        category: '清热药',
        effect: '清热解毒，疏散风热',
        price: 18.5,
        stock: 350,
        imageUrl: '🌼',
        isHot: false,
        isQuality: true
      }
    ],
    
    // 文创产品示例数据
    sampleProducts: [
      {
        id: '1',
        name: '中医养生茶具套装',
        category: '茶具',
        description: '精美的紫砂茶具，包含茶壶、茶杯、茶盘等',
        price: 168,
        originalPrice: 198,
        sales: 125,
        imageUrl: '🍵',
        isHot: true,
        rating: 4.8
      },
      {
        id: '2',
        name: '本草纲目典藏版',
        category: '书籍',
        description: '李时珍经典著作《本草纲目》典藏版',
        price: 58,
        originalPrice: 68,
        sales: 234,
        imageUrl: '📚',
        isHot: false,
        rating: 4.9
      },
      {
        id: '3',
        name: '艾灸养生套装',
        category: '养生用品',
        description: '传统艾灸工具套装，包含艾条、艾灸盒等',
        price: 128,
        originalPrice: 158,
        sales: 67,
        imageUrl: '🔥',
        isHot: true,
        rating: 4.7
      }
    ]
  },

  onLoad: function() {
    console.log('布局预览页面加载');
  },

  // 切换标签页
  switchTab: function(e) {
    var index = e.currentTarget.dataset.index;
    this.setData({
      currentTab: index
    });
  },

  // 模拟添加到购物车
  addToCart: function(e) {
    var item = e.currentTarget.dataset.item;
    wx.showToast({
      title: '已添加: ' + item.name,
      icon: 'success',
      duration: 1500
    });
  },

  // 查看详情
  viewDetail: function(e) {
    var item = e.currentTarget.dataset.item;
    var type = e.currentTarget.dataset.type;
    
    wx.showModal({
      title: '查看详情',
      content: '商品名称: ' + item.name + '\n类型: ' + type,
      showCancel: false
    });
  },

  // 返回首页
  goHome: function() {
    wx.navigateBack();
  },

  // 跳转到实际页面
  goToMedicines: function() {
    wx.navigateTo({
      url: '/pages/medicines/medicines'
    });
  },

  goToProducts: function() {
    wx.navigateTo({
      url: '/pages/products/products'
    });
  }
});

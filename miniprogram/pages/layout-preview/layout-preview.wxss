/* 布局预览页面样式 */
@import '../medicines/medicines.wxss';
@import '../products/products.wxss';

.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 8rpx;
}

.subtitle {
  font-size: 20rpx;
  color: #666;
}

/* 标签页 */
.tab-bar {
  display: flex;
  background: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.1);
}

.tab-item {
  flex: 1;
  padding: 20rpx;
  text-align: center;
  background: #f8f8f8;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #8B4513, #A0522D);
}

.tab-text {
  font-size: 22rpx;
  font-weight: bold;
  color: #666;
}

.tab-item.active .tab-text {
  color: white;
}

/* 内容区域 */
.tab-content {
  margin-bottom: 30rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  padding: 15rpx 20rpx;
  background: white;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.08);
}

.title-icon {
  font-size: 24rpx;
}

.title-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
}

/* 中药材列表样式覆盖 */
.medicine-list {
  margin-bottom: 20rpx;
}

.medicine-item {
  margin-bottom: 15rpx;
}

/* 文创产品网格样式覆盖 */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
  gap: 15rpx;
  margin-bottom: 20rpx;
}

/* 操作按钮 */
.action-section,
.back-section {
  margin-bottom: 20rpx;
}

.preview-btn,
.back-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 18rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.preview-btn {
  background: linear-gradient(135deg, #4169E1, #6495ED);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(65, 105, 225, 0.3);
}

.back-btn {
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.3);
}

.preview-btn:active,
.back-btn:active {
  transform: translateY(-2rpx);
  opacity: 0.9;
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 22rpx;
}

/* 优化说明 */
.optimization-info {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.info-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 20rpx;
  text-align: center;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.info-icon {
  font-size: 20rpx;
  width: 30rpx;
  text-align: center;
}

.info-text {
  font-size: 20rpx;
  color: #666;
  flex: 1;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .product-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-bar {
    flex-direction: column;
  }
  
  .container {
    padding: 15rpx;
  }
}

/* 动画效果 */
.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

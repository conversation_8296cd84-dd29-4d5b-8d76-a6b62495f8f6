<!--布局预览页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">🎨 布局预览</text>
    <text class="subtitle">查看优化后的界面效果</text>
  </view>

  <!-- 标签页切换 -->
  <view class="tab-bar">
    <view class="tab-item {{currentTab === index ? 'active' : ''}}" 
          wx:for="{{tabs}}" wx:key="*this" wx:for-index="index"
          bindtap="switchTab" data-index="{{index}}">
      <text class="tab-text">{{item}}</text>
    </view>
  </view>

  <!-- 中药材布局预览 -->
  <view class="tab-content" wx:if="{{currentTab === 0}}">
    <view class="section-title">
      <text class="title-icon">🌿</text>
      <text class="title-text">中药材列表布局</text>
    </view>
    
    <view class="medicine-list">
      <view class="medicine-item list-item medicine-card" 
            wx:for="{{sampleMedicines}}" wx:key="id"
            bindtap="viewDetail" data-item="{{item}}" data-type="中药材">
        <image class="medicine-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
        <view class="medicine-info">
          <text class="medicine-name">{{item.name}}</text>
          <text class="medicine-category">{{item.category}}</text>
          <text class="medicine-effect">功效：{{item.effect}}</text>
          <view class="medicine-meta">
            <text class="medicine-price">¥{{item.price}}/克</text>
            <text class="medicine-stock">库存：{{item.stock}}克</text>
          </view>
          <button class="cart-btn add-cart-btn" 
                  bindtap="addToCart" data-item="{{item}}" catchtap="stopPropagation">
            加入购物车
          </button>
        </view>
      </view>
    </view>
    
    <view class="action-section">
      <button class="preview-btn" bindtap="goToMedicines">
        <text class="btn-icon">👀</text>
        <text class="btn-text">查看实际页面</text>
      </button>
    </view>
  </view>

  <!-- 文创产品布局预览 -->
  <view class="tab-content" wx:if="{{currentTab === 1}}">
    <view class="section-title">
      <text class="title-icon">🎋</text>
      <text class="title-text">文创产品网格布局</text>
    </view>
    
    <view class="product-grid">
      <view class="product-item" 
            wx:for="{{sampleProducts}}" wx:key="id"
            bindtap="viewDetail" data-item="{{item}}" data-type="文创产品">
        <view class="product-image-container">
          <image class="product-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
        </view>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-category">{{item.category}}</text>
          <view class="product-meta">
            <text class="product-price">¥{{item.price}}</text>
            <text class="product-sales">销量{{item.sales}}</text>
          </view>
          <button class="cart-btn add-cart-btn" 
                  bindtap="addToCart" data-item="{{item}}" catchtap="stopPropagation">
            加入购物车
          </button>
        </view>
      </view>
    </view>
    
    <view class="action-section">
      <button class="preview-btn" bindtap="goToProducts">
        <text class="btn-icon">👀</text>
        <text class="btn-text">查看实际页面</text>
      </button>
    </view>
  </view>

  <!-- 优化说明 -->
  <view class="optimization-info">
    <view class="info-title">✨ 布局优化要点</view>
    <view class="info-list">
      <view class="info-item">
        <text class="info-icon">📐</text>
        <text class="info-text">图片与文本垂直对齐，视觉更协调</text>
      </view>
      <view class="info-item">
        <text class="info-icon">🎨</text>
        <text class="info-text">标签样式统一，间距合理</text>
      </view>
      <view class="info-item">
        <text class="info-icon">📱</text>
        <text class="info-text">响应式设计，适配不同屏幕</text>
      </view>
      <view class="info-item">
        <text class="info-icon">⚡</text>
        <text class="info-text">交互反馈优化，用户体验提升</text>
      </view>
    </view>
  </view>

  <!-- 返回按钮 -->
  <view class="back-section">
    <button class="back-btn" bindtap="goHome">
      <text class="btn-icon">🏠</text>
      <text class="btn-text">返回首页</text>
    </button>
  </view>
</view>

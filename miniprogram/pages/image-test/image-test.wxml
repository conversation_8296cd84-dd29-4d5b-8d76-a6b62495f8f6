<!--图片替换测试页面-->
<view class="container">
  <view class="header">
    <text class="title">🖼️ 图片替换测试</text>
    <text class="subtitle">测试更新后的图片是否能正确加载</text>
  </view>

  <!-- 测试统计 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{testResults.length}}</text>
        <text class="stat-label">总图片数</text>
      </view>
      <view class="stat-item success">
        <text class="stat-number">0</text>
        <text class="stat-label">加载成功</text>
      </view>
      <view class="stat-item error">
        <text class="stat-number">0</text>
        <text class="stat-label">加载失败</text>
      </view>
      <view class="stat-item testing">
        <text class="stat-number">0</text>
        <text class="stat-label">待测试</text>
      </view>
    </view>
  </view>

  <!-- 图片预览 -->
  <view class="preview-section">
    <view class="section-title">
      <text class="title-icon">🎨</text>
      <text class="title-text">图片预览</text>
    </view>
    
    <view class="image-grid">
      <view class="image-item" wx:for="{{testResults}}" wx:key="index">
        <view class="image-container">
          <image 
            class="test-image" 
            src="{{item.path}}" 
            mode="aspectFill"
            bindload="onImageLoad"
            binderror="onImageError"
            data-index="{{index}}">
          </image>
          <view class="image-overlay">
            <text class="status-icon {{item.status}}">
              {{item.status === 'success' ? '✅' : item.status === 'error' ? '❌' : item.status === 'testing' ? '⏳' : '❓'}}
            </text>
          </view>
        </view>
        <view class="image-info">
          <text class="image-category">{{item.category}}</text>
          <text class="image-name">{{item.name}}</text>
          <text class="image-path">{{item.path}}</text>
          <text class="image-status {{item.status}}">{{item.message}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 详细结果 -->
  <view class="results-section">
    <view class="section-title">
      <text class="title-icon">📋</text>
      <text class="title-text">详细结果</text>
    </view>
    
    <view class="result-list">
      <view class="result-item" wx:for="{{testResults}}" wx:key="index">
        <view class="result-info">
          <text class="result-category">{{item.category}}</text>
          <text class="result-name">{{item.name}}</text>
          <text class="result-path">{{item.path}}</text>
        </view>
        <view class="result-status">
          <text class="status-icon {{item.status}}">
            {{item.status === 'success' ? '✅' : item.status === 'error' ? '❌' : item.status === 'testing' ? '⏳' : '❓'}}
          </text>
          <text class="status-text {{item.status}}">{{item.message}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="retry-btn" bindtap="retryTest" disabled="{{testing}}">
      <text class="btn-icon">🔄</text>
      <text class="btn-text">{{testing ? '测试中...' : '重新测试'}}</text>
    </button>
    <button class="back-btn" bindtap="goHome">
      <text class="btn-icon">🏠</text>
      <text class="btn-text">返回首页</text>
    </button>
  </view>
</view>

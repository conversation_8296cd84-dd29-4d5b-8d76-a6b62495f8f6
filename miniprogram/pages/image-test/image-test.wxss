/* 图片替换测试页面样式 */
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 8rpx;
}

.subtitle {
  font-size: 20rpx;
  color: #666;
}

/* 统计区域 */
.stats-section {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  border-radius: 15rpx;
  background: #f8f8f8;
}

.stat-item.success {
  background: rgba(34, 139, 34, 0.1);
}

.stat-item.error {
  background: rgba(231, 76, 60, 0.1);
}

.stat-item.testing {
  background: rgba(255, 165, 0, 0.1);
}

.stat-number {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 18rpx;
  color: #666;
}

/* 图片预览区域 */
.preview-section,
.results-section,
.action-section {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-icon {
  font-size: 24rpx;
}

.title-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.image-item {
  border-radius: 15rpx;
  overflow: hidden;
  background: #f8f8f8;
}

.image-container {
  position: relative;
  width: 100%;
  height: 200rpx;
  overflow: hidden;
}

.test-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-info {
  padding: 15rpx;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.image-category {
  font-size: 16rpx;
  color: #8B4513;
  font-weight: bold;
}

.image-name {
  font-size: 20rpx;
  color: #333;
  font-weight: bold;
}

.image-path {
  font-size: 14rpx;
  color: #999;
  font-family: monospace;
  word-break: break-all;
}

.image-status {
  font-size: 16rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  align-self: flex-start;
}

.image-status.success {
  background: rgba(34, 139, 34, 0.1);
  color: #228B22;
}

.image-status.error {
  background: rgba(231, 76, 60, 0.1);
  color: #E74C3C;
}

.image-status.testing {
  background: rgba(255, 165, 0, 0.1);
  color: #FFA500;
}

/* 详细结果区域 */
.result-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.result-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.result-category {
  font-size: 16rpx;
  color: #8B4513;
  font-weight: bold;
}

.result-name {
  font-size: 20rpx;
  color: #333;
  font-weight: bold;
}

.result-path {
  font-size: 14rpx;
  color: #999;
  font-family: monospace;
  word-break: break-all;
}

.result-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  min-width: 80rpx;
}

.status-icon {
  font-size: 24rpx;
}

.status-icon.success {
  color: #228B22;
}

.status-icon.error {
  color: #E74C3C;
}

.status-icon.testing {
  color: #FFA500;
}

.status-text {
  font-size: 16rpx;
  text-align: center;
}

.status-text.success {
  color: #228B22;
}

.status-text.error {
  color: #E74C3C;
}

.status-text.testing {
  color: #FFA500;
}

/* 操作按钮 */
.action-section {
  display: flex;
  gap: 15rpx;
}

.retry-btn,
.back-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 18rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.retry-btn {
  background: linear-gradient(135deg, #228B22, #32CD32);
  color: white;
}

.retry-btn:disabled {
  background: #ccc;
  color: #999;
}

.back-btn {
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 22rpx;
}

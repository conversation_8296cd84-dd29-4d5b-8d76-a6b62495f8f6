// 图片替换测试页面
Page({
  data: {
    testResults: [],
    testing: false,
    imageTests: [
      // 轮播图测试
      {
        category: '轮播图',
        items: [
          { name: '传承千年中医智慧', path: '/images/轮播图/轮播图-传承千年中医智慧.jpg' },
          { name: '精选道地药材', path: '/images/轮播图/轮播图-精选道地药材.jpg' },
          { name: '弘扬养生文化', path: '/images/轮播图/轮播图-弘扬养生文化.jpg' }
        ]
      },
      // 中药材测试
      {
        category: '中药材',
        items: [
          { name: '人参', path: '/images/中药材/人参.jpg' },
          { name: '枸杞子', path: '/images/中药材/枸杞子.jpg' },
          { name: '当归', path: '/images/中药材/当归.jpg' },
          { name: '黄芪', path: '/images/中药材/黄芪.jpg' },
          { name: '甘草', path: '/images/中药材/甘草.jpg' },
          { name: '川芎', path: '/images/中药材/川芎.jpg' },
          { name: '白芍', path: '/images/中药材/白芍.jpg' },
          { name: '白术', path: '/images/中药材/白术.jpg' },
          { name: '党参', path: '/images/中药材/党参.jpg' },
          { name: '熟地黄', path: '/images/中药材/熟地黄.jpg' }
        ]
      },
      // 文创产品测试
      {
        category: '文创产品',
        items: [
          { name: '中医养生茶具套装', path: '/images/文创/中医养生茶具套装.jpg' },
          { name: '本草纲目典藏版', path: '/images/文创/本草纲目典藏版.jpg' },
          { name: '艾灸养生套装', path: '/images/文创/艾灸养生套装.jpg' },
          { name: '中药香薰炉', path: '/images/文创/中药香薰炉.jpg' },
          { name: '养生香囊', path: '/images/文创/养生香囊.jpg' },
          { name: '中医经络图', path: '/images/文创/中医经络图.jpg' },
          { name: '养生书籍', path: '/images/文创/养生书籍.jpg' },
          { name: '中药材标本', path: '/images/文创/中药材标本.jpg' }
        ]
      }
    ]
  },

  onLoad: function() {
    console.log('图片替换测试页面加载');
    this.runImageTests();
  },

  // 运行图片测试
  runImageTests: function() {
    this.setData({ testing: true });
    
    var testResults = [];
    var that = this;
    
    this.data.imageTests.forEach(function(category) {
      category.items.forEach(function(item) {
        testResults.push({
          category: category.category,
          name: item.name,
          path: item.path,
          status: 'testing',
          message: '检测中...'
        });
      });
    });

    this.setData({ testResults: testResults });

    // 模拟测试完成
    setTimeout(function() {
      var updatedResults = testResults.map(function(test) {
        return Object.assign({}, test, {
          status: 'unknown',
          message: '请查看图片显示效果'
        });
      });
      
      that.setData({ 
        testResults: updatedResults,
        testing: false
      });
    }, 1500);
  },

  // 图片加载成功
  onImageLoad: function(e) {
    var index = e.currentTarget.dataset.index;
    var testResults = this.data.testResults;
    if (testResults[index]) {
      testResults[index].status = 'success';
      testResults[index].message = '图片加载成功';
      this.setData({ testResults: testResults });
    }
  },

  // 图片加载失败
  onImageError: function(e) {
    var index = e.currentTarget.dataset.index;
    var testResults = this.data.testResults;
    if (testResults[index]) {
      testResults[index].status = 'error';
      testResults[index].message = '图片加载失败';
      this.setData({ testResults: testResults });
    }
  },

  // 重新测试
  retryTest: function() {
    this.runImageTests();
  },

  // 返回首页
  goHome: function() {
    wx.navigateBack();
  }
});

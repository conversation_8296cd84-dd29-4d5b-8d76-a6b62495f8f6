// 修复测试页面
Page({
  data: {
    testResults: []
  },

  onLoad: function() {
    console.log('修复测试页面加载');
    this.runTests();
  },

  // 运行测试
  runTests: function() {
    const tests = [
      {
        name: '中药材列表页面',
        url: '/pages/medicines/medicines',
        description: '测试中药材图片显示'
      },
      {
        name: '中药材详情页面',
        url: '/pages/medicine-detail/medicine-detail?id=1',
        description: '测试人参详情图片显示'
      },
      {
        name: '文创产品详情页面',
        url: '/pages/product-detail/product-detail?id=1',
        description: '测试文创产品图片显示'
      },
      {
        name: '图片调试页面',
        url: '/pages/image-debug/image-debug',
        description: '专门的图片路径测试'
      }
    ];

    this.setData({
      testResults: tests
    });
  },

  // 跳转测试页面
  goToTest: function(e) {
    const url = e.currentTarget.dataset.url;
    wx.navigateTo({
      url: url,
      fail: function(err) {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 返回首页
  goHome: function() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});

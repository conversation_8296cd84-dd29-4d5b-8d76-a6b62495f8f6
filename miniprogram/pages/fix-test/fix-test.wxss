/* 修复测试页面样式 */
.container {
  padding: 30rpx;
  background: linear-gradient(135deg, #f5f2e8, #faf8f3);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(139, 69, 19, 0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 24rpx;
  color: #666;
}

.test-list {
  margin-bottom: 40rpx;
}

.test-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx;
  margin-bottom: 20rpx;
  background: white;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 10rpx rgba(139, 69, 19, 0.08);
  border-left: 4rpx solid #8B4513;
}

.test-info {
  flex: 1;
  margin-right: 20rpx;
}

.test-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.test-description {
  display: block;
  font-size: 22rpx;
  color: #666;
}

.test-btn {
  background: #8B4513;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 15rpx 25rpx;
  font-size: 24rpx;
}

.test-btn:active {
  background: #A0522D;
}

.actions {
  text-align: center;
  margin-bottom: 40rpx;
}

.home-btn {
  background: #228B22;
  color: white;
  border: none;
  border-radius: 15rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
}

.fix-summary {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(139, 69, 19, 0.1);
}

.summary-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 20rpx;
  text-align: center;
}

.fix-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
  padding: 10rpx 0;
}

.fix-icon {
  font-size: 24rpx;
  margin-right: 15rpx;
  margin-top: 2rpx;
}

.fix-text {
  flex: 1;
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
}

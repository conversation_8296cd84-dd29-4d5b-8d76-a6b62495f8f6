// 中药图鉴页面
const app = getApp();

Page({
  data: {
    encyclopediaList: [],
    searchKeyword: '',
    currentCategory: '',
    categories: ['补气药', '补血药', '清热药', '解表药', '化痰药', '理气药'],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    
    // 筛选相关
    showFilterModal: false,
    allCategories: ['全部', '补气药', '补血药', '清热药', '解表药', '化痰药', '理气药', '活血药', '止血药'],
    effectTypes: ['全部', '补益', '清热', '解毒', '止咳', '化痰', '理气', '活血', '止血'],
    sortOptions: [
      { label: '默认排序', value: 'default' },
      { label: '按名称排序', value: 'name' },
      { label: '按分类排序', value: 'category' },
      { label: '按收藏排序', value: 'favorite' }
    ],
    filterCategory: '全部',
    filterEffect: '全部',
    sortType: 'default'
  },

  onLoad: function() {
    this.loadEncyclopediaData();
  },

  onShow: function() {
    // 刷新收藏状态
    this.refreshFavoriteStatus();
  },

  // 加载图鉴数据
  loadEncyclopediaData: function(reset) {
    if (reset === undefined) reset = false;
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 模拟数据，实际应该从数据库获取
      const mockData = [
        {
          id: 'enc_001',
          name: '人参',
          latinName: 'Panax ginseng',
          category: '补气药',
          effect: '补益',
          imageUrl: '/images/中药材/中药材-人参.jpg',
          description: '人参为五加科植物人参的干燥根和根茎。具有大补元气，复脉固脱，补脾益肺，生津止渴，安神益智的功效。',
          properties: '味甘、微苦，性微温',
          meridians: '归脾、肺、心、肾经',
          usage: '煎服，3-9g，另煎兑入；研粉吞服，每次1-2g',
          isFavorite: false
        },
        {
          id: 'enc_002',
          name: '枸杞子',
          latinName: 'Lycium barbarum',
          category: '补血药',
          effect: '补益',
          imageUrl: '/images/中药材/中药材-枸杞子.jpg',
          description: '枸杞子为茄科植物宁夏枸杞的干燥成熟果实。具有滋补肝肾，益精明目的功效。',
          properties: '味甘，性平',
          meridians: '归肝、肾经',
          usage: '煎服，6-12g',
          isFavorite: false
        },
        {
          id: 'enc_003',
          name: '当归',
          latinName: 'Angelica sinensis',
          category: '补血药',
          effect: '补益',
          imageUrl: '/images/中药材/中药材-当归.jpg',
          description: '当归为伞形科植物当归的干燥根。具有补血活血，调经止痛，润肠通便的功效。',
          properties: '味甘、辛，性温',
          meridians: '归肝、心、脾经',
          usage: '煎服，6-12g',
          isFavorite: false
        },
        {
          id: 'enc_004',
          name: '黄芪',
          latinName: 'Astragalus membranaceus',
          category: '补气药',
          effect: '补益',
          imageUrl: '/images/中药材/中药材-黄芪.jpg',
          description: '黄芪为豆科植物蒙古黄芪或膜荚黄芪的干燥根。具有补气升阳，固表止汗，利水消肿，生津养血，行滞通痹，托毒排脓，敛疮生肌的功效。',
          properties: '味甘，性微温',
          meridians: '归肺、脾经',
          usage: '煎服，9-30g',
          isFavorite: false
        },
        {
          id: 'enc_005',
          name: '甘草',
          latinName: 'Glycyrrhiza uralensis',
          category: '补气药',
          effect: '补益',
          imageUrl: '/images/中药材/中药材-甘草.jpg',
          description: '甘草为豆科植物甘草、胀果甘草或光果甘草的干燥根和根茎。具有补脾益气，清热解毒，祛痰止咳，缓急止痛，调和诸药的功效。',
          properties: '味甘，性平',
          meridians: '归心、肺、脾、胃经',
          usage: '煎服，2-10g',
          isFavorite: false
        }
      ];

      if (reset) {
        this.setData({
          encyclopediaList: mockData,
          page: 1,
          hasMore: mockData.length >= this.data.pageSize
        });
      } else {
        this.setData({
          encyclopediaList: [...this.data.encyclopediaList, ...mockData],
          page: this.data.page + 1,
          hasMore: mockData.length >= this.data.pageSize
        });
      }

    } catch (error) {
      console.error('加载图鉴数据失败:', error);
      app.showError('加载失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch: function() {
    console.log('搜索关键词:', this.data.searchKeyword);
    this.loadEncyclopediaData(true);
  },

  // 分类筛选
  onCategoryFilter: function(e) {
    var category = e.currentTarget.dataset.category;
    this.setData({
      currentCategory: category
    });
    this.loadEncyclopediaData(true);
  },

  // 跳转到详情页
  goToDetail: function(e) {
    var id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/encyclopedia/detail?id=' + id
    });
  },

  // 切换收藏状态
  toggleFavorite: function(e) {
    var id = e.currentTarget.dataset.id;
    var list = this.data.encyclopediaList;
    var index = -1;
    for (var i = 0; i < list.length; i++) {
      if (list[i].id === id) {
        index = i;
        break;
      }
    }

    if (index >= 0) {
      list[index].isFavorite = !list[index].isFavorite;
      this.setData({
        encyclopediaList: list
      });

      // 保存收藏状态到本地存储
      this.saveFavoriteStatus(id, list[index].isFavorite);

      wx.showToast({
        title: list[index].isFavorite ? '已收藏' : '已取消收藏',
        icon: 'success'
      });
    }
  },

  // 分享药材
  shareItem: function(e) {
    var item = e.currentTarget.dataset.item;
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    // 设置分享内容
    this.shareData = {
      title: item.name + ' - 中药图鉴',
      path: '/pages/encyclopedia/detail?id=' + item.id,
      imageUrl: item.imageUrl
    };
  },

  // 加载更多
  loadMore: function() {
    if (!this.data.hasMore || this.data.loading) return;
    this.loadEncyclopediaData();
  },

  // 显示筛选弹窗
  showFilterModal: function() {
    this.setData({
      showFilterModal: true
    });
  },

  // 隐藏筛选弹窗
  hideFilterModal: function() {
    this.setData({
      showFilterModal: false
    });
  },

  // 选择筛选分类
  selectFilterCategory: function(e) {
    this.setData({
      filterCategory: e.currentTarget.dataset.category
    });
  },

  // 选择功效筛选
  selectFilterEffect: function(e) {
    this.setData({
      filterEffect: e.currentTarget.dataset.effect
    });
  },

  // 选择排序方式
  selectSortType: function(e) {
    this.setData({
      sortType: e.currentTarget.dataset.sort
    });
  },

  // 重置筛选
  resetFilter: function() {
    this.setData({
      filterCategory: '全部',
      filterEffect: '全部',
      sortType: 'default'
    });
  },

  // 应用筛选
  applyFilter: function() {
    this.hideFilterModal();
    this.loadEncyclopediaData(true);
  },

  // 滚动到顶部
  scrollToTop: function() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  },

  // 保存收藏状态
  saveFavoriteStatus: function(id, isFavorite) {
    var userInfo = app.globalData.userInfo;
    if (!userInfo) return;

    var key = 'favorites_' + userInfo.id;
    var favorites = wx.getStorageSync(key) || [];

    if (isFavorite) {
      if (favorites.indexOf(id) === -1) {
        favorites.push(id);
      }
    } else {
      favorites = favorites.filter(function(fId) {
        return fId !== id;
      });
    }

    wx.setStorageSync(key, favorites);
  },

  // 刷新收藏状态
  refreshFavoriteStatus: function() {
    var userInfo = app.globalData.userInfo;
    if (!userInfo) return;

    var key = 'favorites_' + userInfo.id;
    var favorites = wx.getStorageSync(key) || [];

    var list = this.data.encyclopediaList.map(function(item) {
      return Object.assign({}, item, {
        isFavorite: favorites.indexOf(item.id) !== -1
      });
    });

    this.setData({
      encyclopediaList: list
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 空函数，用于阻止事件冒泡
  },

  // 分享给朋友
  onShareAppMessage: function() {
    return this.shareData || {
      title: '慧心智药 - 中药图鉴',
      path: '/pages/encyclopedia/encyclopedia'
    };
  },

  // 分享到朋友圈
  onShareTimeline: function() {
    return this.shareData || {
      title: '慧心智药 - 中药图鉴',
      query: 'from=timeline'
    };
  }
});

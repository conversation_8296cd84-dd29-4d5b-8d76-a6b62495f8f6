<!--中药图鉴页面-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <input class="search-input" placeholder="搜索中药材名称、功效..." value="{{searchKeyword}}" bindinput="onSearchInput" />
      <button class="search-btn" bindtap="onSearch">
        <text class="search-icon">🔍</text>
      </button>
    </view>
    <view class="search-filters">
      <scroll-view class="filter-scroll" scroll-x="true">
        <view class="filter-item {{currentCategory === '' ? 'active' : ''}}" bindtap="onCategoryFilter" data-category="">
          全部
        </view>
        <view class="filter-item {{currentCategory === item ? 'active' : ''}}" 
              wx:for="{{categories}}" wx:key="*this"
              bindtap="onCategoryFilter" data-category="{{item}}">
          {{item}}
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 图鉴列表 -->
  <view class="encyclopedia-list">
    <view class="encyclopedia-item" wx:for="{{encyclopediaList}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
      <view class="item-card">
        <image class="item-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
        <view class="item-overlay">
          <view class="item-info">
            <text class="item-name">{{item.name}}</text>
            <text class="item-latin">{{item.latinName}}</text>
            <text class="item-category">{{item.category}}</text>
          </view>
          <view class="item-actions">
            <view class="action-btn favorite {{item.isFavorite ? 'active' : ''}}" bindtap="toggleFavorite" data-id="{{item.id}}" catchtap="stopPropagation">
              <text class="action-icon">{{item.isFavorite ? '❤️' : '🤍'}}</text>
            </view>
            <view class="action-btn share" bindtap="shareItem" data-item="{{item}}" catchtap="stopPropagation">
              <text class="action-icon">📤</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <button class="load-btn" bindtap="loadMore" loading="{{loading}}">
      {{loading ? '加载中...' : '加载更多'}}
    </button>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{encyclopediaList.length === 0 && !loading}}">
    <image class="empty-image" src="/images/系统图标/empty-search.png" mode="aspectFit"></image>
    <text class="empty-text">暂无相关中药材</text>
    <text class="empty-desc">试试其他关键词或分类</text>
  </view>

  <!-- 浮动按钮 -->
  <view class="float-actions">
    <view class="float-btn filter-btn" bindtap="showFilterModal">
      <text class="float-icon">🔧</text>
    </view>
    <view class="float-btn top-btn" bindtap="scrollToTop">
      <text class="float-icon">⬆️</text>
    </view>
  </view>
</view>

<!-- 筛选弹窗 -->
<view class="modal-overlay" wx:if="{{showFilterModal}}" bindtap="hideFilterModal">
  <view class="modal-content filter-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">筛选条件</text>
      <text class="modal-close" bindtap="hideFilterModal">×</text>
    </view>
    <view class="modal-body">
      <view class="filter-group">
        <text class="filter-title">药材分类</text>
        <view class="filter-options">
          <view class="filter-option {{filterCategory === item ? 'active' : ''}}" 
                wx:for="{{allCategories}}" wx:key="*this"
                bindtap="selectFilterCategory" data-category="{{item}}">
            {{item}}
          </view>
        </view>
      </view>
      <view class="filter-group">
        <text class="filter-title">功效分类</text>
        <view class="filter-options">
          <view class="filter-option {{filterEffect === item ? 'active' : ''}}" 
                wx:for="{{effectTypes}}" wx:key="*this"
                bindtap="selectFilterEffect" data-effect="{{item}}">
            {{item}}
          </view>
        </view>
      </view>
      <view class="filter-group">
        <text class="filter-title">排序方式</text>
        <view class="filter-options">
          <view class="filter-option {{sortType === item.value ? 'active' : ''}}" 
                wx:for="{{sortOptions}}" wx:key="value"
                bindtap="selectSortType" data-sort="{{item.value}}">
            {{item.label}}
          </view>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="resetFilter">重置</button>
      <button class="btn btn-primary" bindtap="applyFilter">应用</button>
    </view>
  </view>
</view>

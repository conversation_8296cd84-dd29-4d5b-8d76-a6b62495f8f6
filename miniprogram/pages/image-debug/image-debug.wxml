<!--图片调试页面-->
<view class="container">
  <view class="header">
    <view class="back-btn" bindtap="goBack">← 返回</view>
    <view class="title">图片路径调试</view>
    <view class="retry-btn" bindtap="retryTest">重新测试</view>
  </view>

  <view class="stats">
    <text>总计: {{totalTests}} 张图片</text>
    <text>成功: {{successCount}}</text>
    <text>失败: {{errorCount}}</text>
  </view>

  <scroll-view class="test-list" scroll-y="true">
    <view class="test-item" wx:for="{{testResults}}" wx:key="id">
      <view class="test-header">
        <text class="test-name">{{item.name}}</text>
        <text class="test-status {{item.status}}">{{item.message}}</text>
      </view>
      
      <view class="test-path">
        <text>路径: {{item.path}}</text>
        <text>类型: {{item.type}}</text>
      </view>

      <view class="image-container">
        <image 
          class="test-image" 
          src="{{item.path}}" 
          mode="aspectFit"
          data-index="{{index}}"
          bindload="onImageLoad"
          binderror="onImageError"
          lazy-load="{{false}}"
        />
      </view>

      <view class="test-actions">
        <button 
          class="mini-btn" 
          size="mini" 
          data-index="{{index}}"
          bindtap="testSingleImage"
        >
          重测
        </button>
      </view>
    </view>
  </scroll-view>
</view>

// 图片调试页面
Page({
  data: {
    testResults: [],
    currentTest: 0,
    totalTests: 0,
    successCount: 0,
    errorCount: 0
  },

  onLoad: function() {
    console.log('图片调试页面加载');
    this.runImageTests();
  },

  // 运行图片测试
  runImageTests: function() {
    const testImages = [
      // 测试不同的路径格式
      { name: '人参-绝对路径', path: '/images/中药材/人参.jpg', type: 'absolute' },
      { name: '人参-相对路径', path: '../../images/中药材/人参.jpg', type: 'relative' },
      { name: '人参-无斜杠', path: 'images/中药材/人参.jpg', type: 'no-slash' },
      { name: '枸杞子-绝对路径', path: '/images/中药材/枸杞子.jpg', type: 'absolute' },
      { name: '当归-绝对路径', path: '/images/中药材/当归.jpg', type: 'absolute' },
      { name: '黄芪-绝对路径', path: '/images/中药材/黄芪.jpg', type: 'absolute' },
      // 测试文创产品
      { name: '茶具套装', path: '/images/文创/中医养生茶具套装.jpg', type: 'absolute' },
      { name: '本草纲目', path: '/images/文创/本草纲目典藏版.jpg', type: 'absolute' },
      // 测试轮播图
      { name: '轮播图1', path: '/images/轮播图/轮播图-传承千年中医智慧.jpg', type: 'absolute' },
      { name: '轮播图2', path: '/images/轮播图/轮播图-精选道地药材.jpg', type: 'absolute' }
    ];

    const testResults = testImages.map((img, index) => ({
      ...img,
      id: index,
      status: 'testing',
      message: '测试中...',
      loadTime: null
    }));

    this.setData({
      testResults: testResults,
      totalTests: testResults.length,
      currentTest: 0
    });

    console.log('开始图片测试，共', testResults.length, '张图片');
  },

  // 图片加载成功
  onImageLoad: function(e) {
    const index = e.currentTarget.dataset.index;
    const testResults = this.data.testResults;

    if (testResults[index]) {
      testResults[index].status = 'success';
      testResults[index].message = '✅ 加载成功';
      testResults[index].loadTime = Date.now();

      this.setData({ testResults: testResults });
      this.updateCounts();
      console.log('图片加载成功:', testResults[index].name, testResults[index].path);
    }
  },

  // 图片加载失败
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const testResults = this.data.testResults;

    if (testResults[index]) {
      testResults[index].status = 'error';
      testResults[index].message = '❌ 加载失败';
      testResults[index].loadTime = Date.now();

      this.setData({ testResults: testResults });
      this.updateCounts();
      console.error('图片加载失败:', testResults[index].name, testResults[index].path);
    }
  },

  // 重新测试
  retryTest: function() {
    this.runImageTests();
  },

  // 测试单个图片
  testSingleImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const testResults = this.data.testResults;
    
    if (testResults[index]) {
      testResults[index].status = 'testing';
      testResults[index].message = '重新测试中...';
      this.setData({ testResults: testResults });
    }
  },

  // 更新计数
  updateCounts: function() {
    const testResults = this.data.testResults;
    let successCount = 0;
    let errorCount = 0;

    testResults.forEach(function(item) {
      if (item.status === 'success') {
        successCount++;
      } else if (item.status === 'error') {
        errorCount++;
      }
    });

    this.setData({
      successCount: successCount,
      errorCount: errorCount
    });
  },

  // 返回
  goBack: function() {
    wx.navigateBack();
  }
});

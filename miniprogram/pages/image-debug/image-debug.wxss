/* 图片调试页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #ddd;
  margin-bottom: 20rpx;
}

.back-btn, .retry-btn {
  color: #8B4513;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  border: 2rpx solid #8B4513;
  border-radius: 10rpx;
  background-color: white;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.stats {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  background-color: white;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.stats text {
  font-size: 26rpx;
  color: #666;
}

.test-list {
  height: calc(100vh - 300rpx);
}

.test-item {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.test-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.test-status {
  font-size: 24rpx;
  padding: 5rpx 10rpx;
  border-radius: 5rpx;
}

.test-status.success {
  background-color: #d4edda;
  color: #155724;
}

.test-status.error {
  background-color: #f8d7da;
  color: #721c24;
}

.test-status.testing {
  background-color: #fff3cd;
  color: #856404;
}

.test-path {
  margin-bottom: 15rpx;
}

.test-path text {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.image-container {
  width: 100%;
  height: 200rpx;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 15rpx;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-image {
  width: 100%;
  height: 100%;
}

.test-actions {
  text-align: center;
}

.mini-btn {
  background-color: #8B4513;
  color: white;
  border: none;
  border-radius: 5rpx;
}

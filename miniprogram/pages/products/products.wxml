<!--products.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <input class="search-input" placeholder="搜索文创产品名称..." value="{{searchKeyword}}" bindinput="onSearchInput" />
      <button class="search-btn btn btn-primary" bindtap="searchProducts">搜索</button>
    </view>
    <view class="filter-bar">
      <scroll-view class="category-scroll" scroll-x="true">
        <view class="category-item {{selectedCategory === '' ? 'active' : ''}}" bindtap="selectCategory" data-category="">
          全部
        </view>
        <view class="category-item {{selectedCategory === item ? 'active' : ''}}" 
              wx:for="{{categories}}" wx:key="*this" 
              bindtap="selectCategory" data-category="{{item}}">
          {{item}}
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 管理员操作栏 -->
  <view class="admin-actions" wx:if="{{isAdmin}}">
    <button class="btn btn-primary" bindtap="addProduct">添加产品</button>
    <button class="btn btn-secondary" bindtap="batchManage">批量管理</button>
  </view>

  <!-- 产品网格 -->
  <view class="product-grid">
    <view class="product-item product-card" wx:for="{{productList}}" wx:key="id" bindtap="viewProduct" data-id="{{item.id}}" id="product-{{item.id}}">
      <view class="product-image-container">
        <image class="product-image" src="{{item.imageUrl}}" mode="aspectFill" wx:if="{{item.imageUrl && !item.imageError}}" binderror="onImageError" data-index="{{index}}"></image>
        <view class="product-placeholder" wx:if="{{!item.imageUrl || item.imageError}}">
          <text class="placeholder-icon">🎋</text>
        </view>
        <view class="product-badge" wx:if="{{item.isHot}}">热销</view>
        <view class="admin-overlay" wx:if="{{isAdmin}}">
          <button class="overlay-btn edit-btn" bindtap="editProduct" data-id="{{item.id}}" catchtap="stopPropagation">编辑</button>
          <button class="overlay-btn delete-btn" bindtap="deleteProduct" data-id="{{item.id}}" catchtap="stopPropagation">删除</button>
        </view>
      </view>
      <view class="product-info">
        <text class="product-name">{{item.name}}</text>
        <text class="product-category">{{item.category}}</text>
        <view class="product-meta">
          <text class="product-price">¥{{item.price}}</text>
          <text class="product-sales">销量{{item.sales}}</text>
        </view>
        <button class="cart-btn add-cart-btn" id="product-{{item.id}}" bindtap="addToCart" data-item="{{item}}" catchtap="stopPropagation" wx:if="{{!isAdmin}}">
          加入购物车
        </button>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <button class="btn btn-outline" bindtap="loadMore" disabled="{{loading}}">
      {{loading ? '加载中...' : '加载更多'}}
    </button>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{productList.length === 0 && !loading}}">
    <image class="empty-image" src="/images/empty-product.png" mode="aspectFit"></image>
    <text class="empty-text">暂无文创产品</text>
    <button class="btn btn-primary" bindtap="addProduct" wx:if="{{isAdmin}}">添加第一个产品</button>
  </view>

  <!-- 调试工具 -->
  <view class="debug-tools" wx:if="{{isAdmin}}">
    <button class="btn btn-outline debug-btn" bindtap="testImages">🔧 图片测试</button>
  </view>
</view>

<!-- 添加/编辑产品弹窗 -->
<view class="modal-overlay" wx:if="{{showEditModal}}" bindtap="hideEditModal">
  <view class="modal-content edit-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{editMode === 'add' ? '添加' : '编辑'}}文创产品</text>
      <text class="modal-close" bindtap="hideEditModal">×</text>
    </view>
    <scroll-view class="modal-body" scroll-y="true">
      <view class="form-group">
        <text class="form-label">产品名称</text>
        <input class="form-input" placeholder="请输入产品名称" value="{{editData.name}}" bindinput="onEditInput" data-field="name" />
      </view>
      
      <view class="form-group">
        <text class="form-label">产品分类</text>
        <picker class="form-picker" range="{{categories}}" value="{{editData.categoryIndex}}" bindchange="onCategoryChange">
          <view class="picker-text">{{editData.category || '请选择分类'}}</view>
        </picker>
      </view>
      
      <view class="form-group">
        <text class="form-label">产品描述</text>
        <textarea class="form-textarea" placeholder="请输入产品描述" value="{{editData.description}}" bindinput="onEditInput" data-field="description"></textarea>
      </view>
      
      <view class="form-row">
        <view class="form-group half">
          <text class="form-label">价格（元）</text>
          <input class="form-input" type="digit" placeholder="0.00" value="{{editData.price}}" bindinput="onEditInput" data-field="price" />
        </view>
        <view class="form-group half">
          <text class="form-label">库存</text>
          <input class="form-input" type="number" placeholder="0" value="{{editData.stock}}" bindinput="onEditInput" data-field="stock" />
        </view>
      </view>
      
      <view class="form-group">
        <label class="checkbox-label">
          <checkbox checked="{{editData.isHot}}" bindchange="onHotChange" />
          <text class="checkbox-text">设为热销产品</text>
        </label>
      </view>
      
      <view class="form-group">
        <text class="form-label">产品图片</text>
        <view class="image-upload">
          <image class="upload-preview" src="{{editData.imageUrl}}" mode="aspectFill" wx:if="{{editData.imageUrl}}"></image>
          <button class="upload-btn btn btn-outline" bindtap="uploadImage">
            {{editData.imageUrl ? '更换图片' : '上传图片'}}
          </button>
        </view>
      </view>
    </scroll-view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="hideEditModal">取消</button>
      <button class="btn btn-primary" bindtap="saveProduct" disabled="{{!canSave}}">保存</button>
    </view>
  </view>
</view>

<!-- 购物车动画 -->
<view class="cart-animation" wx:if="{{showCartAnimation}}" style="left: {{animationStartX}}px; top: {{animationStartY}}px;">
  <view class="cart-ball"></view>
</view>

<!-- 成功提示动画 -->
<view class="cart-success-animation" wx:if="{{showSuccessAnimation}}">
  <view class="success-icon"></view>
</view>

/**products.wxss**/

/* 搜索区域 */
.search-section {
  margin-bottom: 30rpx;
}

.search-bar {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  font-size: 28rpx;
  background-color: white;
}

.search-btn {
  padding: 20rpx 30rpx;
  font-size: 26rpx;
}

.filter-bar {
  background-color: white;
  border-radius: 15rpx;
  padding: 20rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-item {
  display: inline-block;
  padding: 15rpx 25rpx;
  margin-right: 15rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background-color: #F5F2E8;
  color: #666;
}

.category-item.active {
  background-color: #8B4513;
  color: white;
}

/* 管理员操作 */
.admin-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.admin-actions .btn {
  flex: 1;
  padding: 25rpx;
  font-size: 26rpx;
}

/* 产品网格 */
.product-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.product-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 246, 240, 0.95));
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(139, 69, 19, 0.1);
  border: 1rpx solid rgba(139, 69, 19, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.product-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #228B22, #32CD32, #8B4513);
  opacity: 0.6;
  z-index: 1;
}

.product-item:active {
  transform: translateY(-3rpx);
  box-shadow: 0 12rpx 40rpx rgba(139, 69, 19, 0.18);
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 280rpx;
  background: linear-gradient(135deg, #228B22, #32CD32);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #228B22, #32CD32);
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  font-size: 80rpx;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.product-image-container::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shine 4s infinite;
}

.product-badge {
  position: absolute;
  top: 15rpx;
  left: 15rpx;
  background-color: #E74C3C;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 15rpx;
  font-size: 20rpx;
}

/* 管理员覆盖层 */
.admin-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
  opacity: 0;
  transition: opacity 0.3s;
}

.product-item:hover .admin-overlay {
  opacity: 1;
}

.overlay-btn {
  padding: 15rpx 25rpx;
  border-radius: 10rpx;
  font-size: 22rpx;
  color: white;
  border: none;
}

.edit-btn {
  background-color: #3498DB;
}

.delete-btn {
  background-color: #E74C3C;
}

.product-info {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.product-name {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.3;
  letter-spacing: 0.3rpx;
}

.product-category {
  font-size: 18rpx;
  color: #228B22;
  background: rgba(34, 139, 34, 0.08);
  padding: 3rpx 10rpx;
  border-radius: 8rpx;
  display: inline-block;
  margin-bottom: 8rpx;
  border: 1rpx solid rgba(34, 139, 34, 0.15);
  font-weight: 500;
  letter-spacing: 0.2rpx;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.product-price {
  font-size: 26rpx;
  font-weight: bold;
  color: #E74C3C;
  letter-spacing: 0.3rpx;
}

.product-sales {
  font-size: 18rpx;
  color: #999;
  background: rgba(153, 153, 153, 0.08);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.cart-btn {
  width: 100%;
  background: linear-gradient(135deg, #228B22, #32CD32);
  color: white;
  padding: 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  border: none;
  margin-top: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(34, 139, 34, 0.25);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cart-btn::before {
  content: '🛒 ';
  font-size: 20rpx;
}

.cart-btn:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(34, 139, 34, 0.4);
  background: linear-gradient(135deg, #32CD32, #228B22);
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-bottom: 30rpx;
  grid-column: 1 / -1;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 50rpx;
  grid-column: 1 / -1;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 30rpx;
}

/* 编辑弹窗 */
.edit-modal {
  width: 90%;
  max-width: 700rpx;
  max-height: 80vh;
}

.modal-body {
  max-height: 60vh;
  padding: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-group.half {
  width: 48%;
}

.form-row {
  display: flex;
  justify-content: space-between;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  font-size: 26rpx;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #8B4513;
}

.form-picker {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  background-color: white;
}

.picker-text {
  font-size: 26rpx;
  color: #333;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  font-size: 26rpx;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #8B4513;
}

/* 复选框 */
.checkbox-label {
  display: flex;
  align-items: center;
}

.checkbox-text {
  font-size: 26rpx;
  color: #333;
  margin-left: 15rpx;
}

/* 图片上传 */
.image-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.upload-preview {
  width: 200rpx;
  height: 200rpx;
  border-radius: 15rpx;
}

.upload-btn {
  padding: 20rpx 40rpx;
  font-size: 24rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .product-grid {
    grid-template-columns: 1fr;
  }
  
  .search-bar {
    flex-direction: column;
  }
  
  .admin-actions {
    flex-direction: column;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .form-group.half {
    width: 100%;
  }
}

/* 购物车动画样式 */
.cart-animation {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
}

.cart-ball {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
  animation: cart-fly 1s ease-out forwards;
}

@keyframes cart-fly {
  0% {
    transform: scale(1) translate(0, 0);
    opacity: 1;
  }
  50% {
    transform: scale(0.8) translate(-200rpx, -300rpx);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.3) translate(-400rpx, -600rpx);
    opacity: 0;
  }
}

/* 成功提示动画 */
.cart-success-animation {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  pointer-events: none;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: success-bounce 0.6s ease-out forwards;
  position: relative;
}

.success-icon::after {
  content: '✓';
  color: white;
  font-size: 60rpx;
  font-weight: bold;
}

@keyframes success-bounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 美化购物车按钮 */
.add-cart-btn {
  background: linear-gradient(135deg, #228B22, #32CD32);
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 15rpx 30rpx;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(34, 139, 34, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 140rpx;
  text-align: center;
}

.add-cart-btn::before {
  content: '🛒 ';
  font-size: 20rpx;
}

.add-cart-btn:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(34, 139, 34, 0.4);
  background: linear-gradient(135deg, #32CD32, #228B22);
}

.add-cart-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.add-cart-btn:active::after {
  width: 200rpx;
  height: 200rpx;
}

/* 调试工具 */
.debug-tools {
  padding: 40rpx;
  text-align: center;
  border-top: 1px solid #E5E5E5;
  margin-top: 40rpx;
  background: #F8F5F0;
}

.debug-btn {
  font-size: 28rpx;
  padding: 16rpx 32rpx;
  border-radius: 20rpx;
  background: #FFF;
  border: 2rpx solid #228B22;
  color: #228B22;
}

.debug-btn:active {
  background: #228B22;
  color: #FFF;
}

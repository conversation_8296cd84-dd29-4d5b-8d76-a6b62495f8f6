// 文创产品页面
var app = getApp();

Page({
  data: {
    isAdmin: false,
    searchKeyword: '',
    selectedCategory: '',
    categories: ['茶具', '书籍', '养生用品', '香薰用品', '保健器具', '文房四宝', '装饰品', '其他'],
    productList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 编辑相关
    showEditModal: false,
    editMode: 'add',
    editData: {},
    canSave: false
  },

  onLoad: function(options) {
    console.log('文创产品页面加载中...');
    var that = this;
    
    // 初始化云开发
    if (wx.cloud) {
      wx.cloud.init({
        env: 'cloud1-8gj5z3ju8fc199f3',
        traceUser: true
      });
    }
    
    that.setData({
      isAdmin: app.globalData.isAdmin || false
    });
    
    // 立即显示默认数据
    that.setDefaultData();
    
    // 检查管理模式
    if (options && (options.mode === 'admin' || options.mode === 'add')) {
      if (!app.globalData.isAdmin) {
        wx.showModal({
          title: '权限不足',
          content: '您没有管理员权限',
          showCancel: false,
          success: function() {
            wx.navigateBack();
          }
        });
        return;
      }
      
      if (options.mode === 'add') {
        that.addProduct();
      }
    }
    
    // 异步加载云数据
    setTimeout(function() {
      that.tryLoadCloudData();
    }, 1000);
  },

  onShow: function() {
    this.setData({
      isAdmin: app.globalData.isAdmin || false
    });
  },

  // 设置默认数据
  setDefaultData: function() {
    console.log('设置默认文创产品数据...');
    var that = this;
    var defaultProducts = [
      // 茶具类
      {
        id: '1',
        name: '中医养生茶具套装',
        category: '茶具',
        description: '精美的紫砂茶具，包含茶壶、茶杯、茶盘等，适合泡制各种养生茶。',
        price: 168,
        originalPrice: 198,
        stock: 50,
        sales: 125,
        isHot: true,
        rating: 4.8,
        reviewCount: 89,
        imageUrl: '/images/文创/中医养生茶具套装.jpg',
        createTime: new Date()
      },
      {
        id: '2',
        name: '紫砂功夫茶具',
        category: '茶具',
        description: '宜兴紫砂制作，手工精制，适合品茗养生。',
        price: 288,
        originalPrice: 328,
        stock: 25,
        sales: 78,
        isHot: false,
        rating: 4.9,
        reviewCount: 56,
        imageUrl: '/images/文创/紫砂功夫茶具.jpg',
        createTime: new Date()
      },
      {
        id: '3',
        name: '青瓷茶杯套装',
        category: '茶具',
        description: '景德镇青瓷茶杯，釉色温润，品茶首选。',
        price: 98,
        originalPrice: 118,
        stock: 80,
        sales: 156,
        isHot: true,
        rating: 4.7,
        reviewCount: 123,
        imageUrl: '/images/文创/青瓷茶杯套装.jpg',
        createTime: new Date()
      },
      {
        id: '4',
        name: '竹制茶盘',
        category: '茶具',
        description: '天然竹制茶盘，环保健康，排水设计合理。',
        price: 128,
        originalPrice: 148,
        stock: 45,
        sales: 89,
        isHot: false,
        rating: 4.6,
        reviewCount: 67,
        imageUrl: '/images/文创/竹制茶盘.jpg',
        createTime: new Date()
      },

      // 书籍类
      {
        id: '5',
        name: '本草纲目典藏版',
        category: '书籍',
        description: '李时珍经典著作《本草纲目》典藏版，精装硬壳，内容详实。',
        price: 58,
        originalPrice: 68,
        stock: 100,
        sales: 234,
        isHot: false,
        rating: 4.9,
        reviewCount: 156,
        imageUrl: '/images/文创/本草纲目典藏版.jpg',
        createTime: new Date()
      },
      {
        id: '6',
        name: '黄帝内经注释版',
        category: '书籍',
        description: '中医经典《黄帝内经》现代注释版，通俗易懂。',
        price: 48,
        originalPrice: 58,
        stock: 120,
        sales: 189,
        isHot: true,
        rating: 4.8,
        reviewCount: 134,
        imageUrl: '/images/文创/黄帝内经注释版.jpg',
        createTime: new Date()
      },
      {
        id: '7',
        name: '伤寒论研读',
        category: '书籍',
        description: '张仲景《伤寒论》深度解析，中医学习必备。',
        price: 42,
        originalPrice: 52,
        stock: 85,
        sales: 67,
        isHot: false,
        rating: 4.7,
        reviewCount: 45,
        imageUrl: '/images/文创/伤寒论研读.jpg',
        createTime: new Date()
      },
      {
        id: '8',
        name: '中医养生大全',
        category: '书籍',
        description: '现代中医养生指南，图文并茂，实用性强。',
        price: 35,
        originalPrice: 45,
        stock: 150,
        sales: 298,
        isHot: true,
        rating: 4.6,
        reviewCount: 201,
        imageUrl: '/images/文创/中医养生大全.jpg',
        createTime: new Date()
      },

      // 养生用品类
      {
        id: '9',
        name: '艾灸养生套装',
        category: '养生用品',
        description: '传统艾灸工具套装，包含艾条、艾灸盒、艾灸罐等。',
        price: 128,
        originalPrice: 158,
        stock: 30,
        sales: 67,
        isHot: true,
        rating: 4.7,
        reviewCount: 45,
        imageUrl: '/images/文创/艾灸养生套装.jpg',
        createTime: new Date()
      },
      {
        id: '10',
        name: '刮痧板套装',
        category: '养生用品',
        description: '天然牛角刮痧板，多种规格，适合全身刮痧。',
        price: 68,
        originalPrice: 88,
        stock: 75,
        sales: 134,
        isHot: false,
        rating: 4.5,
        reviewCount: 89,
        imageUrl: '/images/文创/刮痧板套装.jpg',
        createTime: new Date()
      },
      {
        id: '11',
        name: '拔罐器套装',
        category: '养生用品',
        description: '真空拔罐器，安全便捷，居家养生必备。',
        price: 88,
        originalPrice: 108,
        stock: 55,
        sales: 98,
        isHot: false,
        rating: 4.4,
        reviewCount: 67,
        imageUrl: '/images/文创/拔罐器套装.jpg',
        createTime: new Date()
      },
      {
        id: '12',
        name: '足浴盆',
        category: '养生用品',
        description: '智能恒温足浴盆，按摩功能，促进血液循环。',
        price: 298,
        originalPrice: 358,
        stock: 20,
        sales: 45,
        isHot: true,
        rating: 4.8,
        reviewCount: 34,
        imageUrl: '/images/文创/足浴盆.jpg',
        createTime: new Date()
      },

      // 香薰用品类
      {
        id: '13',
        name: '中药香薰炉',
        category: '香薰用品',
        description: '精美陶瓷香薰炉，可用于熏香养生。',
        price: 88,
        originalPrice: 108,
        stock: 25,
        sales: 156,
        isHot: false,
        rating: 4.6,
        reviewCount: 98,
        imageUrl: '/images/文创/中药香薰炉.jpg',
        createTime: new Date()
      },
      {
        id: '14',
        name: '檀香线香套装',
        category: '香薰用品',
        description: '天然檀香制作，香味持久，安神助眠。',
        price: 58,
        originalPrice: 78,
        stock: 90,
        sales: 178,
        isHot: true,
        rating: 4.7,
        reviewCount: 123,
        imageUrl: '/images/文创/檀香线香套装.jpg',
        createTime: new Date()
      },
      {
        id: '15',
        name: '藏香熏香炉',
        category: '香薰用品',
        description: '藏式熏香炉，铜制工艺，适合熏藏香。',
        price: 158,
        originalPrice: 188,
        stock: 35,
        sales: 67,
        isHot: false,
        rating: 4.8,
        reviewCount: 45,
        imageUrl: '/images/文创/藏香熏香炉.jpg',
        createTime: new Date()
      },

      // 保健器具类
      {
        id: '16',
        name: '中医脉诊模型',
        category: '保健器具',
        description: '专业中医脉诊学习模型，适合中医爱好者和学生使用。',
        price: 298,
        originalPrice: 358,
        stock: 15,
        sales: 23,
        isHot: false,
        rating: 4.9,
        reviewCount: 12,
        imageUrl: '/images/文创/中医脉诊模型.jpg',
        createTime: new Date()
      },
      {
        id: '17',
        name: '针灸人体模型',
        category: '保健器具',
        description: '标准针灸穴位模型，学习针灸必备工具。',
        price: 388,
        originalPrice: 458,
        stock: 12,
        sales: 18,
        isHot: false,
        rating: 4.9,
        reviewCount: 8,
        imageUrl: '/images/文创/针灸人体模型.jpg',
        createTime: new Date()
      },
      {
        id: '18',
        name: '电子血压计',
        category: '保健器具',
        description: '家用电子血压计，精准测量，健康监测。',
        price: 168,
        originalPrice: 198,
        stock: 60,
        sales: 89,
        isHot: true,
        rating: 4.6,
        reviewCount: 67,
        imageUrl: '/images/文创/电子血压计.jpg',
        createTime: new Date()
      },
      {
        id: '19',
        name: '中医推拿按摩器',
        category: '保健器具',
        description: '电动推拿按摩器，多种模式，缓解疲劳。',
        price: 228,
        originalPrice: 268,
        stock: 40,
        sales: 56,
        isHot: false,
        rating: 4.5,
        reviewCount: 34,
        imageUrl: '/images/文创/中医推拿按摩器.jpg',
        createTime: new Date()
      },

      // 文房四宝类
      {
        id: '20',
        name: '文房四宝套装',
        category: '文房四宝',
        description: '传统文房四宝套装，包含毛笔、墨条、宣纸、砚台。',
        price: 218,
        originalPrice: 268,
        stock: 20,
        sales: 34,
        isHot: false,
        rating: 4.8,
        reviewCount: 28,
        imageUrl: '/images/文创/文房四宝套装.jpg',
        createTime: new Date()
      },
      {
        id: '21',
        name: '湖笔套装',
        category: '文房四宝',
        description: '湖州湖笔，笔锋细腻，书法绘画佳品。',
        price: 128,
        originalPrice: 158,
        stock: 45,
        sales: 67,
        isHot: false,
        rating: 4.7,
        reviewCount: 45,
        imageUrl: '/images/文创/湖笔套装.jpg',
        createTime: new Date()
      },
      {
        id: '22',
        name: '端砚砚台',
        category: '文房四宝',
        description: '广东端砚，石质细腻，研墨效果佳。',
        price: 388,
        originalPrice: 458,
        stock: 15,
        sales: 23,
        isHot: false,
        rating: 4.9,
        reviewCount: 18,
        imageUrl: '/images/文创/端砚砚台.jpg',
        createTime: new Date()
      },

      // 装饰品类
      {
        id: '23',
        name: '中医药材标本',
        category: '装饰品',
        description: '精美药材标本展示盒，教学装饰两用。',
        price: 158,
        originalPrice: 188,
        stock: 30,
        sales: 45,
        isHot: false,
        rating: 4.6,
        reviewCount: 34,
        imageUrl: '/images/文创/中药材标本.jpg',
        createTime: new Date()
      },
      {
        id: '24',
        name: '中医挂画套装',
        category: '装饰品',
        description: '中医文化主题挂画，装饰诊所或家居。',
        price: 88,
        originalPrice: 108,
        stock: 50,
        sales: 78,
        isHot: false,
        rating: 4.5,
        reviewCount: 56,
        imageUrl: '/images/文创/中医挂画套装.jpg',
        createTime: new Date()
      },
      {
        id: '25',
        name: '青花瓷药罐',
        category: '装饰品',
        description: '景德镇青花瓷药罐，实用美观，收纳佳品。',
        price: 68,
        originalPrice: 88,
        stock: 80,
        sales: 123,
        isHot: true,
        rating: 4.7,
        reviewCount: 89,
        imageUrl: '/images/文创/青花瓷药罐.jpg',
        createTime: new Date()
      }
    ];

    that.setData({
      productList: defaultProducts,
      hasMore: false,
      loading: false
    });

    console.log('默认文创产品数据设置完成，共', defaultProducts.length, '条');
  },

  // 尝试加载云数据库数据
  tryLoadCloudData: function() {
    var that = this;
    console.log('尝试加载云数据库数据...');
    
    if (!wx.cloud) {
      console.log('云开发未初始化，使用默认数据');
      return;
    }
    
    var db = wx.cloud.database();
    db.collection('products')
      .orderBy('createTime', 'desc')
      .limit(10)
      .get()
      .then(function(result) {
        console.log('云数据库查询结果:', result);
        if (result.data && result.data.length > 0) {
          console.log('成功加载云数据库数据:', result.data.length, '条');
          var processedData = result.data.map(function(item) {
            return {
              id: item._id || item.id,
              name: item.name || '未知产品',
              category: item.category || '其他',
              description: item.description || '描述待补充',
              price: item.price || 0,
              originalPrice: item.originalPrice || 0,
              stock: item.stock || 0,
              sales: item.sales || 0,
              imageUrl: item.imageUrl || '🎋',
              isHot: item.isHot || false,
              rating: item.rating || 4.5,
              reviewCount: item.reviewCount || 0,
              createTime: item.createTime || new Date()
            };
          });
          
          that.setData({
            productList: processedData,
            hasMore: result.data.length === 10
          });
        } else {
          console.log('云数据库为空，使用默认数据');
        }
      })
      .catch(function(error) {
        console.log('云数据库加载失败，使用默认数据:', error);
      });
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索产品
  searchProducts: function() {
    this.loadProducts(true);
  },

  // 选择分类
  selectCategory: function(e) {
    var category = e.currentTarget.dataset.category;
    this.setData({
      selectedCategory: category
    });
    this.loadProducts(true);
  },

  // 查看产品详情
  viewProduct: function(e) {
    var id = e.currentTarget.dataset.id;
    console.log('跳转到文创产品详情页面，ID:', id);
    wx.navigateTo({
      url: '/pages/product-detail/product-detail?id=' + id
    });
  },

  // 图片加载错误处理
  onImageError: function(e) {
    var index = e.currentTarget.dataset.index;
    var productList = this.data.productList;
    if (productList[index]) {
      productList[index].imageError = true;
      this.setData({
        productList: productList
      });
      console.log('文创产品图片加载失败，使用备用图标:', productList[index].name);
    }
  },

  // 添加到购物车
  addToCart: function(e) {
    var that = this;
    var item = e.currentTarget.dataset.item;

    if (!item) {
      wx.showToast({
        title: '商品数据错误',
        icon: 'none'
      });
      return;
    }

    // 确保有用户信息（创建游客账号）
    that.ensureUserInfo();

    // 获取用户信息
    var userInfo = app.globalData.userInfo;
    if (!userInfo) {
      wx.showToast({
        title: '用户信息错误',
        icon: 'none'
      });
      return;
    }

    // 检查库存
    if (item.stock <= 0) {
      wx.showToast({
        title: '产品库存不足',
        icon: 'none'
      });
      return;
    }

    // 创建购物车商品
    var cartItem = {
      id: 'cart_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      productId: item.id,
      name: item.name,
      price: item.price,
      imageUrl: item.imageUrl,
      type: 'product',
      category: item.category,
      quantity: 1,
      selected: true,
      spec: '1件',
      unit: '/件',
      totalPrice: item.price.toFixed(2)
    };

    // 获取当前购物车数据
    var cartKey = 'cart_' + userInfo.id;
    var cartData = wx.getStorageSync(cartKey) || [];

    // 检查是否已存在相同商品
    var existingIndex = -1;
    for (var i = 0; i < cartData.length; i++) {
      if (cartData[i].productId === item.id) {
        existingIndex = i;
        break;
      }
    }

    if (existingIndex >= 0) {
      // 如果已存在，增加数量
      cartData[existingIndex].quantity += 1;
      cartData[existingIndex].totalPrice = (cartData[existingIndex].price * cartData[existingIndex].quantity).toFixed(2);
    } else {
      // 如果不存在，添加新商品
      cartData.push(cartItem);
    }

    try {
      // 保存到本地存储
      wx.setStorageSync(cartKey, cartData);

      // 显示成功提示
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      });

      // 更新购物车徽章
      if (app.updateCartBadge) {
        app.updateCartBadge();
      }

    } catch (error) {
      console.error('保存购物车数据失败:', error);
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      });
    }
  },

  // 加载产品数据
  loadProducts: function(refresh) {
    // 简化版本，直接使用默认数据
    if (refresh === undefined) refresh = true;
    if (refresh) {
      this.setDefaultData();
    }
  },

  // 添加产品
  addProduct: function() {
    wx.showModal({
      title: '添加产品',
      content: '添加产品功能正在开发中...',
      showCancel: false
    });
  },

  // 确保用户信息存在（创建游客账号）
  ensureUserInfo: function() {
    if (!app.globalData.userInfo) {
      // 创建游客账号
      var guestUser = {
        id: 'guest_' + Date.now(),
        name: '游客用户',
        avatar: '👤',
        role: 'user',
        isGuest: true
      };

      app.setUserInfo(guestUser);
    }
  },

  // 图片测试
  testImages: function() {
    wx.navigateTo({
      url: '/pages/image-test/image-test'
    });
  }
});

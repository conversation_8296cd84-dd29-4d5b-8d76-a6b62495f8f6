// 文创产品页面
var app = getApp();

Page({
  data: {
    isAdmin: false,
    searchKeyword: '',
    selectedCategory: '',
    categories: ['茶具', '书籍', '养生用品', '香薰用品', '保健器具', '文房四宝', '装饰品', '其他'],
    productList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 编辑相关
    showEditModal: false,
    editMode: 'add',
    editData: {},
    canSave: false
  },

  onLoad: function(options) {
    console.log('文创产品页面加载中...');
    var that = this;
    
    // 初始化云开发
    if (wx.cloud) {
      wx.cloud.init({
        env: 'cloud1-8gj5z3ju8fc199f3',
        traceUser: true
      });
    }
    
    that.setData({
      isAdmin: app.globalData.isAdmin || false
    });
    
    // 立即显示默认数据
    that.setDefaultData();
    
    // 检查管理模式
    if (options && (options.mode === 'admin' || options.mode === 'add')) {
      if (!app.globalData.isAdmin) {
        wx.showModal({
          title: '权限不足',
          content: '您没有管理员权限',
          showCancel: false,
          success: function() {
            wx.navigateBack();
          }
        });
        return;
      }
      
      if (options.mode === 'add') {
        that.addProduct();
      }
    }
    
    // 异步加载云数据
    setTimeout(function() {
      that.tryLoadCloudData();
    }, 1000);
  },

  onShow: function() {
    this.setData({
      isAdmin: app.globalData.isAdmin || false
    });
  },

  // 设置默认数据
  setDefaultData: function() {
    console.log('设置默认文创产品数据...');
    var that = this;
    var defaultProducts = [
      // 茶具类
      {
        id: '1',
        name: '中医养生茶具套装',
        category: '茶具',
        description: '精美的紫砂茶具，包含茶壶、茶杯、茶盘等，适合泡制各种养生茶。传统工艺制作，质地细腻，保温效果佳，是品茗养生的理想选择。',
        price: 168,
        originalPrice: 198,
        stock: 50,
        sales: 125,
        isHot: true,
        rating: 4.8,
        reviewCount: 89,
        imageUrl: '/images/文创/中医养生茶具套装.jpg',
        createTime: new Date()
      },

      // 书籍类
      {
        id: '2',
        name: '本草纲目典藏版',
        category: '书籍',
        description: '李时珍经典著作《本草纲目》典藏版，精装硬壳，内容详实。收录1892种药物，11096个药方，是中医药学的百科全书，具有极高的学术价值和收藏价值。',
        price: 58,
        originalPrice: 68,
        stock: 100,
        sales: 234,
        isHot: true,
        rating: 4.9,
        reviewCount: 156,
        imageUrl: '/images/文创/本草纲目典藏版.jpg',
        createTime: new Date()
      },

      // 养生用品类
      {
        id: '3',
        name: '艾灸养生套装',
        category: '养生用品',
        description: '传统艾灸工具套装，包含艾条、艾灸盒、艾灸罐等。采用优质艾草制作，温经散寒，行气通络，是居家养生保健的理想选择。',
        price: 128,
        originalPrice: 158,
        stock: 30,
        sales: 67,
        isHot: true,
        rating: 4.7,
        reviewCount: 45,
        imageUrl: '/images/文创/艾灸养生套装.jpg',
        createTime: new Date()
      },

      // 香薰用品类
      {
        id: '4',
        name: '中药香薰炉',
        category: '香薰用品',
        description: '精美陶瓷香薰炉，可用于熏香养生。采用优质陶瓷制作，造型典雅，可搭配各种中药香料使用，营造宁静舒适的养生环境。',
        price: 88,
        originalPrice: 108,
        stock: 25,
        sales: 156,
        isHot: false,
        rating: 4.6,
        reviewCount: 98,
        imageUrl: '/images/文创/中药香薰炉.jpg',
        createTime: new Date()
      },

      // 养生香囊
      {
        id: '5',
        name: '养生香囊',
        category: '香薰用品',
        description: '传统中药香囊，内含多种天然香料。精选薰衣草、艾叶、丁香等天然香料，具有安神助眠、驱虫防蛀的功效，是随身携带的养生佳品。',
        price: 38,
        originalPrice: 48,
        stock: 120,
        sales: 289,
        isHot: true,
        rating: 4.5,
        reviewCount: 178,
        imageUrl: '/images/文创/养生香囊.jpg',
        createTime: new Date()
      },

      // 养生书籍
      {
        id: '6',
        name: '养生书籍',
        category: '书籍',
        description: '现代中医养生指南，图文并茂，实用性强。涵盖四季养生、饮食调理、运动保健等多个方面，是现代人健康生活的实用指南。',
        price: 35,
        originalPrice: 45,
        stock: 150,
        sales: 298,
        isHot: false,
        rating: 4.6,
        reviewCount: 201,
        imageUrl: '/images/文创/养生书籍.jpg',
        createTime: new Date()
      },

      // 中医经络图
      {
        id: '7',
        name: '中医经络图',
        category: '装饰品',
        description: '专业中医经络穴位图，学习装饰两用。高清印刷，标注详细，适合中医爱好者学习使用，也可作为诊所、书房的装饰画。',
        price: 68,
        originalPrice: 88,
        stock: 80,
        sales: 123,
        isHot: false,
        rating: 4.7,
        reviewCount: 89,
        imageUrl: '/images/文创/中医经络图.jpg',
        createTime: new Date()
      },

      // 中药材标本
      {
        id: '8',
        name: '中药材标本',
        category: '装饰品',
        description: '精美药材标本展示盒，教学装饰两用。收录常见中药材标本，制作精美，既可用于教学展示，也可作为装饰品收藏。',
        price: 158,
        originalPrice: 188,
        stock: 30,
        sales: 45,
        isHot: false,
        rating: 4.6,
        reviewCount: 34,
        imageUrl: '/images/文创/中药材标本.jpg',
        createTime: new Date()
      }
    ];
    ];

    that.setData({
      productList: defaultProducts,
      hasMore: false,
      loading: false
    });

    console.log('默认文创产品数据设置完成，共', defaultProducts.length, '条');
  },

  // 尝试加载云数据库数据
  tryLoadCloudData: function() {
    var that = this;
    console.log('尝试加载云数据库数据...');
    
    if (!wx.cloud) {
      console.log('云开发未初始化，使用默认数据');
      return;
    }
    
    var db = wx.cloud.database();
    db.collection('products')
      .orderBy('createTime', 'desc')
      .limit(10)
      .get()
      .then(function(result) {
        console.log('云数据库查询结果:', result);
        if (result.data && result.data.length > 0) {
          console.log('成功加载云数据库数据:', result.data.length, '条');
          var processedData = result.data.map(function(item) {
            return {
              id: item._id || item.id,
              name: item.name || '未知产品',
              category: item.category || '其他',
              description: item.description || '描述待补充',
              price: item.price || 0,
              originalPrice: item.originalPrice || 0,
              stock: item.stock || 0,
              sales: item.sales || 0,
              imageUrl: item.imageUrl || '🎋',
              isHot: item.isHot || false,
              rating: item.rating || 4.5,
              reviewCount: item.reviewCount || 0,
              createTime: item.createTime || new Date()
            };
          });
          
          that.setData({
            productList: processedData,
            hasMore: result.data.length === 10
          });
        } else {
          console.log('云数据库为空，使用默认数据');
        }
      })
      .catch(function(error) {
        console.log('云数据库加载失败，使用默认数据:', error);
      });
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索产品
  searchProducts: function() {
    this.loadProducts(true);
  },

  // 选择分类
  selectCategory: function(e) {
    var category = e.currentTarget.dataset.category;
    this.setData({
      selectedCategory: category
    });
    this.loadProducts(true);
  },

  // 查看产品详情
  viewProduct: function(e) {
    var id = e.currentTarget.dataset.id;
    console.log('跳转到文创产品详情页面，ID:', id);
    wx.navigateTo({
      url: '/pages/product-detail/product-detail?id=' + id
    });
  },

  // 图片加载错误处理
  onImageError: function(e) {
    var index = e.currentTarget.dataset.index;
    var productList = this.data.productList;
    if (productList[index]) {
      productList[index].imageError = true;
      this.setData({
        productList: productList
      });
      console.log('文创产品图片加载失败，使用备用图标:', productList[index].name);
    }
  },

  // 添加到购物车
  addToCart: function(e) {
    var that = this;
    var item = e.currentTarget.dataset.item;

    if (!item) {
      wx.showToast({
        title: '商品数据错误',
        icon: 'none'
      });
      return;
    }

    // 确保有用户信息（创建游客账号）
    that.ensureUserInfo();

    // 获取用户信息
    var userInfo = app.globalData.userInfo;
    if (!userInfo) {
      wx.showToast({
        title: '用户信息错误',
        icon: 'none'
      });
      return;
    }

    // 检查库存
    if (item.stock <= 0) {
      wx.showToast({
        title: '产品库存不足',
        icon: 'none'
      });
      return;
    }

    // 创建购物车商品
    var cartItem = {
      id: 'cart_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      productId: item.id,
      name: item.name,
      price: item.price,
      imageUrl: item.imageUrl,
      type: 'product',
      category: item.category,
      quantity: 1,
      selected: true,
      spec: '1件',
      unit: '/件',
      totalPrice: item.price.toFixed(2)
    };

    // 获取当前购物车数据
    var cartKey = 'cart_' + userInfo.id;
    var cartData = wx.getStorageSync(cartKey) || [];

    // 检查是否已存在相同商品
    var existingIndex = -1;
    for (var i = 0; i < cartData.length; i++) {
      if (cartData[i].productId === item.id) {
        existingIndex = i;
        break;
      }
    }

    if (existingIndex >= 0) {
      // 如果已存在，增加数量
      cartData[existingIndex].quantity += 1;
      cartData[existingIndex].totalPrice = (cartData[existingIndex].price * cartData[existingIndex].quantity).toFixed(2);
    } else {
      // 如果不存在，添加新商品
      cartData.push(cartItem);
    }

    try {
      // 保存到本地存储
      wx.setStorageSync(cartKey, cartData);

      // 显示成功提示
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      });

      // 更新购物车徽章
      if (app.updateCartBadge) {
        app.updateCartBadge();
      }

    } catch (error) {
      console.error('保存购物车数据失败:', error);
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      });
    }
  },

  // 加载产品数据
  loadProducts: function(refresh) {
    // 简化版本，直接使用默认数据
    if (refresh === undefined) refresh = true;
    if (refresh) {
      this.setDefaultData();
    }
  },

  // 添加产品
  addProduct: function() {
    wx.showModal({
      title: '添加产品',
      content: '添加产品功能正在开发中...',
      showCancel: false
    });
  },

  // 确保用户信息存在（创建游客账号）
  ensureUserInfo: function() {
    if (!app.globalData.userInfo) {
      // 创建游客账号
      var guestUser = {
        id: 'guest_' + Date.now(),
        name: '游客用户',
        avatar: '👤',
        role: 'user',
        isGuest: true
      };

      app.setUserInfo(guestUser);
    }
  },

  // 图片测试
  testImages: function() {
    wx.navigateTo({
      url: '/pages/image-test/image-test'
    });
  }
});

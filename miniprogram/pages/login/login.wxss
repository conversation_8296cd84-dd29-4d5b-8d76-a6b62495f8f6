/**login.wxss**/

.container {
  min-height: 100vh;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #F5F2E8 0%, #E8DCC0 100%);
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 10rpx;
}

.app-slogan {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 登录表单 */
.login-form {
  margin-bottom: 40rpx;
}

.form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  text-align: center;
  margin-bottom: 40rpx;
}

.input-group {
  position: relative;
  margin-bottom: 30rpx;
}

.input-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.input {
  width: 100%;
  padding: 25rpx 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  font-size: 28rpx;
  background-color: #FAFAFA;
  box-sizing: border-box;
}

.input:focus {
  border-color: #8B4513;
  background-color: white;
}

.password-toggle {
  position: absolute;
  right: 20rpx;
  top: 50rpx;
  padding: 10rpx;
}

.toggle-text {
  font-size: 24rpx;
  color: #8B4513;
}

/* 登录选项 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
}

.checkbox-text {
  font-size: 24rpx;
  color: #666;
  margin-left: 10rpx;
}

.forgot-password {
  font-size: 24rpx;
  color: #8B4513;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  padding: 30rpx;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.login-btn[disabled] {
  background-color: #CCC;
  color: #999;
}

/* 注册提示 */
.register-prompt {
  text-align: center;
}

.prompt-text {
  font-size: 24rpx;
  color: #666;
}

.register-link {
  font-size: 24rpx;
  color: #8B4513;
  margin-left: 10rpx;
}

/* 快速登录 */
.quick-login {
  margin-bottom: 40rpx;
}

.quick-title {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.quick-buttons {
  display: flex;
  gap: 20rpx;
}

.quick-buttons .btn {
  flex: 1;
  padding: 25rpx;
  font-size: 26rpx;
}

/* 管理员登录 */
.admin-login {
  text-align: center;
  margin-top: 40rpx;
}

.admin-text {
  font-size: 24rpx;
  color: #A0522D;
  text-decoration: underline;
  margin-right: 30rpx;
}

.debug-text {
  font-size: 20rpx;
  color: #666;
  text-decoration: underline;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 20rpx;
  width: 80%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.modal-body {
  padding: 30rpx;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 2rpx solid #F0F0F0;
}

.modal-footer .btn {
  flex: 1;
  padding: 25rpx;
  font-size: 28rpx;
}

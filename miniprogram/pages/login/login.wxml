<!--login.wxml-->
<view class="container">
  <view class="login-header">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="app-name">慧心制药</text>
    <text class="app-slogan">传承中医智慧，守护健康生活</text>
  </view>

  <view class="login-form card">
    <view class="form-title">用户登录</view>
    
    <view class="input-group">
      <text class="input-label">用户名</text>
      <input class="input" placeholder="请输入用户名" value="{{username}}" bindinput="onUsernameInput" />
    </view>
    
    <view class="input-group">
      <text class="input-label">密码</text>
      <input class="input" placeholder="请输入密码" password="{{!showPassword}}" value="{{password}}" bindinput="onPasswordInput" />
      <view class="password-toggle" bindtap="togglePassword">
        <text class="toggle-text">{{showPassword ? '隐藏' : '显示'}}</text>
      </view>
    </view>

    <view class="login-options">
      <label class="checkbox-item">
        <checkbox checked="{{rememberMe}}" bindchange="onRememberChange" />
        <text class="checkbox-text">记住密码</text>
      </label>
      <text class="forgot-password" bindtap="forgotPassword">忘记密码？</text>
    </view>

    <button class="btn btn-primary login-btn" bindtap="login" disabled="{{!canLogin}}">
      {{loginLoading ? '登录中...' : '登录'}}
    </button>

    <view class="register-prompt">
      <text class="prompt-text">还没有账号？</text>
      <text class="register-link" bindtap="goToRegister">立即注册</text>
    </view>
  </view>

  <view class="quick-login card">
    <view class="quick-title">快速登录</view>
    <view class="quick-buttons">
      <button class="btn btn-outline" open-type="getUserProfile" bindgetuserprofile="wxLogin">
        微信登录
      </button>
      <button class="btn btn-secondary" bindtap="guestLogin">
        游客模式
      </button>
    </view>
  </view>

  <view class="admin-login">
    <text class="admin-text" bindtap="showAdminLogin">管理员登录</text>
    <text class="debug-text" bindtap="checkDatabaseStatus">检查数据库</text>
    <text class="debug-text" bindtap="quickInit">快速初始化</text>
    <text class="debug-text" bindtap="goToDebugTool">系统诊断</text>
  </view>
</view>

<!-- 管理员登录弹窗 -->
<view class="modal-overlay" wx:if="{{showAdminModal}}" bindtap="hideAdminModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">管理员登录</text>
      <text class="modal-close" bindtap="hideAdminModal">×</text>
    </view>
    <view class="modal-body">
      <view class="input-group">
        <text class="input-label">管理员账号</text>
        <input class="input" placeholder="请输入管理员账号" value="{{adminUsername}}" bindinput="onAdminUsernameInput" />
      </view>
      <view class="input-group">
        <text class="input-label">管理员密码</text>
        <input class="input" placeholder="请输入管理员密码" password="true" value="{{adminPassword}}" bindinput="onAdminPasswordInput" />
      </view>
    </view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="hideAdminModal">取消</button>
      <button class="btn btn-primary" bindtap="adminLogin">登录</button>
    </view>
  </view>
</view>

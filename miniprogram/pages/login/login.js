// login.js
const app = getApp();
const { initDatabaseDirect } = require('../../utils/initDatabase');
const dbChecker = require('../../utils/database-checker');

Page({
  data: {
    username: '',
    password: '',
    showPassword: false,
    rememberMe: false,
    loginLoading: false,
    canLogin: false,
    
    // 管理员登录
    showAdminModal: false,
    adminUsername: '',
    adminPassword: ''
  },

  onLoad() {
    // 检查是否已登录
    if (app.globalData.userInfo) {
      wx.switchTab({
        url: '/pages/index/index'
      });
      return;
    }

    // 初始化数据库
    this.initDatabaseIfNeeded();

    // 加载记住的密码
    this.loadRememberedPassword();
  },

  // 初始化数据库（如果需要）
  async initDatabaseIfNeeded() {
    try {
      console.log('开始数据库初始化流程...');

      // 直接调用云函数检查数据库
      const checkResult = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'checkDatabase'
        }
      });

      console.log('数据库检查结果:', checkResult);

      if (checkResult.result && checkResult.result.success) {
        const data = checkResult.result.data;
        const needInit = !data.hasUsers || !data.hasAdmin;

        if (needInit) {
          console.log('数据库需要初始化');
          wx.showModal({
            title: '数据库初始化',
            content: '检测到数据库需要初始化\n\n将创建：\n• 管理员账号：admin / admin123\n• 测试用户：test / test123\n• 示例数据\n\n推荐使用直接初始化（更稳定）',
            confirmText: '直接初始化',
            cancelText: '云函数初始化',
            success: async (res) => {
              if (res.confirm) {
                // 直接初始化（推荐）
                await this.initDatabaseDirectly();
              } else {
                // 云函数初始化
                await this.initWithCloudFunction();
              }
            }
          });
        } else {
          console.log('数据库状态正常，无需初始化');
        }
      } else {
        console.error('数据库检查失败:', checkResult.result ? checkResult.result.message : '云函数调用失败');
        wx.showModal({
          title: '数据库检查失败',
          content: '无法检查数据库状态，建议使用系统诊断工具进行详细检查',
          confirmText: '系统诊断',
          cancelText: '稍后再试',
          success: (res) => {
            if (res.confirm) {
              this.goToDebugTool();
            }
          }
        });
      }
    } catch (error) {
      console.error('数据库检查失败:', error);
      wx.showModal({
        title: '检查失败',
        content: '数据库检查过程中发生错误，建议使用系统诊断工具',
        confirmText: '系统诊断',
        cancelText: '知道了',
        success: (res) => {
          if (res.confirm) {
            this.goToDebugTool();
          }
        }
      });
    }
  },

  // 直接初始化数据库
  async initDatabaseDirectly() {
    try {
      app.showLoading('正在初始化数据库...');

      const result = await initDatabaseDirect();

      app.hideLoading();

      if (result.success) {
        app.showSuccess('数据库初始化成功！');
        wx.showModal({
          title: '初始化完成',
          content: '数据库初始化成功！\n\n管理员账号：\n用户名：admin\n密码：admin123\n\n测试用户：\n用户名：test\n密码：test123\n\n现在可以正常登录了！',
          showCancel: false,
          success: () => {
            // 自动填入管理员账号
            this.setData({
              username: 'admin',
              password: 'admin123'
            });
          }
        });
      } else {
        app.showError('初始化失败: ' + result.message);
        console.error('直接初始化失败:', result);
      }
    } catch (error) {
      app.hideLoading();
      console.error('直接初始化异常:', error);
      app.showError('初始化异常: ' + error.message);
    }
  },

  // 云函数初始化数据库
  async initWithCloudFunction() {
    try {
      app.showLoading('使用云函数初始化...');

      const initResult = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'initTestUsers'
        }
      });

      app.hideLoading();

      if (initResult.result && initResult.result.success) {
        app.showSuccess('数据库初始化成功');
        wx.showModal({
          title: '初始化完成',
          content: '管理员账号已创建：\n用户名：admin\n密码：admin123\n\n测试用户：\n用户名：test\n密码：test123\n\n现在可以正常登录了！',
          showCancel: false
        });
      } else {
        app.showError('数据库初始化失败: ' + (initResult.result ? initResult.result.message : '未知错误'));
        // 如果云函数失败，提供直接初始化选项
        wx.showModal({
          title: '云函数初始化失败',
          content: '云函数初始化失败，是否尝试直接初始化？',
          success: async (res) => {
            if (res.confirm) {
              await this.initDatabaseDirectly();
            }
          }
        });
      }
    } catch (initError) {
      app.hideLoading();
      console.error('云函数初始化失败:', initError);
      app.showError('云函数初始化失败: ' + initError.message);

      // 提供直接初始化选项
      wx.showModal({
        title: '云函数初始化失败',
        content: '云函数初始化失败，是否尝试直接初始化？',
        success: async (res) => {
          if (res.confirm) {
            await this.initDatabaseDirectly();
          }
        }
      });
    }
  },

  // 加载记住的密码
  loadRememberedPassword() {
    const savedUsername = wx.getStorageSync('savedUsername');
    const savedPassword = wx.getStorageSync('savedPassword');
    
    if (savedUsername && savedPassword) {
      this.setData({
        username: savedUsername,
        password: savedPassword,
        rememberMe: true
      });
      this.checkCanLogin();
    }
  },

  // 用户名输入
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    });
    this.checkCanLogin();
  },

  // 密码输入
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
    this.checkCanLogin();
  },

  // 检查是否可以登录
  checkCanLogin() {
    const canLogin = this.data.username.trim() && this.data.password.trim();
    this.setData({ canLogin });
  },

  // 切换密码显示
  togglePassword() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 记住密码选择
  onRememberChange(e) {
    this.setData({
      rememberMe: e.detail.value.length > 0
    });
  },

  // 普通用户登录
  async login() {
    if (!this.data.canLogin || this.data.loginLoading) return;

    const { username, password, rememberMe } = this.data;

    this.setData({ loginLoading: true });
    app.showLoading('登录中...');

    try {
      // 调用简化版云函数进行登录验证
      const result = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'userLogin',
          username: username.trim(),
          password: password.trim()
        }
      });

      if (result.result.success) {
        const userInfo = result.result.data;
        
        // 保存用户信息
        app.setUserInfo(userInfo);
        
        // 记住密码
        if (rememberMe) {
          wx.setStorageSync('savedUsername', username);
          wx.setStorageSync('savedPassword', password);
        } else {
          wx.removeStorageSync('savedUsername');
          wx.removeStorageSync('savedPassword');
        }

        app.showSuccess('登录成功');
        
        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);
        
      } else {
        app.showError(result.result.message || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      app.showError('登录失败，请检查网络连接');
    }

    this.setData({ loginLoading: false });
    app.hideLoading();
  },

  // 微信登录
  async wxLogin(e) {
    if (!e.detail.userInfo) {
      app.showError('需要授权才能登录');
      return;
    }

    app.showLoading('微信登录中...');

    try {
      // 获取微信登录凭证
      const loginRes = await wx.login();
      
      // 调用云函数进行微信登录
      const result = await wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: 'wxLogin',
          code: loginRes.code,
          userInfo: e.detail.userInfo
        }
      });

      if (result.result.success) {
        const userInfo = result.result.data;
        app.setUserInfo(userInfo);
        app.showSuccess('微信登录成功');
        
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);
      } else {
        app.showError(result.result.message || '微信登录失败');
      }
    } catch (error) {
      console.error('微信登录失败:', error);
      app.showError('微信登录失败');
    }

    app.hideLoading();
  },

  // 游客登录
  guestLogin() {
    const guestInfo = {
      id: 'guest_' + Date.now(),
      nickName: '游客用户',
      avatarUrl: '/images/default-avatar.png',
      role: 'guest'
    };

    app.setUserInfo(guestInfo);
    app.showSuccess('游客登录成功');
    
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }, 1500);
  },

  // 显示管理员登录弹窗
  showAdminLogin() {
    this.setData({
      showAdminModal: true,
      adminUsername: '',
      adminPassword: ''
    });
  },

  // 隐藏管理员登录弹窗
  hideAdminModal() {
    this.setData({
      showAdminModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击模态框内容时关闭弹窗
  },

  // 管理员用户名输入
  onAdminUsernameInput(e) {
    this.setData({
      adminUsername: e.detail.value
    });
  },

  // 管理员密码输入
  onAdminPasswordInput(e) {
    this.setData({
      adminPassword: e.detail.value
    });
  },

  // 管理员登录
  async adminLogin() {
    const { adminUsername, adminPassword } = this.data;

    if (!adminUsername.trim() || !adminPassword.trim()) {
      app.showError('请输入管理员账号和密码');
      return;
    }

    app.showLoading('管理员登录中...');

    try {
      console.log('尝试管理员登录:', { username: adminUsername.trim(), password: '***' });

      // 先测试数据库连接
      const dbCheckResult = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'checkDatabase'
        }
      });

      if (dbCheckResult.result && dbCheckResult.result.success) {
        const data = dbCheckResult.result.data;
        if (!data.hasUsers || !data.hasAdmin) {
          app.hideLoading();
          wx.showModal({
            title: '数据库未初始化',
            content: '数据库尚未初始化，是否立即初始化？\n\n这将创建管理员账号和测试数据',
            success: async (res) => {
              if (res.confirm) {
                await this.initDatabaseIfNeeded();
              }
            }
          });
          return;
        }
      }

      const result = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'adminLogin',
          username: adminUsername.trim(),
          password: adminPassword.trim()
        }
      });

      console.log('管理员登录结果:', result);

      if (result.result && result.result.success) {
        const userInfo = result.result.data;
        app.setUserInfo(userInfo);
        app.showSuccess('管理员登录成功');

        this.hideAdminModal();

        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);
      } else {
        const errorMsg = result.result ? result.result.message : '登录失败';
        console.error('管理员登录失败:', errorMsg);

        // 显示详细错误信息
        wx.showModal({
          title: '登录失败',
          content: `错误信息：${errorMsg}\n\n请确认：\n1. 用户名：admin\n2. 密码：admin123\n3. 数据库已初始化`,
          showCancel: false
        });
      }
    } catch (error) {
      console.error('管理员登录异常:', error);

      // 显示详细错误信息
      wx.showModal({
        title: '登录异常',
        content: `网络或系统错误：${error.message || error}\n\n可能原因：\n1. 网络连接问题\n2. 云函数未部署\n3. 数据库权限问题`,
        showCancel: false
      });
    }

    app.hideLoading();
  },

  // 忘记密码
  forgotPassword() {
    wx.showModal({
      title: '忘记密码',
      content: '请联系管理员重置密码\n联系方式：<EMAIL>',
      showCancel: false
    });
  },

  // 跳转到注册页
  goToRegister() {
    wx.showModal({
      title: '注册功能',
      content: '注册功能正在开发中，请使用微信登录或游客模式',
      showCancel: false
    });
  },

  // 检查数据库状态（调试用）
  async checkDatabaseStatus() {
    app.showLoading('检查中...');

    try {
      console.log('开始检查数据库状态...');

      // 直接调用云函数检查数据库
      const result = await wx.cloud.callFunction({
        name: 'simpleFunction',
        data: {
          type: 'checkDatabase'
        }
      });

      console.log('数据库检查结果:', result);

      app.hideLoading();

      if (result.result && result.result.success) {
        const data = result.result.data;
        const isDbOk = data.hasUsers && data.hasAdmin;

        wx.showModal({
          title: '数据库状态',
          content: `数据库连接：正常\n用户数量：${data.totalUsers}\n管理员账号：${data.hasAdmin ? '存在' : '不存在'}\n\n${!isDbOk ? '建议：点击确定初始化数据库' : '数据库状态正常'}`,
          success: async (res) => {
            if (res.confirm && !isDbOk) {
              await this.initDatabaseIfNeeded();
            }
          }
        });
      } else {
        wx.showModal({
          title: '数据库检查失败',
          content: `错误信息：${result.result ? result.result.message : '云函数调用失败'}\n\n建议使用"系统诊断"工具进行详细检查`,
          confirmText: '系统诊断',
          cancelText: '稍后再试',
          success: (res) => {
            if (res.confirm) {
              this.goToDebugTool();
            }
          }
        });
      }

    } catch (error) {
      app.hideLoading();
      console.error('检查数据库状态失败:', error);

      // 提供更详细的错误信息
      let errorMessage = '检查失败';
      if (error.message) {
        if (error.message.includes('listCollections')) {
          errorMessage = '云函数版本不兼容，请重新部署 simpleFunction';
        } else if (error.message.includes('Permission')) {
          errorMessage = '数据库权限不足，请检查权限设置';
        } else if (error.message.includes('Network')) {
          errorMessage = '网络连接失败，请检查网络';
        } else {
          errorMessage = error.message;
        }
      }

      wx.showModal({
        title: '检查失败',
        content: `错误信息：${errorMessage}\n\n建议：\n1. 重新部署云函数\n2. 检查网络连接\n3. 使用系统诊断工具`,
        confirmText: '系统诊断',
        cancelText: '知道了',
        success: (res) => {
          if (res.confirm) {
            this.goToDebugTool();
          }
        }
      });
    }
  },

  // 快速初始化
  quickInit() {
    wx.showModal({
      title: '快速初始化数据库',
      content: '这将创建所有必要的数据：\n\n• 管理员账号：admin / admin123\n• 测试用户：test / test123\n• 示例中药材、产品、文章数据\n\n是否继续？',
      success: async (res) => {
        if (res.confirm) {
          await this.initDatabaseDirectly();
        }
      }
    });
  },

  // 跳转到系统诊断工具
  goToDebugTool() {
    wx.navigateTo({
      url: '/pages/debug/debug'
    });
  }
});

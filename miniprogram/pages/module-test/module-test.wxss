/* 首页模块跳转测试页面样式 */
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 8rpx;
}

.subtitle {
  font-size: 20rpx;
  color: #666;
}

/* 通用区块样式 */
.module-section,
.info-section,
.status-section,
.back-section {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-icon {
  font-size: 24rpx;
}

.title-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
}

/* 模块列表 */
.module-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.module-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 15rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.module-item:active {
  background: #f0f0f0;
  border-color: #8B4513;
  transform: translateY(-2rpx);
}

.module-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.icon-text {
  font-size: 40rpx;
  color: white;
}

.module-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.module-name {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.module-description {
  font-size: 20rpx;
  color: #666;
  line-height: 1.4;
}

.module-url {
  font-size: 16rpx;
  color: #999;
  font-family: monospace;
  background: rgba(139, 69, 19, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  align-self: flex-start;
}

.module-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(139, 69, 19, 0.1);
  border-radius: 50%;
}

.arrow-icon {
  font-size: 20rpx;
  color: #8B4513;
  font-weight: bold;
}

/* 功能说明 */
.info-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.info-icon {
  font-size: 20rpx;
  width: 30rpx;
  text-align: center;
}

.info-text {
  font-size: 20rpx;
  color: #666;
  flex: 1;
  line-height: 1.4;
}

/* 测试状态 */
.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 15rpx;
  border: 2rpx solid rgba(34, 139, 34, 0.2);
}

.status-icon {
  font-size: 32rpx;
  color: #228B22;
}

.status-text {
  font-size: 18rpx;
  color: #333;
  font-weight: 500;
}

/* 返回按钮 */
.back-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 18rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  border: none;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.3);
  transition: all 0.3s ease;
}

.back-btn:active {
  transform: translateY(-2rpx);
  opacity: 0.9;
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 22rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .module-item {
    flex-direction: column;
    text-align: center;
  }
  
  .module-info {
    align-items: center;
  }
  
  .module-url {
    align-self: center;
  }
}

/* 动画效果 */
.module-item {
  animation: slideInUp 0.3s ease-out;
}

.module-item:nth-child(1) { animation-delay: 0.1s; }
.module-item:nth-child(2) { animation-delay: 0.2s; }
.module-item:nth-child(3) { animation-delay: 0.3s; }
.module-item:nth-child(4) { animation-delay: 0.4s; }
.module-item:nth-child(5) { animation-delay: 0.5s; }
.module-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 首页模块跳转测试页面
Page({
  data: {
    modules: [
      {
        id: 'encyclopedia',
        name: '中药图鉴',
        icon: '📖',
        description: '浏览中药材详细信息',
        url: '/pages/encyclopedia/encyclopedia',
        color: '#8B4513'
      },
      {
        id: 'encyclopedia-detail',
        name: '中药图鉴详情',
        icon: '🌿',
        description: '查看人参的详细信息',
        url: '/pages/encyclopedia-detail/encyclopedia-detail?id=enc_001',
        color: '#228B22'
      },
      {
        id: 'store-map',
        name: '门店地图',
        icon: '🗺️',
        description: '查找附近的门店位置',
        url: '/pages/store-map/store-map',
        color: '#4169E1'
      },
      {
        id: 'article-detail',
        name: '养生文章详情',
        icon: '📚',
        description: '阅读春季养生文章',
        url: '/pages/article-detail/article-detail?id=article_001',
        color: '#32CD32'
      },
      {
        id: 'medicine-detail',
        name: '中药材详情',
        icon: '🌿',
        description: '查看人参商品详情',
        url: '/pages/medicine-detail/medicine-detail?id=1',
        color: '#8B4513'
      },
      {
        id: 'product-detail',
        name: '文创产品详情',
        icon: '🍵',
        description: '查看茶具套装详情',
        url: '/pages/product-detail/product-detail?id=1',
        color: '#228B22'
      }
    ]
  },

  onLoad: function() {
    console.log('首页模块跳转测试页面加载');
  },

  // 跳转到指定模块
  goToModule: function(e) {
    var module = e.currentTarget.dataset.module;
    console.log('跳转到模块:', module);
    
    wx.navigateTo({
      url: module.url,
      success: function() {
        console.log('跳转成功:', module.name);
      },
      fail: function(error) {
        console.error('跳转失败:', error);
        wx.showModal({
          title: '跳转失败',
          content: '页面可能不存在或路径错误',
          showCancel: false
        });
      }
    });
  },

  // 返回首页
  goHome: function() {
    wx.navigateBack();
  },

  // 分享功能
  onShareAppMessage: function() {
    return {
      title: '慧心制药 - 模块功能测试',
      path: '/pages/module-test/module-test',
      imageUrl: ''
    };
  }
});

<!--首页模块跳转测试页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">🏠 首页模块跳转测试</text>
    <text class="subtitle">测试首页各个模块的跳转功能</text>
  </view>

  <!-- 模块列表 -->
  <view class="module-section">
    <view class="section-title">
      <text class="title-icon">🧩</text>
      <text class="title-text">功能模块</text>
    </view>
    
    <view class="module-list">
      <view class="module-item" wx:for="{{modules}}" wx:key="id"
            bindtap="goToModule" data-module="{{item}}">
        <view class="module-icon" style="background: {{item.color}};">
          <text class="icon-text">{{item.icon}}</text>
        </view>
        
        <view class="module-info">
          <text class="module-name">{{item.name}}</text>
          <text class="module-description">{{item.description}}</text>
          <text class="module-url">{{item.url}}</text>
        </view>
        
        <view class="module-arrow">
          <text class="arrow-icon">→</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能说明 -->
  <view class="info-section">
    <view class="section-title">
      <text class="title-icon">ℹ️</text>
      <text class="title-text">功能说明</text>
    </view>
    
    <view class="info-content">
      <view class="info-item">
        <text class="info-icon">📖</text>
        <text class="info-text">中药图鉴：浏览和搜索中药材信息</text>
      </view>
      <view class="info-item">
        <text class="info-icon">🌿</text>
        <text class="info-text">图鉴详情：查看中药材的详细介绍</text>
      </view>
      <view class="info-item">
        <text class="info-icon">🗺️</text>
        <text class="info-text">门店地图：查找附近门店位置和信息</text>
      </view>
      <view class="info-item">
        <text class="info-icon">📚</text>
        <text class="info-text">养生文章：阅读专业的养生知识</text>
      </view>
      <view class="info-item">
        <text class="info-icon">🛒</text>
        <text class="info-text">商品详情：查看商品信息并购买</text>
      </view>
    </view>
  </view>

  <!-- 测试状态 -->
  <view class="status-section">
    <view class="section-title">
      <text class="title-icon">✅</text>
      <text class="title-text">测试状态</text>
    </view>
    
    <view class="status-grid">
      <view class="status-item">
        <text class="status-icon">✅</text>
        <text class="status-text">页面注册</text>
      </view>
      <view class="status-item">
        <text class="status-icon">✅</text>
        <text class="status-text">路由配置</text>
      </view>
      <view class="status-item">
        <text class="status-icon">✅</text>
        <text class="status-text">数据加载</text>
      </view>
      <view class="status-item">
        <text class="status-icon">✅</text>
        <text class="status-text">交互功能</text>
      </view>
    </view>
  </view>

  <!-- 返回按钮 -->
  <view class="back-section">
    <button class="back-btn" bindtap="goHome">
      <text class="btn-icon">🏠</text>
      <text class="btn-text">返回首页</text>
    </button>
  </view>
</view>

// 中药材页面
var app = getApp();

Page({
  data: {
    isAdmin: false,
    searchKeyword: '',
    selectedCategory: '',
    categories: ['补气药', '补血药', '清热药', '解表药', '化痰药', '活血药', '安神药', '消食药'],
    medicineList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 编辑相关
    showEditModal: false,
    editMode: 'add',
    editData: {},
    canSave: false
  },

  onLoad: function(options) {
    console.log('中药材页面加载中...');
    var that = this;
    
    // 初始化云开发
    if (wx.cloud) {
      wx.cloud.init({
        env: 'cloud1-8gj5z3ju8fc199f3',
        traceUser: true
      });
    }
    
    that.setData({
      isAdmin: app.globalData.isAdmin || false
    });
    
    // 立即显示默认数据
    that.setDefaultData();
    
    // 检查管理模式
    if (options && (options.mode === 'admin' || options.mode === 'add')) {
      if (!app.globalData.isAdmin) {
        wx.showModal({
          title: '权限不足',
          content: '您没有管理员权限',
          showCancel: false,
          success: function() {
            wx.navigateBack();
          }
        });
        return;
      }
      
      if (options.mode === 'add') {
        that.addMedicine();
      }
    }
    
    // 异步加载云数据
    setTimeout(function() {
      that.tryLoadCloudData();
    }, 1000);
  },

  onShow: function() {
    this.setData({
      isAdmin: app.globalData.isAdmin || false
    });
  },

  // 设置默认数据
  setDefaultData: function() {
    console.log('设置默认中药材数据...');
    var that = this;
    var defaultMedicines = [
      // 补气药类
      {
        id: '1',
        name: '人参',
        category: '补气药',
        effect: '大补元气，复脉固脱，补脾益肺，生津止渴，安神益智',
        description: '人参为五加科植物人参的干燥根和根茎。具有大补元气，复脉固脱，补脾益肺，生津止渴，安神益智的功效。主治气虚欲脱，肢冷脉微，脾虚食少，肺虚喘咳，津伤口渴，内热消渴，气血亏虚，久病虚羸，惊悸失眠，阳痿宫冷。',
        price: 12.5,
        stock: 500,
        imageUrl: '/images/中药材/人参.jpg',
        viewCount: 1250,
        isHot: true,
        isQuality: true,
        createTime: new Date()
      },
      {
        id: '2',
        name: '黄芪',
        category: '补气药',
        effect: '补气升阳，固表止汗，利水消肿，生津养血',
        description: '黄芪为豆科植物蒙古黄芪或膜荚黄芪的干燥根。具有补气升阳，固表止汗，利水消肿，生津养血的功效。主治脾肺气虚，食少倦怠，咳喘气短，自汗，水肿，血虚萎黄，半身不遂，痹痛麻木，痈疽难溃，久溃不敛。',
        price: 9.2,
        stock: 800,
        imageUrl: '/images/中药材/黄芪.jpg',
        viewCount: 654,
        isHot: true,
        isQuality: true,
        createTime: new Date()
      },
      {
        id: '3',
        name: '甘草',
        category: '补气药',
        effect: '补脾益气，清热解毒，祛痰止咳，缓急止痛，调和诸药',
        description: '甘草为豆科植物甘草、胀果甘草或光果甘草的干燥根和根茎。具有补脾益气，清热解毒，祛痰止咳，缓急止痛，调和诸药的功效。主治脾胃虚弱，倦怠乏力，心悸气短，咳嗽痰多，脘腹、四肢挛急疼痛，痈肿疮毒。',
        price: 6.5,
        stock: 1200,
        imageUrl: '/images/中药材/甘草.jpg',
        viewCount: 432,
        isHot: false,
        isQuality: true,
        createTime: new Date()
      },
      {
        id: '4',
        name: '白术',
        category: '补气药',
        effect: '健脾益气，燥湿利水，止汗，安胎',
        description: '白术为菊科植物大头白术的干燥根茎。具有健脾益气，燥湿利水，止汗，安胎的功效。主治脾虚食少，腹胀泄泻，痰饮眩悸，水肿，自汗，胎动不安。',
        price: 11.8,
        stock: 600,
        imageUrl: '/images/中药材/白术.jpg',
        viewCount: 567,
        isHot: false,
        isQuality: true,
        createTime: new Date()
      },
      {
        id: '5',
        name: '党参',
        category: '补气药',
        effect: '健脾益肺，养血生津',
        description: '党参为桔梗科植物党参、素花党参或川党参的干燥根。具有健脾益肺，养血生津的功效。主治脾肺气虚，食少倦怠，咳嗽虚喘，气血不足，面色萎黄，心悸气短，津伤口渴，内热消渴。',
        price: 8.9,
        stock: 450,
        imageUrl: '/images/中药材/党参.jpg',
        viewCount: 389,
        isHot: false,
        isQuality: true,
        createTime: new Date()
      },

      // 补血药类
      {
        id: '6',
        name: '当归',
        category: '补血药',
        effect: '补血活血，调经止痛，润肠通便',
        description: '当归为伞形科植物当归的干燥根。具有补血活血，调经止痛，润肠通便的功效。主治血虚萎黄，眩晕心悸，月经不调，经闭痛经，虚寒腹痛，风湿痹痛，跌扑损伤，痈疽疮疡，肠燥便秘。',
        price: 15.6,
        stock: 300,
        imageUrl: '/images/中药材/当归.jpg',
        viewCount: 756,
        isHot: true,
        isQuality: true,
        createTime: new Date()
      },
      {
        id: '7',
        name: '枸杞子',
        category: '补阴药',
        effect: '滋补肝肾，益精明目',
        description: '枸杞子为茄科植物宁夏枸杞的干燥成熟果实。具有滋补肝肾，益精明目的功效。主治肝肾阴亏，腰膝酸软，头晕，目眩，目昏多泪，虚劳咳嗽，消渴，遗精。',
        price: 8.8,
        stock: 1000,
        imageUrl: '/images/中药材/枸杞子.jpg',
        viewCount: 980,
        isHot: true,
        isQuality: true,
        createTime: new Date()
      },
      {
        id: '8',
        name: '熟地黄',
        category: '补血药',
        effect: '滋阴补血，益精填髓',
        description: '熟地黄为玄参科植物地黄的块根经加工炮制而成。具有滋阴补血，益精填髓的功效。主治血虚萎黄，心悸怔忡，月经不调，崩漏下血，肝肾阴亏，腰膝酸软，骨蒸潮热，盗汗遗精，内热消渴，眩晕，耳鸣，须发早白。',
        price: 13.2,
        stock: 280,
        imageUrl: '/images/中药材/熟地黄.jpg',
        viewCount: 445,
        isHot: false,
        isQuality: true,
        createTime: new Date()
      },
      {
        id: '9',
        name: '白芍',
        category: '补血药',
        effect: '养血调经，敛阴止汗，柔肝止痛，平抑肝阳',
        description: '白芍为毛茛科植物芍药的干燥根。具有养血调经，敛阴止汗，柔肝止痛，平抑肝阳的功效。主治血虚萎黄，月经不调，自汗，盗汗，胁痛，腹痛，四肢挛痛，头痛眩晕。',
        price: 10.5,
        stock: 520,
        imageUrl: '/images/中药材/白芍.jpg',
        viewCount: 367,
        isHot: false,
        isQuality: true,
        createTime: new Date()
      },
      {
        id: '10',
        name: '川芎',
        category: '活血化瘀药',
        effect: '活血行气，祛风止痛',
        description: '川芎为伞形科植物川芎的干燥根茎。具有活血行气，祛风止痛的功效。主治月经不调，经闭痛经，产后瘀滞腹痛，症瘕积聚，胸胁刺痛，跌扑肿痛，头痛，风湿痹痛。',
        price: 19.6,
        stock: 280,
        imageUrl: '/images/中药材/川芎.jpg',
        viewCount: 623,
        isHot: true,
        isQuality: true,
        createTime: new Date()
      }
    ];

    that.setData({
      medicineList: defaultMedicines,
      hasMore: false,
      loading: false
    });

    console.log('默认中药材数据设置完成，共', defaultMedicines.length, '条');
  },

  // 尝试加载云数据库数据
  tryLoadCloudData: function() {
    var that = this;
    console.log('尝试加载云数据库数据...');
    
    if (!wx.cloud) {
      console.log('云开发未初始化，使用默认数据');
      return;
    }
    
    var db = wx.cloud.database();
    db.collection('medicines')
      .orderBy('createTime', 'desc')
      .limit(10)
      .get()
      .then(function(result) {
        console.log('云数据库查询结果:', result);
        if (result.data && result.data.length > 0) {
          console.log('成功加载云数据库数据:', result.data.length, '条');
          var processedData = result.data.map(function(item) {
            // 处理旧的图片路径
            var imageUrl = item.imageUrl || '🌿';
            if (imageUrl.includes('/images/medicine') || imageUrl.includes('/images/empty-medicine')) {
              // 如果是旧的图片路径，根据药材名称设置新路径
              var medicineName = item.name || '';
              if (medicineName === '人参') {
                imageUrl = '/images/中药材/人参.jpg';
              } else if (medicineName === '黄芪') {
                imageUrl = '/images/中药材/黄芪.jpg';
              } else if (medicineName === '甘草') {
                imageUrl = '/images/中药材/甘草.jpg';
              } else if (medicineName === '白术') {
                imageUrl = '/images/中药材/白术.jpg';
              } else if (medicineName === '党参') {
                imageUrl = '/images/中药材/党参.jpg';
              } else if (medicineName === '当归') {
                imageUrl = '/images/中药材/当归.jpg';
              } else if (medicineName === '枸杞子') {
                imageUrl = '/images/中药材/枸杞子.jpg';
              } else if (medicineName === '熟地黄') {
                imageUrl = '/images/中药材/熟地黄.jpg';
              } else if (medicineName === '白芍') {
                imageUrl = '/images/中药材/白芍.jpg';
              } else if (medicineName === '川芎') {
                imageUrl = '/images/中药材/川芎.jpg';
              } else {
                imageUrl = '🌿'; // 使用emoji作为备用
              }
            }

            return {
              id: item._id || item.id,
              name: item.name || '未知药材',
              category: item.category || '其他',
              effect: item.effect || '功效待补充',
              description: item.description || '描述待补充',
              price: item.price || 0,
              stock: item.stock || 0,
              imageUrl: imageUrl,
              viewCount: item.viewCount || 0,
              isHot: item.isHot || false,
              isQuality: item.isQuality || false,
              createTime: item.createTime || new Date()
            };
          });
          
          that.setData({
            medicineList: processedData,
            hasMore: result.data.length === 10
          });
        } else {
          console.log('云数据库为空，使用默认数据');
        }
      })
      .catch(function(error) {
        console.log('云数据库加载失败，使用默认数据:', error);
      });
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索中药材
  searchMedicines: function() {
    this.loadMedicines(true);
  },

  // 选择分类
  selectCategory: function(e) {
    var category = e.currentTarget.dataset.category;
    this.setData({
      selectedCategory: category
    });
    this.loadMedicines(true);
  },

  // 查看中药材详情
  viewMedicine: function(e) {
    var id = e.currentTarget.dataset.id;
    console.log('跳转到中药材详情页面，ID:', id);
    wx.navigateTo({
      url: '/pages/medicine-detail/medicine-detail?id=' + id
    });
  },

  // 图片加载成功处理
  onImageLoad: function(e) {
    var id = e.currentTarget.dataset.id;
    console.log('中药材图片加载成功:', id);
  },

  // 图片加载错误处理
  onImageError: function(e) {
    var index = e.currentTarget.dataset.index;
    var medicineList = this.data.medicineList;
    if (medicineList[index]) {
      medicineList[index].imageError = true;
      // 尝试使用emoji作为备用显示
      if (!medicineList[index].imageUrl.includes('🌿')) {
        medicineList[index].fallbackIcon = '🌿';
      }
      this.setData({
        medicineList: medicineList
      });
      console.log('中药材图片加载失败，使用备用图标:', medicineList[index].name, '原路径:', medicineList[index].imageUrl);
    }
  },

  // 添加到购物车
  addToCart: function(e) {
    var that = this;
    var item = e.currentTarget.dataset.item;

    if (!item) {
      wx.showToast({
        title: '商品数据错误',
        icon: 'none'
      });
      return;
    }

    // 确保有用户信息（创建游客账号）
    that.ensureUserInfo();

    // 获取用户信息
    var userInfo = app.globalData.userInfo;
    if (!userInfo) {
      wx.showToast({
        title: '用户信息错误',
        icon: 'none'
      });
      return;
    }

    // 检查库存
    if (item.stock <= 0) {
      wx.showToast({
        title: '中药材库存不足',
        icon: 'none'
      });
      return;
    }

    // 创建购物车商品
    var cartItem = {
      id: 'cart_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      productId: item.id,
      name: item.name,
      price: item.price,
      imageUrl: item.imageUrl,
      type: 'medicine',
      category: item.category,
      quantity: 1,
      selected: true,
      spec: '1克',
      unit: '/克',
      totalPrice: item.price.toFixed(2)
    };

    // 获取当前购物车数据
    var cartKey = 'cart_' + userInfo.id;
    var cartData = wx.getStorageSync(cartKey) || [];

    // 检查是否已存在相同商品
    var existingIndex = -1;
    for (var i = 0; i < cartData.length; i++) {
      if (cartData[i].productId === item.id) {
        existingIndex = i;
        break;
      }
    }

    if (existingIndex >= 0) {
      // 如果已存在，增加数量
      cartData[existingIndex].quantity += 1;
      cartData[existingIndex].totalPrice = (cartData[existingIndex].price * cartData[existingIndex].quantity).toFixed(2);
    } else {
      // 如果不存在，添加新商品
      cartData.push(cartItem);
    }

    try {
      // 保存到本地存储
      wx.setStorageSync(cartKey, cartData);

      // 显示成功提示
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      });

      // 更新购物车徽章
      if (app.updateCartBadge) {
        app.updateCartBadge();
      }

    } catch (error) {
      console.error('保存购物车数据失败:', error);
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      });
    }
  },

  // 加载中药材数据
  loadMedicines: function(refresh) {
    // 简化版本，直接使用默认数据
    if (refresh === undefined) refresh = true;
    if (refresh) {
      this.setDefaultData();
    }
  },

  // 添加中药材
  addMedicine: function() {
    wx.showModal({
      title: '添加中药材',
      content: '添加中药材功能正在开发中...',
      showCancel: false
    });
  },

  // 确保用户信息存在（创建游客账号）
  ensureUserInfo: function() {
    if (!app.globalData.userInfo) {
      // 创建游客账号
      var guestUser = {
        id: 'guest_' + Date.now(),
        name: '游客用户',
        avatar: '👤',
        role: 'user',
        isGuest: true
      };

      app.setUserInfo(guestUser);
    }
  },

  // 图片测试
  testImages: function() {
    wx.navigateTo({
      url: '/pages/image-debug/image-debug'
    });
  }
});

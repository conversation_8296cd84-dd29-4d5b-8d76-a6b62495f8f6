<!--medicines.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <input class="search-input" placeholder="搜索中药材名称、功效..." value="{{searchKeyword}}" bindinput="onSearchInput" />
      <button class="search-btn btn btn-primary" bindtap="searchMedicines">搜索</button>
    </view>
    <view class="filter-bar">
      <scroll-view class="category-scroll" scroll-x="true">
        <view class="category-item {{selectedCategory === '' ? 'active' : ''}}" bindtap="selectCategory" data-category="">
          全部
        </view>
        <view class="category-item {{selectedCategory === item ? 'active' : ''}}" 
              wx:for="{{categories}}" wx:key="*this" 
              bindtap="selectCategory" data-category="{{item}}">
          {{item}}
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 管理员操作栏 -->
  <view class="admin-actions" wx:if="{{isAdmin}}">
    <button class="btn btn-primary" bindtap="addMedicine">添加中药材</button>
    <button class="btn btn-secondary" bindtap="batchManage">批量管理</button>
  </view>



  <!-- 中药材列表 -->
  <view class="medicine-list">
    <view class="medicine-item list-item medicine-card" wx:for="{{medicineList}}" wx:key="id" bindtap="viewMedicine" data-id="{{item.id}}" id="medicine-{{item.id}}">
      <view class="medicine-image-container">
        <image class="medicine-image" src="{{item.imageUrl}}" mode="aspectFill" wx:if="{{item.imageUrl && !item.imageError}}" binderror="onImageError" data-index="{{index}}" bindload="onImageLoad" data-id="{{item.id}}"></image>
        <view class="medicine-placeholder" wx:if="{{!item.imageUrl || item.imageError}}">
          <text class="placeholder-icon">🌿</text>
          <text class="placeholder-text">{{item.name}}</text>
        </view>
      </view>
      <view class="medicine-info">
        <text class="medicine-name">{{item.name}}</text>
        <text class="medicine-category">{{item.category}}</text>
        <text class="medicine-effect">功效：{{item.effect}}</text>
        <view class="medicine-meta">
          <text class="medicine-price">¥{{item.price}}/克</text>
          <text class="medicine-stock">库存：{{item.stock}}克</text>
        </view>
        <button class="cart-btn add-cart-btn" id="medicine-{{item.id}}" bindtap="addToCart" data-item="{{item}}" catchtap="stopPropagation" wx:if="{{!isAdmin}}">
          加入购物车
        </button>
      </view>
      <view class="medicine-actions" wx:if="{{isAdmin}}">
        <button class="action-btn edit-btn" bindtap="editMedicine" data-id="{{item.id}}" catchtap="stopPropagation">编辑</button>
        <button class="action-btn delete-btn" bindtap="deleteMedicine" data-id="{{item.id}}" catchtap="stopPropagation">删除</button>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <button class="btn btn-outline" bindtap="loadMore" disabled="{{loading}}">
      {{loading ? '加载中...' : '加载更多'}}
    </button>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{medicineList.length === 0 && !loading}}">
    <image class="empty-image" src="/images/empty-medicine.png" mode="aspectFit"></image>
    <text class="empty-text">暂无中药材数据</text>
    <button class="btn btn-primary" bindtap="addMedicine" wx:if="{{isAdmin}}">添加第一个中药材</button>
  </view>

  <!-- 调试工具 -->
  <view class="debug-tools" wx:if="{{isAdmin}}">
    <button class="btn btn-outline debug-btn" bindtap="testImages">🔧 图片测试</button>
  </view>
</view>

<!-- 添加/编辑中药材弹窗 -->
<view class="modal-overlay" wx:if="{{showEditModal}}" bindtap="hideEditModal">
  <view class="modal-content edit-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{editMode === 'add' ? '添加' : '编辑'}}中药材</text>
      <text class="modal-close" bindtap="hideEditModal">×</text>
    </view>
    <scroll-view class="modal-body" scroll-y="true">
      <view class="form-group">
        <text class="form-label">中药材名称</text>
        <input class="form-input" placeholder="请输入中药材名称" value="{{editData.name}}" bindinput="onEditInput" data-field="name" />
      </view>
      
      <view class="form-group">
        <text class="form-label">分类</text>
        <picker class="form-picker" range="{{categories}}" value="{{editData.categoryIndex}}" bindchange="onCategoryChange">
          <view class="picker-text">{{editData.category || '请选择分类'}}</view>
        </picker>
      </view>
      
      <view class="form-group">
        <text class="form-label">主要功效</text>
        <textarea class="form-textarea" placeholder="请输入主要功效" value="{{editData.effect}}" bindinput="onEditInput" data-field="effect"></textarea>
      </view>
      
      <view class="form-group">
        <text class="form-label">详细描述</text>
        <textarea class="form-textarea" placeholder="请输入详细描述" value="{{editData.description}}" bindinput="onEditInput" data-field="description"></textarea>
      </view>
      
      <view class="form-row">
        <view class="form-group half">
          <text class="form-label">价格（元/克）</text>
          <input class="form-input" type="digit" placeholder="0.00" value="{{editData.price}}" bindinput="onEditInput" data-field="price" />
        </view>
        <view class="form-group half">
          <text class="form-label">库存（克）</text>
          <input class="form-input" type="number" placeholder="0" value="{{editData.stock}}" bindinput="onEditInput" data-field="stock" />
        </view>
      </view>
      
      <view class="form-group">
        <text class="form-label">中药材图片</text>
        <view class="image-upload">
          <image class="upload-preview" src="{{editData.imageUrl}}" mode="aspectFill" wx:if="{{editData.imageUrl}}"></image>
          <button class="upload-btn btn btn-outline" bindtap="uploadImage">
            {{editData.imageUrl ? '更换图片' : '上传图片'}}
          </button>
        </view>
      </view>
    </scroll-view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="hideEditModal">取消</button>
      <button class="btn btn-primary" bindtap="saveMedicine" disabled="{{!canSave}}">保存</button>
    </view>
  </view>
</view>

<!-- 购物车动画 -->
<view class="cart-animation" wx:if="{{showCartAnimation}}" style="left: {{animationStartX}}px; top: {{animationStartY}}px;">
  <view class="cart-ball"></view>
</view>

<!-- 成功提示动画 -->
<view class="cart-success-animation" wx:if="{{showSuccessAnimation}}">
  <view class="success-icon"></view>
</view>

<!--数据测试页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">🧪 数据加载测试</text>
  </view>

  <!-- 数据统计 -->
  <view class="stats-section">
    <view class="section-title">数据统计</view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-icon">🌿</text>
        <text class="stat-number">{{medicineCount}}</text>
        <text class="stat-label">中药材</text>
      </view>
      <view class="stat-item">
        <text class="stat-icon">🎋</text>
        <text class="stat-number">{{productCount}}</text>
        <text class="stat-label">文创产品</text>
      </view>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="results-section">
    <view class="section-title">测试结果</view>
    <view class="result-list">
      <view class="result-item {{item.status}}" wx:for="{{testResults}}" wx:key="test">
        <view class="result-header">
          <text class="result-icon">{{item.status === 'success' ? '✅' : '❌'}}</text>
          <text class="result-test">{{item.test}}</text>
        </view>
        <text class="result-message">{{item.message}}</text>
        <text class="result-count" wx:if="{{item.count > 0}}">数量: {{item.count}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-buttons">
      <button class="action-btn primary" bindtap="goToMedicines">
        <text class="btn-icon">🌿</text>
        <text class="btn-text">查看中药材</text>
      </button>
      <button class="action-btn secondary" bindtap="goToProducts">
        <text class="btn-icon">🎋</text>
        <text class="btn-text">查看文创产品</text>
      </button>
    </view>
    <button class="action-btn refresh" bindtap="retestData">
      <text class="btn-icon">🔄</text>
      <text class="btn-text">重新测试</text>
    </button>
  </view>

  <!-- 说明 -->
  <view class="info-section">
    <view class="section-title">说明</view>
    <view class="info-content">
      <text class="info-text">此页面用于测试数据加载是否正常。</text>
      <text class="info-text">如果显示错误，请检查JavaScript语法。</text>
      <text class="info-text">如果数据正常，可以点击按钮跳转到对应页面。</text>
    </view>
  </view>
</view>

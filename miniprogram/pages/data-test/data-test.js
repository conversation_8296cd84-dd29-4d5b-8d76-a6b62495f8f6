// 数据测试页面
Page({
  data: {
    medicineCount: 0,
    productCount: 0,
    testResults: []
  },

  onLoad: function() {
    console.log('数据测试页面加载');
    this.testDataLoading();
  },

  // 测试数据加载
  testDataLoading: function() {
    var that = this;
    var results = [];

    // 测试中药材数据
    try {
      var medicineData = this.getMedicineTestData();
      results.push({
        test: '中药材数据加载',
        status: 'success',
        count: medicineData.length,
        message: '成功加载 ' + medicineData.length + ' 种中药材'
      });
      
      that.setData({
        medicineCount: medicineData.length
      });
    } catch (error) {
      results.push({
        test: '中药材数据加载',
        status: 'error',
        count: 0,
        message: '错误: ' + error.message
      });
    }

    // 测试文创产品数据
    try {
      var productData = this.getProductTestData();
      results.push({
        test: '文创产品数据加载',
        status: 'success',
        count: productData.length,
        message: '成功加载 ' + productData.length + ' 种文创产品'
      });
      
      that.setData({
        productCount: productData.length
      });
    } catch (error) {
      results.push({
        test: '文创产品数据加载',
        status: 'error',
        count: 0,
        message: '错误: ' + error.message
      });
    }

    that.setData({
      testResults: results
    });
  },

  // 获取中药材测试数据
  getMedicineTestData: function() {
    return [
      {
        id: '1',
        name: '人参',
        category: '补气药',
        price: 12.5,
        imageUrl: '🌿'
      },
      {
        id: '2',
        name: '黄芪',
        category: '补气药',
        price: 9.2,
        imageUrl: '🌾'
      },
      {
        id: '3',
        name: '当归',
        category: '补血药',
        price: 15.6,
        imageUrl: '🌱'
      }
    ];
  },

  // 获取文创产品测试数据
  getProductTestData: function() {
    return [
      {
        id: '1',
        name: '中医养生茶具套装',
        category: '茶具',
        price: 168,
        imageUrl: '🍵'
      },
      {
        id: '2',
        name: '本草纲目典藏版',
        category: '书籍',
        price: 58,
        imageUrl: '📚'
      },
      {
        id: '3',
        name: '艾灸养生套装',
        category: '养生用品',
        price: 128,
        imageUrl: '🔥'
      }
    ];
  },

  // 跳转到中药材页面
  goToMedicines: function() {
    wx.navigateTo({
      url: '/pages/medicines/medicines'
    });
  },

  // 跳转到文创产品页面
  goToProducts: function() {
    wx.navigateTo({
      url: '/pages/products/products'
    });
  },

  // 重新测试
  retestData: function() {
    this.testDataLoading();
    wx.showToast({
      title: '重新测试完成',
      icon: 'success'
    });
  }
});

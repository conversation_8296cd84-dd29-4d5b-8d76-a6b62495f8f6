/* 数据测试页面样式 */
.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
}

/* 通用区块样式 */
.stats-section,
.results-section,
.action-section,
.info-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 24rpx rgba(139, 69, 19, 0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #8B4513;
  padding-left: 15rpx;
}

/* 统计样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 25rpx;
  background: #f8f8f8;
  border-radius: 15rpx;
}

.stat-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #666;
}

/* 测试结果样式 */
.result-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.result-item {
  padding: 20rpx;
  border-radius: 15rpx;
  border-left: 4rpx solid #ccc;
}

.result-item.success {
  background: rgba(76, 175, 80, 0.1);
  border-left-color: #4CAF50;
}

.result-item.error {
  background: rgba(244, 67, 54, 0.1);
  border-left-color: #F44336;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 8rpx;
}

.result-icon {
  font-size: 20rpx;
}

.result-test {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
}

.result-message {
  font-size: 20rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.result-count {
  font-size: 18rpx;
  color: #8B4513;
  font-weight: bold;
}

/* 按钮样式 */
.action-buttons {
  display: flex;
  gap: 15rpx;
  margin-bottom: 15rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 15rpx 20rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  flex: 1;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
}

.action-btn.secondary {
  flex: 1;
  background: linear-gradient(135deg, #228B22, #32CD32);
  color: white;
}

.action-btn.refresh {
  width: 100%;
  background: linear-gradient(135deg, #4169E1, #6495ED);
  color: white;
}

.action-btn:active {
  transform: translateY(-2rpx);
  opacity: 0.9;
}

.btn-icon {
  font-size: 18rpx;
}

.btn-text {
  font-size: 20rpx;
}

/* 说明样式 */
.info-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.info-text {
  font-size: 20rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 20rpx;
  position: relative;
}

.info-text::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #8B4513;
  font-weight: bold;
}

// checkout.js
const app = getApp();

Page({
  data: {
    checkoutItems: [],
    selectedAddress: null,
    selectedDelivery: 'standard',
    selectedPayment: 'wechat',
    selectedCoupon: null,
    orderRemark: '',
    
    // 配送方式
    deliveryMethods: [
      {
        id: 'standard',
        name: '标准配送',
        description: '5-7个工作日送达',
        price: 0
      },
      {
        id: 'express',
        name: '快速配送',
        description: '2-3个工作日送达',
        price: 10
      },
      {
        id: 'same_day',
        name: '当日达',
        description: '当日18:00前送达',
        price: 20
      }
    ],
    
    // 支付方式
    paymentMethods: [
      {
        id: 'wechat',
        name: '微信支付',
        icon: '/images/wechat-pay.png'
      },
      {
        id: 'alipay',
        name: '支付宝',
        icon: '/images/alipay.png'
      }
    ],
    
    // 地址列表
    addressList: [],
    availableCoupons: [],
    
    // 费用计算
    goodsTotal: 0,
    deliveryFee: 0,
    couponDiscount: 0,
    totalAmount: 0,
    
    // 弹窗控制
    showAddressModal: false,
    showCouponModal: false,
    
    // 提交状态
    submitting: false,
    canSubmit: false
  },

  onLoad() {
    this.loadCheckoutData();
    this.loadAddressList();
    this.loadAvailableCoupons();
  },

  // 加载结算数据
  loadCheckoutData() {
    const checkoutItems = wx.getStorageSync('checkoutItems') || [];
    
    if (checkoutItems.length === 0) {
      wx.showModal({
        title: '提示',
        content: '没有要结算的商品',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    // 计算商品总价
    const goodsTotal = checkoutItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    this.setData({
      checkoutItems,
      goodsTotal: goodsTotal.toFixed(2)
    });

    this.calculateTotal();
  },

  // 加载地址列表
  async loadAddressList() {
    try {
      const userInfo = app.globalData.userInfo;
      if (!userInfo) return;

      // 从本地存储获取地址列表
      const addressList = wx.getStorageSync(`addresses_${userInfo.id}`) || [];
      
      // 如果没有地址，添加默认地址
      if (addressList.length === 0) {
        const defaultAddress = {
          id: 'default_1',
          name: '张三',
          phone: '138****8888',
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          detail: '三里屯街道某某小区1号楼101室',
          isDefault: true
        };
        addressList.push(defaultAddress);
        wx.setStorageSync(`addresses_${userInfo.id}`, addressList);
      }

      // 选择默认地址
      const defaultAddress = addressList.find(addr => addr.isDefault) || addressList[0];

      this.setData({
        addressList,
        selectedAddress: defaultAddress
      });

      this.checkCanSubmit();
    } catch (error) {
      console.error('加载地址列表失败:', error);
    }
  },

  // 加载可用优惠券
  async loadAvailableCoupons() {
    try {
      // 模拟优惠券数据
      const availableCoupons = [
        {
          id: 'coupon_1',
          name: '新用户专享券',
          amount: 10,
          minAmount: 50,
          expireDate: '2024-12-31'
        },
        {
          id: 'coupon_2',
          name: '满减优惠券',
          amount: 20,
          minAmount: 100,
          expireDate: '2024-12-31'
        },
        {
          id: 'coupon_3',
          name: '中药材专用券',
          amount: 15,
          minAmount: 80,
          expireDate: '2024-12-31'
        }
      ];

      // 筛选可用的优惠券
      const goodsTotal = parseFloat(this.data.goodsTotal);
      const usableCoupons = availableCoupons.filter(coupon => goodsTotal >= coupon.minAmount);

      this.setData({
        availableCoupons: usableCoupons
      });
    } catch (error) {
      console.error('加载优惠券失败:', error);
    }
  },

  // 选择收货地址
  selectAddress() {
    this.setData({
      showAddressModal: true
    });
  },

  // 隐藏地址选择弹窗
  hideAddressModal() {
    this.setData({
      showAddressModal: false
    });
  },

  // 选择地址项
  selectAddressItem(e) {
    const address = e.currentTarget.dataset.address;
    this.setData({
      selectedAddress: address,
      showAddressModal: false
    });
    this.checkCanSubmit();
  },

  // 编辑地址
  editAddress(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/address-edit/address-edit?id=${id}`
    });
  },

  // 添加地址
  addAddress() {
    wx.navigateTo({
      url: '/pages/address-edit/address-edit'
    });
  },

  // 配送方式改变
  onDeliveryChange(e) {
    const deliveryId = e.detail.value;
    this.setData({
      selectedDelivery: deliveryId
    });
    this.calculateTotal();
  },

  // 支付方式改变
  onPaymentChange(e) {
    const paymentId = e.detail.value;
    this.setData({
      selectedPayment: paymentId
    });
  },

  // 选择优惠券
  selectCoupon() {
    this.setData({
      showCouponModal: true
    });
  },

  // 隐藏优惠券选择弹窗
  hideCouponModal() {
    this.setData({
      showCouponModal: false
    });
  },

  // 选择优惠券项
  selectCouponItem(e) {
    const coupon = e.currentTarget.dataset.coupon;
    this.setData({
      selectedCoupon: coupon,
      showCouponModal: false
    });
    this.calculateTotal();
  },

  // 不使用优惠券
  selectNoCoupon() {
    this.setData({
      selectedCoupon: null,
      showCouponModal: false
    });
    this.calculateTotal();
  },

  // 订单备注输入
  onRemarkInput(e) {
    this.setData({
      orderRemark: e.detail.value
    });
  },

  // 计算总价
  calculateTotal() {
    const goodsTotal = parseFloat(this.data.goodsTotal);
    
    // 计算运费
    const selectedDeliveryMethod = this.data.deliveryMethods.find(method => method.id === this.data.selectedDelivery);
    const deliveryFee = selectedDeliveryMethod ? selectedDeliveryMethod.price : 0;
    
    // 计算优惠券折扣
    const couponDiscount = this.data.selectedCoupon ? this.data.selectedCoupon.amount : 0;
    
    // 计算总金额
    const totalAmount = Math.max(0, goodsTotal + deliveryFee - couponDiscount);

    this.setData({
      deliveryFee,
      couponDiscount,
      totalAmount: totalAmount.toFixed(2)
    });

    this.checkCanSubmit();
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const canSubmit = this.data.selectedAddress && this.data.checkoutItems.length > 0 && !this.data.submitting;
    this.setData({ canSubmit });
  },

  // 提交订单
  async submitOrder() {
    if (!this.data.canSubmit) return;

    // 验证必要信息
    if (!this.data.selectedAddress) {
      app.showError('请选择收货地址');
      return;
    }

    if (!app.globalData.userInfo) {
      app.showError('请先登录');
      return;
    }

    this.setData({ submitting: true });
    app.showLoading('提交订单中...');

    try {
      // 构建订单数据
      const orderData = {
        id: `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: app.globalData.userInfo.id,
        items: this.data.checkoutItems,
        address: this.data.selectedAddress,
        delivery: this.data.deliveryMethods.find(method => method.id === this.data.selectedDelivery),
        payment: this.data.paymentMethods.find(method => method.id === this.data.selectedPayment),
        coupon: this.data.selectedCoupon,
        remark: this.data.orderRemark,
        goodsTotal: this.data.goodsTotal,
        deliveryFee: this.data.deliveryFee,
        couponDiscount: this.data.couponDiscount,
        totalAmount: this.data.totalAmount,
        status: 'pending_payment',
        createTime: new Date(),
        updateTime: new Date()
      };

      // 调用云函数创建订单
      const result = await wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: 'createOrder',
          orderData: orderData
        }
      });

      if (result.result.success) {
        // 清除购物车中已结算的商品
        this.clearCartItems();
        
        // 清除结算数据
        wx.removeStorageSync('checkoutItems');
        
        app.hideLoading();
        app.showSuccess('订单提交成功');
        
        // 跳转到订单详情页
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/order-detail/order-detail?id=${orderData.id}`
          });
        }, 1500);
      } else {
        throw new Error(result.result.message || '订单提交失败');
      }
    } catch (error) {
      console.error('提交订单失败:', error);
      app.hideLoading();
      app.showError('订单提交失败，请重试');
    }

    this.setData({ submitting: false });
  },

  // 清除购物车中已结算的商品
  clearCartItems() {
    try {
      const userInfo = app.globalData.userInfo;
      if (!userInfo) return;

      const cartData = wx.getStorageSync(`cart_${userInfo.id}`) || [];
      const checkoutItemIds = this.data.checkoutItems.map(item => item.id);
      
      // 过滤掉已结算的商品
      const remainingItems = cartData.filter(item => !checkoutItemIds.includes(item.id));
      
      wx.setStorageSync(`cart_${userInfo.id}`, remainingItems);
    } catch (error) {
      console.error('清除购物车商品失败:', error);
    }
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击模态框内容时关闭弹窗
  }
});

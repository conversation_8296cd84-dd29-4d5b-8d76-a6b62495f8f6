/**checkout.wxss**/

/* 收货地址 */
.address-section {
  margin-bottom: 20rpx;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.address-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
}

.arrow-icon {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.5;
}

.address-content {
  padding-left: 10rpx;
}

.address-info {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.receiver-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.receiver-phone {
  font-size: 24rpx;
  color: #666;
}

.address-detail {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.no-address {
  padding: 20rpx 10rpx;
  text-align: center;
}

.no-address-text {
  font-size: 26rpx;
  color: #999;
}

/* 商品列表 */
.goods-section {
  margin-bottom: 20rpx;
}

.goods-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.goods-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
}

.goods-count {
  font-size: 24rpx;
  color: #666;
}

.goods-list {
  border-top: 2rpx solid #F0F0F0;
  padding-top: 20rpx;
}

.goods-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.goods-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.goods-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.goods-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-spec {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.goods-price-row {
  display: flex;
  align-items: baseline;
}

.goods-price {
  font-size: 24rpx;
  color: #E74C3C;
  font-weight: bold;
  margin-right: 8rpx;
}

.goods-unit {
  font-size: 20rpx;
  color: #999;
  margin-right: 15rpx;
}

.goods-quantity {
  font-size: 22rpx;
  color: #666;
}

.goods-total {
  font-size: 26rpx;
  color: #E74C3C;
  font-weight: bold;
}

/* 配送方式 */
.delivery-section {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
  display: block;
  margin-bottom: 20rpx;
}

.delivery-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.delivery-option {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #F8F8F8;
  border-radius: 10rpx;
}

.delivery-info {
  flex: 1;
  margin-left: 15rpx;
  display: flex;
  flex-direction: column;
}

.delivery-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.delivery-desc {
  font-size: 22rpx;
  color: #666;
}

.delivery-price {
  font-size: 24rpx;
  color: #A0522D;
  font-weight: bold;
}

/* 支付方式 */
.payment-section {
  margin-bottom: 20rpx;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 15rpx;
  background-color: #F8F8F8;
  border-radius: 10rpx;
}

.payment-info {
  flex: 1;
  margin-left: 15rpx;
  display: flex;
  align-items: center;
}

.payment-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.payment-name {
  font-size: 26rpx;
  color: #333;
}

/* 优惠券 */
.coupon-section {
  margin-bottom: 20rpx;
}

.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-right {
  display: flex;
  align-items: center;
}

.coupon-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

/* 订单备注 */
.remark-section {
  margin-bottom: 20rpx;
}

.remark-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  font-size: 26rpx;
  margin-top: 15rpx;
  box-sizing: border-box;
}

/* 费用明细 */
.cost-section {
  margin-bottom: 120rpx;
}

.cost-list {
  margin-top: 15rpx;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.cost-item:last-child {
  border-bottom: none;
}

.cost-item.total {
  padding-top: 20rpx;
  border-top: 2rpx solid #E0E0E0;
  margin-top: 10rpx;
}

.cost-label {
  font-size: 26rpx;
  color: #333;
}

.cost-value {
  font-size: 26rpx;
  color: #333;
}

.cost-value.discount {
  color: #E74C3C;
}

.cost-value.total-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #E74C3C;
}

/* 底部提交 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #F0F0F0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.submit-info {
  flex: 1;
}

.submit-total {
  font-size: 28rpx;
  font-weight: bold;
  color: #E74C3C;
}

.submit-btn {
  padding: 25rpx 50rpx;
  font-size: 28rpx;
  border-radius: 25rpx;
  margin: 0;
}

.submit-btn:disabled {
  background-color: #CCC;
  color: #999;
}

/* 地址选择弹窗 */
.address-modal {
  width: 90%;
  max-width: 700rpx;
  max-height: 70vh;
}

.address-list {
  margin-bottom: 30rpx;
}

.address-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  margin-bottom: 15rpx;
  background-color: #F8F8F8;
  border-radius: 10rpx;
  border: 2rpx solid transparent;
}

.address-item.active {
  border-color: #A0522D;
  background-color: #FFF8F5;
}

.address-item-info {
  flex: 1;
}

.address-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.address-item-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.address-item-phone {
  font-size: 24rpx;
  color: #666;
  margin-right: 15rpx;
}

.default-tag {
  background-color: #A0522D;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
}

.address-item-detail {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.address-item-actions {
  margin-left: 20rpx;
}

.edit-address-btn {
  background-color: transparent;
  color: #A0522D;
  border: 2rpx solid #A0522D;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
}

.add-address-btn {
  width: 100%;
  padding: 25rpx;
  font-size: 26rpx;
}

/* 优惠券选择弹窗 */
.coupon-modal {
  width: 90%;
  max-width: 700rpx;
  max-height: 70vh;
}

.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.coupon-item {
  display: flex;
  padding: 25rpx;
  background-color: #F8F8F8;
  border-radius: 10rpx;
  border: 2rpx solid transparent;
}

.coupon-item.active {
  border-color: #A0522D;
  background-color: #FFF8F5;
}

.coupon-left {
  width: 150rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 2rpx dashed #E0E0E0;
  margin-right: 20rpx;
}

.coupon-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #E74C3C;
  margin-bottom: 5rpx;
}

.coupon-condition {
  font-size: 20rpx;
  color: #999;
}

.coupon-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.coupon-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.coupon-expire {
  font-size: 22rpx;
  color: #999;
}

.no-coupon-item {
  padding: 25rpx;
  text-align: center;
  background-color: #F8F8F8;
  border-radius: 10rpx;
}

.no-coupon-text {
  font-size: 26rpx;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .goods-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .goods-image {
    margin-bottom: 15rpx;
  }
  
  .submit-section {
    flex-direction: column;
    padding: 15rpx 20rpx;
  }
  
  .submit-info {
    width: 100%;
    text-align: center;
    margin-bottom: 15rpx;
  }
  
  .submit-btn {
    width: 100%;
  }
}

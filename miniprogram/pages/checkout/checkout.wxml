<!--checkout.wxml-->
<view class="container">
  <!-- 收货地址 -->
  <view class="address-section card" bindtap="selectAddress">
    <view class="address-header">
      <text class="address-title">收货地址</text>
      <image class="arrow-icon" src="/images/arrow-right.png" mode="aspectFit"></image>
    </view>
    <view class="address-content" wx:if="{{selectedAddress}}">
      <view class="address-info">
        <text class="receiver-name">{{selectedAddress.name}}</text>
        <text class="receiver-phone">{{selectedAddress.phone}}</text>
      </view>
      <text class="address-detail">{{selectedAddress.province}} {{selectedAddress.city}} {{selectedAddress.district}} {{selectedAddress.detail}}</text>
    </view>
    <view class="no-address" wx:else>
      <text class="no-address-text">请选择收货地址</text>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="goods-section card">
    <view class="goods-header">
      <text class="goods-title">商品清单</text>
      <text class="goods-count">共{{checkoutItems.length}}件</text>
    </view>
    <view class="goods-list">
      <view class="goods-item" wx:for="{{checkoutItems}}" wx:key="id">
        <image class="goods-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
        <view class="goods-info">
          <text class="goods-name">{{item.name}}</text>
          <text class="goods-spec" wx:if="{{item.spec}}">规格：{{item.spec}}</text>
          <view class="goods-price-row">
            <text class="goods-price">¥{{item.price}}</text>
            <text class="goods-unit" wx:if="{{item.unit}}">{{item.unit}}</text>
            <text class="goods-quantity">×{{item.quantity}}</text>
          </view>
        </view>
        <text class="goods-total">¥{{item.totalPrice}}</text>
      </view>
    </view>
  </view>

  <!-- 配送方式 -->
  <view class="delivery-section card">
    <text class="section-title">配送方式</text>
    <view class="delivery-options">
      <label class="delivery-option" wx:for="{{deliveryMethods}}" wx:key="id">
        <radio value="{{item.id}}" checked="{{selectedDelivery === item.id}}" bindchange="onDeliveryChange" />
        <view class="delivery-info">
          <text class="delivery-name">{{item.name}}</text>
          <text class="delivery-desc">{{item.description}}</text>
        </view>
        <text class="delivery-price">{{item.price > 0 ? '¥' + item.price : '免费'}}</text>
      </label>
    </view>
  </view>

  <!-- 支付方式 -->
  <view class="payment-section card">
    <text class="section-title">支付方式</text>
    <view class="payment-options">
      <label class="payment-option" wx:for="{{paymentMethods}}" wx:key="id">
        <radio value="{{item.id}}" checked="{{selectedPayment === item.id}}" bindchange="onPaymentChange" />
        <view class="payment-info">
          <image class="payment-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <text class="payment-name">{{item.name}}</text>
        </view>
      </label>
    </view>
  </view>

  <!-- 优惠券 -->
  <view class="coupon-section card" bindtap="selectCoupon">
    <view class="coupon-header">
      <text class="section-title">优惠券</text>
      <view class="coupon-right">
        <text class="coupon-text" wx:if="{{selectedCoupon}}">-¥{{selectedCoupon.amount}}</text>
        <text class="coupon-text" wx:else>选择优惠券</text>
        <image class="arrow-icon" src="/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 订单备注 -->
  <view class="remark-section card">
    <text class="section-title">订单备注</text>
    <textarea class="remark-input" placeholder="选填，请输入您的要求（200字以内）" 
              value="{{orderRemark}}" bindinput="onRemarkInput" maxlength="200"></textarea>
  </view>

  <!-- 费用明细 -->
  <view class="cost-section card">
    <text class="section-title">费用明细</text>
    <view class="cost-list">
      <view class="cost-item">
        <text class="cost-label">商品总价</text>
        <text class="cost-value">¥{{goodsTotal}}</text>
      </view>
      <view class="cost-item">
        <text class="cost-label">运费</text>
        <text class="cost-value">{{deliveryFee > 0 ? '¥' + deliveryFee : '免费'}}</text>
      </view>
      <view class="cost-item" wx:if="{{couponDiscount > 0}}">
        <text class="cost-label">优惠券</text>
        <text class="cost-value discount">-¥{{couponDiscount}}</text>
      </view>
      <view class="cost-item total">
        <text class="cost-label">实付款</text>
        <text class="cost-value total-price">¥{{totalAmount}}</text>
      </view>
    </view>
  </view>

  <!-- 底部提交订单 -->
  <view class="submit-section">
    <view class="submit-info">
      <text class="submit-total">合计：¥{{totalAmount}}</text>
    </view>
    <button class="submit-btn btn btn-primary" bindtap="submitOrder" disabled="{{!canSubmit}}">
      {{submitting ? '提交中...' : '提交订单'}}
    </button>
  </view>
</view>

<!-- 地址选择弹窗 -->
<view class="modal-overlay" wx:if="{{showAddressModal}}" bindtap="hideAddressModal">
  <view class="modal-content address-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择收货地址</text>
      <text class="modal-close" bindtap="hideAddressModal">×</text>
    </view>
    <scroll-view class="modal-body" scroll-y="true">
      <view class="address-list">
        <view class="address-item {{selectedAddress && selectedAddress.id === item.id ? 'active' : ''}}" 
              wx:for="{{addressList}}" wx:key="id" bindtap="selectAddressItem" data-address="{{item}}">
          <view class="address-item-info">
            <view class="address-item-header">
              <text class="address-item-name">{{item.name}}</text>
              <text class="address-item-phone">{{item.phone}}</text>
              <text class="default-tag" wx:if="{{item.isDefault}}">默认</text>
            </view>
            <text class="address-item-detail">{{item.province}} {{item.city}} {{item.district}} {{item.detail}}</text>
          </view>
          <view class="address-item-actions">
            <button class="edit-address-btn" bindtap="editAddress" data-id="{{item.id}}" catchtap="stopPropagation">编辑</button>
          </view>
        </view>
      </view>
      <button class="add-address-btn btn btn-outline" bindtap="addAddress">添加新地址</button>
    </scroll-view>
  </view>
</view>

<!-- 优惠券选择弹窗 -->
<view class="modal-overlay" wx:if="{{showCouponModal}}" bindtap="hideCouponModal">
  <view class="modal-content coupon-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择优惠券</text>
      <text class="modal-close" bindtap="hideCouponModal">×</text>
    </view>
    <scroll-view class="modal-body" scroll-y="true">
      <view class="coupon-list">
        <view class="coupon-item {{selectedCoupon && selectedCoupon.id === item.id ? 'active' : ''}}" 
              wx:for="{{availableCoupons}}" wx:key="id" bindtap="selectCouponItem" data-coupon="{{item}}">
          <view class="coupon-left">
            <text class="coupon-amount">¥{{item.amount}}</text>
            <text class="coupon-condition">满{{item.minAmount}}可用</text>
          </view>
          <view class="coupon-right">
            <text class="coupon-name">{{item.name}}</text>
            <text class="coupon-expire">有效期至{{item.expireDate}}</text>
          </view>
        </view>
        <view class="no-coupon-item" bindtap="selectNoCoupon">
          <text class="no-coupon-text">不使用优惠券</text>
        </view>
      </view>
    </scroll-view>
  </view>
</view>

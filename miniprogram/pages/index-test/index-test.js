// 首页测试页面
Page({
  data: {
    message: '首页测试页面加载成功！',
    testResults: []
  },

  onLoad: function() {
    console.log('首页测试页面加载');
    this.runTests();
  },

  runTests: function() {
    var tests = [
      { name: '页面加载', status: 'success', message: '页面正常加载' },
      { name: 'JS语法检查', status: 'success', message: '无语法错误' },
      { name: '数据绑定', status: 'success', message: '数据正常绑定' },
      { name: '方法调用', status: 'success', message: '方法可正常调用' }
    ];

    this.setData({
      testResults: tests
    });
  },

  // 测试跳转功能
  testNavigation: function() {
    wx.showToast({
      title: '跳转功能正常',
      icon: 'success'
    });
  },

  // 返回首页
  goToIndex: function() {
    wx.redirectTo({
      url: '/pages/index/index'
    });
  }
});

<!--首页测试页面-->
<view class="container">
  <view class="header">
    <text class="title">🔧 首页测试</text>
    <text class="message">{{message}}</text>
  </view>

  <view class="test-section">
    <view class="section-title">测试结果</view>
    <view class="test-list">
      <view class="test-item" wx:for="{{testResults}}" wx:key="name">
        <text class="test-name">{{item.name}}</text>
        <text class="test-status {{item.status}}">{{item.status === 'success' ? '✅' : '❌'}}</text>
        <text class="test-message">{{item.message}}</text>
      </view>
    </view>
  </view>

  <view class="action-section">
    <button class="test-btn" bindtap="testNavigation">测试跳转</button>
    <button class="back-btn" bindtap="goToIndex">返回首页</button>
  </view>
</view>

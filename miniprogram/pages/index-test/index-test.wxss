/* 首页测试页面样式 */
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: white;
  border-radius: 20rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 10rpx;
}

.message {
  font-size: 20rpx;
  color: #666;
}

.test-section {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 20rpx;
}

.test-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.test-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.test-name {
  font-size: 20rpx;
  font-weight: bold;
  color: #333;
  min-width: 120rpx;
}

.test-status {
  font-size: 24rpx;
}

.test-status.success {
  color: #228B22;
}

.test-status.error {
  color: #E74C3C;
}

.test-message {
  font-size: 18rpx;
  color: #666;
  flex: 1;
}

.action-section {
  display: flex;
  gap: 15rpx;
}

.test-btn,
.back-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 22rpx;
  font-weight: bold;
  border: none;
}

.test-btn {
  background: linear-gradient(135deg, #228B22, #32CD32);
  color: white;
}

.back-btn {
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
}

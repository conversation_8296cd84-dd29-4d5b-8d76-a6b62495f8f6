// admin.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    loginTime: '',
    stats: {
      medicineCount: 0,
      productCount: 0,
      articleCount: 0,
      userCount: 0,
      medicineChange: 0,
      productChange: 0,
      articleChange: 0,
      userChange: 0
    }
  },

  onLoad() {
    // 检查管理员权限
    if (!app.globalData.isAdmin) {
      wx.showModal({
        title: '权限不足',
        content: '您没有管理员权限，无法访问此页面',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      });
      return;
    }

    this.loadAdminData();
  },

  onShow() {
    if (app.globalData.isAdmin) {
      this.loadAdminData();
    }
  },

  // 加载管理员数据
  loadAdminData() {
    this.setData({
      userInfo: app.globalData.userInfo,
      loginTime: this.formatTime(new Date())
    });
    
    this.loadStatsData();
  },

  // 加载统计数据
  async loadStatsData() {
    try {
      app.showLoading('加载数据中...');
      
      const db = wx.cloud.database();
      
      // 获取当前数据
      const [medicineRes, productRes, articleRes, userRes] = await Promise.all([
        db.collection('medicines').count(),
        db.collection('products').count(),
        db.collection('articles').count(),
        db.collection('users').count()
      ]);

      // 模拟增长数据（实际项目中应该从数据库获取）
      this.setData({
        stats: {
          medicineCount: medicineRes.total || 156,
          productCount: productRes.total || 89,
          articleCount: articleRes.total || 234,
          userCount: userRes.total || 1280,
          medicineChange: 12,
          productChange: 8,
          articleChange: 15,
          userChange: 45
        }
      });
      
      app.hideLoading();
    } catch (error) {
      console.error('加载统计数据失败:', error);
      app.hideLoading();
      
      // 设置默认数据
      this.setData({
        stats: {
          medicineCount: 156,
          productCount: 89,
          articleCount: 234,
          userCount: 1280,
          medicineChange: 12,
          productChange: 8,
          articleChange: 15,
          userChange: 45
        }
      });
    }
  },

  // 格式化时间
  formatTime(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    
    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出管理员账号吗？',
      success: (res) => {
        if (res.confirm) {
          app.clearUserInfo();
          app.showSuccess('已退出登录');
          
          setTimeout(() => {
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }, 1500);
        }
      }
    });
  },

  // 管理中药材
  manageMedicines() {
    wx.navigateTo({
      url: '/pages/medicines/medicines?mode=admin'
    });
  },

  // 添加中药材
  addMedicine() {
    wx.navigateTo({
      url: '/pages/medicines/medicines?mode=add'
    });
  },

  // 管理文创产品
  manageProducts() {
    wx.navigateTo({
      url: '/pages/products/products?mode=admin'
    });
  },

  // 添加文创产品
  addProduct() {
    wx.navigateTo({
      url: '/pages/products/products?mode=add'
    });
  },

  // 管理养生文章
  manageArticles() {
    wx.navigateTo({
      url: '/pages/articles/articles?mode=admin'
    });
  },

  // 添加养生文章
  addArticle() {
    wx.navigateTo({
      url: '/pages/articles/articles?mode=add'
    });
  },

  // 管理用户
  manageUsers() {
    wx.showModal({
      title: '用户管理',
      content: '用户管理功能正在开发中...',
      showCancel: false
    });
  },

  // 添加用户
  addUser() {
    wx.showModal({
      title: '添加用户',
      content: '添加用户功能正在开发中...',
      showCancel: false
    });
  },

  // 数据备份
  dataBackup() {
    wx.showLoading({
      title: '备份中...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      app.showSuccess('数据备份完成');
    }, 2000);
  },

  // 数据导入
  dataImport() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      success: (res) => {
        app.showLoading('导入中...');
        
        setTimeout(() => {
          app.hideLoading();
          app.showSuccess('数据导入完成');
          this.loadStatsData();
        }, 2000);
      }
    });
  },

  // 系统设置
  systemSettings() {
    wx.showModal({
      title: '系统设置',
      content: '系统设置功能正在开发中...',
      showCancel: false
    });
  },

  // 查看日志
  viewLogs() {
    wx.showModal({
      title: '操作日志',
      content: '操作日志功能正在开发中...',
      showCancel: false
    });
  },

  // 批量导入
  batchImport() {
    wx.showActionSheet({
      itemList: ['导入中药材', '导入文创产品', '导入养生文章'],
      success: (res) => {
        const types = ['中药材', '文创产品', '养生文章'];
        app.showLoading(`导入${types[res.tapIndex]}中...`);
        
        setTimeout(() => {
          app.hideLoading();
          app.showSuccess(`${types[res.tapIndex]}导入完成`);
          this.loadStatsData();
        }, 2000);
      }
    });
  },

  // 数据导出
  dataExport() {
    wx.showActionSheet({
      itemList: ['导出中药材', '导出文创产品', '导出养生文章', '导出全部数据'],
      success: (res) => {
        const types = ['中药材', '文创产品', '养生文章', '全部数据'];
        app.showLoading(`导出${types[res.tapIndex]}中...`);
        
        setTimeout(() => {
          app.hideLoading();
          app.showSuccess(`${types[res.tapIndex]}导出完成`);
        }, 2000);
      }
    });
  },

  // 清理缓存
  clearCache() {
    wx.showModal({
      title: '清理缓存',
      content: '确定要清理系统缓存吗？',
      success: (res) => {
        if (res.confirm) {
          app.showLoading('清理中...');
          
          setTimeout(() => {
            app.hideLoading();
            app.showSuccess('缓存清理完成');
          }, 1500);
        }
      }
    });
  }
});

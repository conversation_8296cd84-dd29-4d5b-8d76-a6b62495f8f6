<!--admin.wxml-->
<view class="container">
  <!-- 管理员信息 -->
  <view class="admin-header card">
    <view class="admin-info">
      <image class="admin-avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="admin-details">
        <text class="admin-name">{{userInfo.nickName}}</text>
        <text class="admin-role">系统管理员</text>
        <text class="login-time">登录时间：{{loginTime}}</text>
      </view>
    </view>
    <button class="btn btn-outline logout-btn" bindtap="logout">退出登录</button>
  </view>

  <!-- 数据统计 -->
  <view class="stats-overview card">
    <text class="title">数据统计</text>
    <view class="stats-grid">
      <view class="stat-card">
        <text class="stat-number">{{stats.medicineCount}}</text>
        <text class="stat-label">中药材</text>
        <text class="stat-change">+{{stats.medicineChange}}</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{stats.productCount}}</text>
        <text class="stat-label">文创产品</text>
        <text class="stat-change">+{{stats.productChange}}</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{stats.articleCount}}</text>
        <text class="stat-label">养生文章</text>
        <text class="stat-change">+{{stats.articleChange}}</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{stats.userCount}}</text>
        <text class="stat-label">注册用户</text>
        <text class="stat-change">+{{stats.userChange}}</text>
      </view>
    </view>
  </view>

  <!-- 管理功能 -->
  <view class="management-section">
    <text class="title">数据管理</text>
    
    <!-- 中药材管理 -->
    <view class="management-card card">
      <view class="card-header">
        <view class="card-info">
          <text class="card-title">中药材管理</text>
          <text class="card-desc">管理中药材信息、价格、库存等</text>
        </view>
        <image class="card-icon" src="/images/medicine-icon.png" mode="aspectFit"></image>
      </view>
      <view class="card-actions">
        <button class="btn btn-primary" bindtap="manageMedicines">进入管理</button>
        <button class="btn btn-secondary" bindtap="addMedicine">添加药材</button>
      </view>
    </view>

    <!-- 文创产品管理 -->
    <view class="management-card card">
      <view class="card-header">
        <view class="card-info">
          <text class="card-title">文创产品管理</text>
          <text class="card-desc">管理文创产品信息、价格、销量等</text>
        </view>
        <image class="card-icon" src="/images/product-icon.png" mode="aspectFit"></image>
      </view>
      <view class="card-actions">
        <button class="btn btn-primary" bindtap="manageProducts">进入管理</button>
        <button class="btn btn-secondary" bindtap="addProduct">添加产品</button>
      </view>
    </view>

    <!-- 养生文章管理 -->
    <view class="management-card card">
      <view class="card-header">
        <view class="card-info">
          <text class="card-title">养生文章管理</text>
          <text class="card-desc">管理养生科普文章、发布状态等</text>
        </view>
        <image class="card-icon" src="/images/article-icon.png" mode="aspectFit"></image>
      </view>
      <view class="card-actions">
        <button class="btn btn-primary" bindtap="manageArticles">进入管理</button>
        <button class="btn btn-secondary" bindtap="addArticle">添加文章</button>
      </view>
    </view>

    <!-- 用户管理 -->
    <view class="management-card card">
      <view class="card-header">
        <view class="card-info">
          <text class="card-title">用户管理</text>
          <text class="card-desc">管理用户信息、权限设置等</text>
        </view>
        <image class="card-icon" src="/images/user-icon.png" mode="aspectFit"></image>
      </view>
      <view class="card-actions">
        <button class="btn btn-primary" bindtap="manageUsers">进入管理</button>
        <button class="btn btn-secondary" bindtap="addUser">添加用户</button>
      </view>
    </view>
  </view>

  <!-- 系统工具 -->
  <view class="tools-section">
    <text class="title">系统工具</text>
    <view class="tools-grid">
      <view class="tool-item" bindtap="dataBackup">
        <image class="tool-icon" src="/images/backup-icon.png" mode="aspectFit"></image>
        <text class="tool-name">数据备份</text>
      </view>
      <view class="tool-item" bindtap="dataImport">
        <image class="tool-icon" src="/images/import-icon.png" mode="aspectFit"></image>
        <text class="tool-name">数据导入</text>
      </view>
      <view class="tool-item" bindtap="systemSettings">
        <image class="tool-icon" src="/images/settings-icon.png" mode="aspectFit"></image>
        <text class="tool-name">系统设置</text>
      </view>
      <view class="tool-item" bindtap="viewLogs">
        <image class="tool-icon" src="/images/log-icon.png" mode="aspectFit"></image>
        <text class="tool-name">操作日志</text>
      </view>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions card">
    <text class="title">快速操作</text>
    <view class="action-buttons">
      <button class="btn btn-primary" bindtap="batchImport">批量导入</button>
      <button class="btn btn-secondary" bindtap="dataExport">数据导出</button>
      <button class="btn btn-outline" bindtap="clearCache">清理缓存</button>
    </view>
  </view>
</view>

/**admin.wxss**/

/* 管理员头部 */
.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.admin-info {
  display: flex;
  align-items: center;
}

.admin-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.admin-details {
  display: flex;
  flex-direction: column;
}

.admin-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 5rpx;
}

.admin-role {
  font-size: 22rpx;
  color: #228B22;
  margin-bottom: 5rpx;
}

.login-time {
  font-size: 20rpx;
  color: #999;
}

.logout-btn {
  padding: 15rpx 30rpx;
  font-size: 24rpx;
}

/* 数据统计 */
.stats-overview {
  margin-bottom: 30rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-top: 20rpx;
}

.stat-card {
  background-color: #F5F2E8;
  border-radius: 15rpx;
  padding: 25rpx;
  text-align: center;
  position: relative;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.stat-change {
  font-size: 20rpx;
  color: #228B22;
  position: absolute;
  top: 15rpx;
  right: 15rpx;
}

/* 管理功能 */
.management-section {
  margin-bottom: 30rpx;
}

.management-card {
  margin-bottom: 25rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  display: block;
  margin-bottom: 8rpx;
}

.card-desc {
  font-size: 22rpx;
  color: #666;
}

.card-icon {
  width: 60rpx;
  height: 60rpx;
}

.card-actions {
  display: flex;
  gap: 15rpx;
}

.card-actions .btn {
  flex: 1;
  padding: 20rpx;
  font-size: 24rpx;
}

/* 系统工具 */
.tools-section {
  margin-bottom: 30rpx;
}

.tools-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-top: 20rpx;
}

.tool-item {
  background-color: white;
  border-radius: 15rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tool-icon {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 15rpx;
}

.tool-name {
  font-size: 24rpx;
  color: #333;
}

/* 快速操作 */
.quick-actions {
  margin-bottom: 30rpx;
}

.action-buttons {
  display: flex;
  gap: 15rpx;
  margin-top: 20rpx;
}

.action-buttons .btn {
  flex: 1;
  padding: 25rpx;
  font-size: 24rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .tools-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .card-actions {
    flex-direction: column;
  }
}

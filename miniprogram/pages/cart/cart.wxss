/**cart.wxss**/

/* 购物车头部 */
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.header-left {
  display: flex;
  flex-direction: column;
}

.cart-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #A0522D;
  margin-bottom: 5rpx;
}

.item-count {
  font-size: 24rpx;
  color: #666;
}

.edit-btn {
  font-size: 26rpx;
  color: #A0522D;
  padding: 10rpx 20rpx;
}

/* 购物车列表 */
.cart-list {
  margin-bottom: 120rpx;
}

.cart-item {
  background-color: white;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(160, 82, 45, 0.08);
  transition: all 0.3s ease;
  animation: cart-item-enter 0.3s ease-out;
}

@keyframes cart-item-enter {
  0% {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.cart-item.removing {
  animation: cart-item-exit 0.3s ease-in forwards;
}

@keyframes cart-item-exit {
  0% {
    opacity: 1;
    transform: translateX(0);
    height: auto;
  }
  100% {
    opacity: 0;
    transform: translateX(20rpx);
    height: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
}

.item-checkbox {
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
  display: flex;
  margin-right: 20rpx;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item-category {
  font-size: 22rpx;
  color: #228B22;
  background-color: #F0F8F0;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  align-self: flex-start;
  margin-bottom: 8rpx;
}

.item-spec {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.item-price-row {
  display: flex;
  align-items: baseline;
}

.item-price {
  font-size: 26rpx;
  font-weight: bold;
  color: #E74C3C;
  margin-right: 8rpx;
}

.item-unit {
  font-size: 20rpx;
  color: #999;
}

/* 数量控制 */
.item-quantity {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 15rpx;
}

.quantity-control {
  display: flex;
  align-items: center;
  border: 2rpx solid #E0E0E0;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #F5F5F5;
  border: none;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.quantity-btn:active {
  background-color: #A0522D;
  color: white;
  transform: scale(0.95);
}

.quantity-btn:disabled {
  color: #CCC;
  background-color: #F0F0F0;
}

.quantity-btn.disabled {
  color: #CCC;
  background-color: #F0F0F0;
}

.quantity-btn.disabled:active {
  background-color: #F0F0F0;
  color: #CCC;
  transform: none;
}

.quantity-btn.minus {
  border-radius: 6rpx 0 0 6rpx;
}

.quantity-btn.plus {
  border-radius: 0 6rpx 6rpx 0;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  font-size: 26rpx;
  border: none;
  background-color: white;
  color: #333;
  font-weight: bold;
}

.item-total {
  font-size: 22rpx;
  color: #A0522D;
  font-weight: bold;
}

/* 删除按钮 */
.item-delete {
  margin-left: 15rpx;
}

.delete-btn {
  background-color: #E74C3C;
  color: white;
  padding: 15rpx 25rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  border: none;
}

/* 空购物车 */
.empty-cart {
  text-align: center;
  padding: 150rpx 50rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 40rpx;
}

/* 推荐商品 */
.recommend-section {
  background-color: white;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 120rpx;
}

.recommend-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #A0522D;
  display: block;
  margin-bottom: 20rpx;
}

.recommend-scroll {
  white-space: nowrap;
}

.recommend-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  text-align: center;
  vertical-align: top;
}

.recommend-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
}

.recommend-name {
  font-size: 24rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recommend-price {
  font-size: 22rpx;
  color: #E74C3C;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.recommend-add-btn {
  background-color: #A0522D;
  color: white;
  padding: 12rpx 20rpx;
  border-radius: 15rpx;
  font-size: 20rpx;
  border: none;
}

/* 底部结算栏 */
.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #F0F0F0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.footer-left {
  display: flex;
  align-items: center;
}

.select-all {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
}

.select-all-text {
  font-size: 26rpx;
  color: #333;
  margin-left: 10rpx;
}

.selected-info {
  font-size: 24rpx;
  color: #666;
}

.footer-right {
  display: flex;
  align-items: center;
}

.total-info {
  margin-right: 20rpx;
}

.total-label {
  font-size: 24rpx;
  color: #666;
}

.total-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #E74C3C;
}

.checkout-btn {
  padding: 20rpx 40rpx;
  font-size: 26rpx;
  border-radius: 25rpx;
}

.checkout-btn:disabled {
  background-color: #CCC;
  color: #999;
}

/* 规格选择弹窗 */
.spec-modal {
  width: 90%;
  max-width: 600rpx;
  max-height: 70vh;
}

.product-info {
  display: flex;
  margin-bottom: 30rpx;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.product-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.product-price {
  font-size: 26rpx;
  font-weight: bold;
  color: #E74C3C;
}

.spec-options {
  margin-bottom: 30rpx;
}

.spec-title {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.spec-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.spec-item {
  padding: 15rpx 25rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
  background-color: #F8F8F8;
}

.spec-item.active {
  border-color: #A0522D;
  color: #A0522D;
  background-color: #FFF8F5;
}

.quantity-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-title {
  font-size: 26rpx;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .cart-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .item-info {
    width: 100%;
    margin-bottom: 20rpx;
  }
  
  .item-quantity {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 15rpx;
  }
  
  .cart-footer {
    flex-direction: column;
    padding: 15rpx 20rpx;
  }
  
  .footer-left {
    width: 100%;
    justify-content: space-between;
    margin-bottom: 15rpx;
  }
  
  .footer-right {
    width: 100%;
    justify-content: space-between;
  }
}

/* 调试工具样式 */
.debug-tools {
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 1rpx dashed #ddd;
}

.debug-title {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  display: block;
  margin-bottom: 20rpx;
}

.debug-buttons {
  display: flex;
  gap: 20rpx;
  justify-content: center;
  flex-wrap: wrap;
}

.debug-btn {
  font-size: 22rpx;
  padding: 15rpx 25rpx;
  border-color: #8B4513;
  color: #8B4513;
  min-width: 200rpx;
}

/* 推荐商品样式 */
.recommend-section {
  margin: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 24rpx rgba(139, 69, 19, 0.1);
}

.section-header {
  margin-bottom: 25rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
}

.recommend-scroll {
  white-space: nowrap;
}

.recommend-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 69, 19, 0.08);
  transition: all 0.3s ease;
  vertical-align: top;
}

.recommend-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(139, 69, 19, 0.12);
}

.recommend-image {
  width: 100%;
  height: 120rpx;
  border-radius: 10rpx;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
}

.recommend-icon {
  font-size: 50rpx;
  color: white;
  opacity: 0.8;
}

.recommend-info {
  text-align: center;
}

.recommend-name {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommend-price {
  margin-bottom: 8rpx;
}

.price {
  font-size: 24rpx;
  font-weight: bold;
  color: #E74C3C;
}

.original-price {
  font-size: 18rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 8rpx;
}

.recommend-tag {
  font-size: 16rpx;
  color: #E74C3C;
  background: rgba(231, 76, 60, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(231, 76, 60, 0.2);
}

/* 管理员工具样式 */
.admin-tools {
  margin: 30rpx;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.05));
  border-radius: 20rpx;
  padding: 30rpx;
  border: 2rpx dashed #FFD700;
}

.admin-header {
  text-align: center;
  margin-bottom: 20rpx;
}

.admin-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
}

.admin-buttons {
  display: flex;
  gap: 15rpx;
  justify-content: center;
  flex-wrap: wrap;
}

.admin-btn {
  font-size: 20rpx;
  padding: 12rpx 20rpx;
  border: 1rpx solid #8B4513;
  color: #8B4513;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.admin-btn:active {
  background: rgba(139, 69, 19, 0.1);
}

.admin-btn.danger {
  border-color: #E74C3C;
  color: #E74C3C;
}

.admin-btn.danger:active {
  background: rgba(231, 76, 60, 0.1);
}

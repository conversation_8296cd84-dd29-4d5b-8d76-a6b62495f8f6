<!--cart.wxml-->
<view class="container">
  <!-- 购物车头部 -->
  <view class="cart-header card" wx:if="{{cartItems.length > 0}}">
    <view class="header-left">
      <text class="cart-title">购物车</text>
      <text class="item-count">共{{cartItems.length}}件商品</text>
    </view>
    <view class="header-right">
      <text class="edit-btn" bindtap="toggleEditMode">{{editMode ? '完成' : '编辑'}}</text>
    </view>
  </view>

  <!-- 购物车列表 -->
  <view class="cart-list" wx:if="{{cartItems.length > 0}}">
    <view class="cart-item {{item.removing ? 'removing' : ''}}" wx:for="{{cartItems}}" wx:key="id">
      <!-- 选择框 -->
      <view class="item-checkbox">
        <checkbox checked="{{item.selected}}" bindchange="onItemSelect" data-id="{{item.id}}" />
      </view>
      
      <!-- 商品信息 -->
      <view class="item-info" bindtap="viewProduct" data-id="{{item.productId}}" data-type="{{item.type}}">
        <image class="item-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
        <view class="item-details">
          <text class="item-name">{{item.name}}</text>
          <text class="item-category">{{item.category}}</text>
          <text class="item-spec" wx:if="{{item.spec}}">规格：{{item.spec}}</text>
          <view class="item-price-row">
            <text class="item-price">¥{{item.price}}</text>
            <text class="item-unit" wx:if="{{item.unit}}">{{item.unit}}</text>
          </view>
        </view>
      </view>
      
      <!-- 数量控制 -->
      <view class="item-quantity">
        <view class="quantity-control">
          <button class="quantity-btn minus {{item.quantity <= 1 ? 'disabled' : ''}}" bindtap="decreaseQuantity" data-id="{{item.id}}" disabled="{{item.quantity <= 1}}">-</button>
          <input class="quantity-input" type="number" value="{{item.quantity}}" bindinput="onQuantityInput" data-id="{{item.id}}" maxlength="4" />
          <button class="quantity-btn plus" bindtap="increaseQuantity" data-id="{{item.id}}">+</button>
        </view>
        <text class="item-total">¥{{item.totalPrice}}</text>
      </view>
      
      <!-- 删除按钮 -->
      <view class="item-delete" wx:if="{{editMode}}">
        <button class="delete-btn" bindtap="deleteItem" data-id="{{item.id}}">删除</button>
      </view>
    </view>
  </view>

  <!-- 空购物车 -->
  <view class="empty-cart" wx:else>
    <image class="empty-image" src="/images/empty-cart.png" mode="aspectFit"></image>
    <text class="empty-text">购物车还是空的</text>
    <text class="empty-desc">快去挑选心仪的商品吧</text>
    <button class="btn btn-primary" bindtap="goShopping">去购物</button>

    <!-- 调试工具（仅管理员可见） -->
    <view class="debug-tools" wx:if="{{isAdmin}}">
      <text class="debug-title">调试工具</text>
      <view class="debug-buttons">
        <button class="btn btn-outline debug-btn" bindtap="addTestItems">添加测试商品</button>
        <button class="btn btn-outline debug-btn" bindtap="clearCart">清空购物车</button>
      </view>
    </view>
  </view>

  <!-- 推荐商品 -->
  <view class="recommend-section" wx:if="{{cartItems.length > 0 && recommendItems.length > 0}}">
    <text class="recommend-title">为您推荐</text>
    <scroll-view class="recommend-scroll" scroll-x="true">
      <view class="recommend-item" wx:for="{{recommendItems}}" wx:key="id" bindtap="addRecommendToCart" data-item="{{item}}">
        <image class="recommend-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
        <text class="recommend-name">{{item.name}}</text>
        <text class="recommend-price">¥{{item.price}}</text>
        <button class="recommend-add-btn">加入购物车</button>
      </view>
    </scroll-view>
  </view>

  <!-- 底部结算栏 -->
  <view class="cart-footer" wx:if="{{cartItems.length > 0}}">
    <view class="footer-left">
      <label class="select-all">
        <checkbox checked="{{allSelected}}" bindchange="onSelectAll" />
        <text class="select-all-text">全选</text>
      </label>
      <text class="selected-info">已选{{totalCount}}件</text>
    </view>
    <view class="footer-right">
      <view class="total-info">
        <text class="total-label">合计：</text>
        <text class="total-price">¥{{totalPrice}}</text>
      </view>
      <button class="checkout-btn btn btn-primary" bindtap="goCheckout" disabled="{{totalCount === 0}}">
        结算({{totalCount}})
      </button>
    </view>
  </view>
</view>

<!-- 商品规格选择弹窗 -->
<view class="modal-overlay" wx:if="{{showSpecModal}}" bindtap="hideSpecModal">
  <view class="modal-content spec-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择规格</text>
      <text class="modal-close" bindtap="hideSpecModal">×</text>
    </view>
    <view class="modal-body">
      <view class="product-info">
        <image class="product-image" src="{{selectedProduct.imageUrl}}" mode="aspectFill"></image>
        <view class="product-details">
          <text class="product-name">{{selectedProduct.name}}</text>
          <text class="product-price">¥{{selectedProduct.price}}</text>
        </view>
      </view>
      <view class="spec-options" wx:if="{{selectedProduct.specs}}">
        <text class="spec-title">规格</text>
        <view class="spec-list">
          <view class="spec-item {{selectedSpec === item ? 'active' : ''}}" 
                wx:for="{{selectedProduct.specs}}" wx:key="*this"
                bindtap="selectSpec" data-spec="{{item}}">
            {{item}}
          </view>
        </view>
      </view>
      <view class="quantity-section">
        <text class="quantity-title">数量</text>
        <view class="quantity-control">
          <button class="quantity-btn minus" bindtap="decreaseModalQuantity" disabled="{{modalQuantity <= 1}}">-</button>
          <input class="quantity-input" type="number" value="{{modalQuantity}}" bindinput="onModalQuantityInput" />
          <button class="quantity-btn plus" bindtap="increaseModalQuantity">+</button>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="hideSpecModal">取消</button>
      <button class="btn btn-primary" bindtap="confirmAddToCart">确定</button>
    </view>
  </view>
</view>

<!-- 推荐商品 -->
<view class="recommend-section" wx:if="{{cartItems.length === 0 || recommendItems.length > 0}}">
  <view class="section-header">
    <text class="section-title">为您推荐</text>
  </view>
  <scroll-view class="recommend-scroll" scroll-x="true" show-scrollbar="false">
    <view class="recommend-item" wx:for="{{recommendItems}}" wx:key="id" bindtap="addRecommendToCart" data-item="{{item}}">
      <view class="recommend-image">
        <text class="recommend-icon">{{item.imageUrl}}</text>
      </view>
      <view class="recommend-info">
        <text class="recommend-name">{{item.name}}</text>
        <view class="recommend-price">
          <text class="price">¥{{item.price}}</text>
          <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
        </view>
        <text class="recommend-tag" wx:if="{{item.isHot}}">热销</text>
      </view>
    </view>
  </scroll-view>
</view>

<!-- 管理员测试工具 -->
<view class="admin-tools" wx:if="{{isAdmin}}">
  <view class="admin-header">
    <text class="admin-title">👑 管理员工具</text>
  </view>
  <view class="admin-buttons">
    <button class="admin-btn" bindtap="addTestItems">添加测试商品</button>
    <button class="admin-btn danger" bindtap="clearCart">清空购物车</button>
  </view>
</view>

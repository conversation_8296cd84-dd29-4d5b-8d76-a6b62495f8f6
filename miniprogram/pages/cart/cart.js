// 购物车页面
var app = getApp();

Page({
  data: {
    cartItems: [],
    recommendItems: [],
    totalPrice: 0,
    totalCount: 0,
    selectedAll: false,
    isAdmin: false,
    
    // 编辑模式
    editMode: false,
    selectedItems: []
  },

  onLoad: function() {
    console.log('购物车页面加载中...');
    
    // 立即加载推荐商品，确保页面有内容显示
    this.loadRecommendItems();
    
    // 加载购物车数据
    this.loadCartItems();

    // 检查管理员身份
    var userInfo = app.globalData.userInfo;
    this.setData({
      isAdmin: userInfo && userInfo.role === 'admin'
    });
  },

  onShow: function() {
    console.log('购物车页面显示，开始加载数据...');
    this.loadCartItems();
    this.calculateTotal();
    // 更新购物车徽章
    if (app.updateCartBadge) {
      app.updateCartBadge();
    }
  },

  // 加载购物车商品
  loadCartItems: function() {
    var that = this;
    try {
      // 确保有用户信息
      that.ensureUserInfo();

      var userInfo = app.globalData.userInfo;
      if (!userInfo) {
        console.log('无法获取用户信息，购物车为空');
        that.setData({ cartItems: [] });
        that.calculateTotal();
        return;
      }

      // 从本地存储获取购物车数据
      var cartData = wx.getStorageSync('cart_' + userInfo.id) || [];
      console.log('购物车页面加载数据:', cartData);
      console.log('购物车数据数量:', cartData.length);

      // 处理购物车数据，计算总价等
      var processedItems = cartData.map(function(item) {
        var processedItem = {
          id: item.id,
          productId: item.productId,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          selected: item.selected !== false, // 默认选中
          totalPrice: (item.price * item.quantity).toFixed(2),
          // 确保必要字段存在
          imageUrl: item.imageUrl || '🛒',
          category: item.category || '未分类',
          type: item.type || 'product',
          spec: item.spec || (item.type === 'medicine' ? item.quantity + '克' : '1件'),
          unit: item.unit || (item.type === 'medicine' ? '/克' : '/件')
        };
        console.log('处理商品:', processedItem.name, '类型:', processedItem.type);
        return processedItem;
      });

      console.log('处理后的购物车数据:', processedItems);
      that.setData({
        cartItems: processedItems
      });

      // 如果购物车为空，显示调试信息
      if (processedItems.length === 0) {
        console.log('购物车为空，用户ID:', userInfo.id);
        console.log('存储键:', 'cart_' + userInfo.id);
        
        // 为了演示，可以添加一些示例商品到购物车
        if (app.globalData.isAdmin) {
          console.log('管理员用户，可以添加测试数据');
        }
      }

      that.calculateTotal();
    } catch (error) {
      console.error('加载购物车失败:', error);
      that.setData({ cartItems: [] });
      that.calculateTotal();
    }
  },

  // 加载推荐商品
  loadRecommendItems: function() {
    var that = this;
    try {
      console.log('加载推荐商品数据...');
      // 丰富的推荐商品数据，使用emoji作为图片占位符
      var recommendItems = [
        {
          id: 'rec1',
          name: '人参',
          price: 12.5,
          originalPrice: 15.0,
          imageUrl: '🌿',
          type: 'medicine',
          category: '补气药',
          description: '大补元气，复脉固脱',
          isHot: true
        },
        {
          id: 'rec2',
          name: '枸杞子',
          price: 8.8,
          originalPrice: 10.0,
          imageUrl: '🍇',
          type: 'medicine',
          category: '补血药',
          description: '滋补肝肾，益精明目',
          isHot: true
        },
        {
          id: 'rec3',
          name: '金银花',
          price: 18.5,
          originalPrice: 22.0,
          imageUrl: '🌼',
          type: 'medicine',
          category: '清热药',
          description: '清热解毒，疏散风热',
          isHot: true
        },
        {
          id: 'rec4',
          name: '中医养生茶具套装',
          price: 168,
          originalPrice: 198,
          imageUrl: '🍵',
          type: 'product',
          category: '茶具',
          description: '精美紫砂茶具，适合泡制养生茶',
          isHot: true
        },
        {
          id: 'rec5',
          name: '艾灸养生套装',
          price: 128,
          originalPrice: 158,
          imageUrl: '🔥',
          type: 'product',
          category: '养生用品',
          description: '传统艾灸工具，居家养生必备',
          isHot: true
        },
        {
          id: 'rec6',
          name: '本草纲目典藏版',
          price: 58,
          originalPrice: 68,
          imageUrl: '📚',
          type: 'product',
          category: '书籍',
          description: '李时珍经典著作，中医药学必读',
          isHot: false
        },
        {
          id: 'rec7',
          name: '酸枣仁',
          price: 28.5,
          originalPrice: 32.0,
          imageUrl: '🌰',
          type: 'medicine',
          category: '安神药',
          description: '养心补肝，宁心安神',
          isHot: true
        },
        {
          id: 'rec8',
          name: '足浴盆',
          price: 298,
          originalPrice: 358,
          imageUrl: '🛁',
          type: 'product',
          category: '养生用品',
          description: '智能恒温足浴盆，促进血液循环',
          isHot: true
        },
        {
          id: 'rec9',
          name: '陈皮',
          price: 13.8,
          originalPrice: 16.0,
          imageUrl: '🍊',
          type: 'medicine',
          category: '化痰药',
          description: '理气健脾，燥湿化痰',
          isHot: true
        },
        {
          id: 'rec10',
          name: '檀香线香套装',
          price: 58,
          originalPrice: 78,
          imageUrl: '🕯️',
          type: 'product',
          category: '香薰用品',
          description: '天然檀香制作，安神助眠',
          isHot: true
        }
      ];

      that.setData({
        recommendItems: recommendItems
      });
      
      console.log('推荐商品数据加载完成，共', recommendItems.length, '件');
    } catch (error) {
      console.error('加载推荐商品失败:', error);
    }
  },

  // 计算总价
  calculateTotal: function() {
    var cartItems = this.data.cartItems;
    var totalPrice = 0;
    var totalCount = 0;
    var selectedCount = 0;

    for (var i = 0; i < cartItems.length; i++) {
      var item = cartItems[i];
      if (item.selected) {
        totalPrice += item.price * item.quantity;
        totalCount += item.quantity;
        selectedCount++;
      }
    }

    this.setData({
      totalPrice: totalPrice.toFixed(2),
      totalCount: totalCount,
      allSelected: selectedCount === cartItems.length && cartItems.length > 0
    });

    console.log('购物车总价计算:', {
      totalPrice: totalPrice.toFixed(2),
      totalCount: totalCount,
      selectedCount: selectedCount,
      totalItems: cartItems.length
    });
  },

  // 选择商品
  onItemSelect: function(e) {
    var id = e.currentTarget.dataset.id;
    var checked = e.detail.value.length > 0;
    var cartItems = this.data.cartItems;

    for (var i = 0; i < cartItems.length; i++) {
      if (cartItems[i].id === id) {
        cartItems[i].selected = checked;
        break;
      }
    }

    this.setData({
      cartItems: cartItems
    });

    this.calculateTotal();
    this.saveCartData();
  },

  // 全选/取消全选
  onSelectAll: function(e) {
    var checked = e.detail.value.length > 0;
    var cartItems = this.data.cartItems;

    for (var i = 0; i < cartItems.length; i++) {
      cartItems[i].selected = checked;
    }

    this.setData({
      cartItems: cartItems,
      allSelected: checked
    });

    this.calculateTotal();
    this.saveCartData();
  },

  // 增加数量
  increaseQuantity: function(e) {
    var id = e.currentTarget.dataset.id;
    var cartItems = this.data.cartItems;

    for (var i = 0; i < cartItems.length; i++) {
      if (cartItems[i].id === id) {
        cartItems[i].quantity++;
        cartItems[i].totalPrice = (cartItems[i].price * cartItems[i].quantity).toFixed(2);
        break;
      }
    }

    this.setData({
      cartItems: cartItems
    });

    this.calculateTotal();
    this.saveCartData();
  },

  // 减少数量
  decreaseQuantity: function(e) {
    var id = e.currentTarget.dataset.id;
    var cartItems = this.data.cartItems;

    for (var i = 0; i < cartItems.length; i++) {
      if (cartItems[i].id === id && cartItems[i].quantity > 1) {
        cartItems[i].quantity--;
        cartItems[i].totalPrice = (cartItems[i].price * cartItems[i].quantity).toFixed(2);
        break;
      }
    }

    this.setData({
      cartItems: cartItems
    });

    this.calculateTotal();
    this.saveCartData();
  },

  // 数量输入
  onQuantityInput: function(e) {
    var id = e.currentTarget.dataset.id;
    var value = parseInt(e.detail.value) || 1;
    var cartItems = this.data.cartItems;

    // 限制数量范围
    if (value < 1) value = 1;
    if (value > 9999) value = 9999;

    for (var i = 0; i < cartItems.length; i++) {
      if (cartItems[i].id === id) {
        cartItems[i].quantity = value;
        cartItems[i].totalPrice = (cartItems[i].price * cartItems[i].quantity).toFixed(2);
        break;
      }
    }

    this.setData({
      cartItems: cartItems
    });

    this.calculateTotal();
    this.saveCartData();
  },

  // 删除商品
  deleteItem: function(e) {
    var that = this;
    var id = e.currentTarget.dataset.id;
    var cartItems = that.data.cartItems;
    var item = null;
    var itemIndex = -1;

    // 找到要删除的商品
    for (var i = 0; i < cartItems.length; i++) {
      if (cartItems[i].id === id) {
        item = cartItems[i];
        itemIndex = i;
        break;
      }
    }

    if (!item) return;

    wx.showModal({
      title: '确认删除',
      content: '确定要删除"' + item.name + '"吗？',
      success: function(res) {
        if (res.confirm) {
          cartItems.splice(itemIndex, 1);

          that.setData({
            cartItems: cartItems
          });

          that.calculateTotal();
          that.saveCartData();

          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 保存购物车数据
  saveCartData: function() {
    var userInfo = app.globalData.userInfo;
    if (userInfo) {
      var cartKey = 'cart_' + userInfo.id;
      wx.setStorageSync(cartKey, this.data.cartItems);
    }
  },

  // 切换编辑模式
  toggleEditMode: function() {
    this.setData({
      editMode: !this.data.editMode
    });
  },

  // 查看商品详情
  viewProduct: function(e) {
    var id = e.currentTarget.dataset.id;
    var type = e.currentTarget.dataset.type;

    if (type === 'medicine') {
      wx.navigateTo({
        url: '/pages/medicine-detail/medicine-detail?id=' + id
      });
    } else {
      wx.navigateTo({
        url: '/pages/product-detail/product-detail?id=' + id
      });
    }
  },

  // 去购物
  goShopping: function() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 结算
  goCheckout: function() {
    var selectedItems = this.data.cartItems.filter(function(item) {
      return item.selected;
    });

    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      });
      return;
    }

    // 暂时显示提示，实际项目中跳转到结算页面
    wx.showModal({
      title: '结算功能',
      content: '已选择 ' + selectedItems.length + ' 件商品，总金额 ¥' + this.data.totalPrice + '\n\n结算功能正在开发中...',
      showCancel: false
    });

    // wx.navigateTo({
    //   url: '/pages/checkout/checkout'
    // });
  },

  // 添加测试商品（管理员功能）
  addTestItems: function() {
    var that = this;
    var userInfo = app.globalData.userInfo;

    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    var testItems = [
      {
        id: 'test_' + Date.now() + '_1',
        productId: 'test_medicine_1',
        name: '测试人参',
        price: 12.5,
        quantity: 2,
        selected: true,
        imageUrl: '🌿',
        type: 'medicine',
        category: '补气药',
        spec: '2克',
        unit: '/克',
        totalPrice: '25.00'
      },
      {
        id: 'test_' + Date.now() + '_2',
        productId: 'test_product_1',
        name: '测试茶具套装',
        price: 168,
        quantity: 1,
        selected: true,
        imageUrl: '🍵',
        type: 'product',
        category: '茶具',
        spec: '1套',
        unit: '/套',
        totalPrice: '168.00'
      }
    ];

    var cartItems = that.data.cartItems.concat(testItems);

    that.setData({
      cartItems: cartItems
    });

    that.calculateTotal();
    that.saveCartData();

    wx.showToast({
      title: '测试商品已添加',
      icon: 'success'
    });
  },

  // 清空购物车（管理员功能）
  clearCart: function() {
    var that = this;
    wx.showModal({
      title: '确认清空',
      content: '确定要清空购物车吗？',
      success: function(res) {
        if (res.confirm) {
          that.setData({
            cartItems: []
          });

          that.calculateTotal();
          that.saveCartData();

          wx.showToast({
            title: '购物车已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  // 添加推荐商品到购物车
  addRecommendToCart: function(e) {
    var item = e.currentTarget.dataset.item;
    
    // 检查是否已登录
    if (!app.globalData.userInfo) {
      wx.showModal({
        title: '请先登录',
        content: '登录后才能添加商品到购物车',
        success: function(res) {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 添加到购物车逻辑
    var cartItem = {
      id: 'cart_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      productId: item.id,
      name: item.name,
      price: item.price,
      imageUrl: item.imageUrl,
      type: item.type,
      category: item.category,
      quantity: 1,
      selected: true,
      spec: item.type === 'medicine' ? '1克' : '1件',
      unit: item.type === 'medicine' ? '/克' : '/件'
    };

    var cartItems = this.data.cartItems;
    cartItems.push(cartItem);
    
    this.setData({
      cartItems: cartItems
    });
    
    this.calculateTotal();
    this.saveCartData();
    
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success'
    });
  },

  // 确保用户信息存在（创建游客账号）
  ensureUserInfo: function() {
    if (!app.globalData.userInfo) {
      // 创建游客账号
      var guestUser = {
        id: 'guest_' + Date.now(),
        name: '游客用户',
        avatar: '👤',
        role: 'user',
        isGuest: true
      };

      app.setUserInfo(guestUser);
      console.log('购物车页面创建游客账号:', guestUser);
    }
  }
});

<!--门店地图页面-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <input class="search-input" placeholder="搜索门店名称或地址..." value="{{searchKeyword}}" bindinput="onSearchInput" />
      <button class="search-btn" bindtap="onSearch">
        <text class="search-icon">🔍</text>
      </button>
    </view>
    <view class="location-info">
      <text class="current-location">📍 当前位置：{{currentLocation}}</text>
      <button class="relocate-btn" bindtap="getCurrentLocation">重新定位</button>
    </view>
  </view>

  <!-- 地图容器 -->
  <view class="map-container">
    <map 
      id="storeMap"
      class="store-map"
      longitude="{{mapCenter.longitude}}"
      latitude="{{mapCenter.latitude}}"
      scale="{{mapScale}}"
      markers="{{markers}}"
      show-location="{{true}}"
      bindmarkertap="onMarkerTap"
      bindregionchange="onRegionChange"
      bindtap="onMapTap">
    </map>
    
    <!-- 地图控制按钮 -->
    <view class="map-controls">
      <view class="control-btn zoom-in" bindtap="zoomIn">
        <text class="control-icon">+</text>
      </view>
      <view class="control-btn zoom-out" bindtap="zoomOut">
        <text class="control-icon">-</text>
      </view>
      <view class="control-btn my-location" bindtap="moveToMyLocation">
        <text class="control-icon">📍</text>
      </view>
    </view>
  </view>

  <!-- 门店列表 -->
  <view class="store-list-section">
    <view class="list-header">
      <text class="list-title">附近门店 ({{storeList.length}})</text>
      <view class="sort-options">
        <text class="sort-item {{sortType === 'distance' ? 'active' : ''}}" bindtap="changeSortType" data-type="distance">距离</text>
        <text class="sort-item {{sortType === 'rating' ? 'active' : ''}}" bindtap="changeSortType" data-type="rating">评分</text>
      </view>
    </view>
    
    <scroll-view class="store-list" scroll-y="true">
      <view class="store-item {{selectedStoreId === item.id ? 'selected' : ''}}" 
            wx:for="{{storeList}}" wx:key="id" 
            bindtap="selectStore" data-store="{{item}}">
        <view class="store-info">
          <text class="store-name">{{item.name}}</text>
          <text class="store-address">{{item.address}}</text>
          <view class="store-meta">
            <text class="store-distance">{{item.distance}}km</text>
            <view class="store-rating">
              <text class="rating-stars">★★★★★</text>
              <text class="rating-score">{{item.rating}}</text>
            </view>
            <text class="store-status {{item.isOpen ? 'open' : 'closed'}}">
              {{item.isOpen ? '营业中' : '已打烊'}}
            </text>
          </view>
          <view class="store-services">
            <text class="service-tag" wx:for="{{item.services}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
        <view class="store-actions">
          <button class="action-btn call-btn" bindtap="callStore" data-phone="{{item.phone}}" catchtap="stopPropagation">
            <text class="action-icon">📞</text>
            <text class="action-text">电话</text>
          </button>
          <button class="action-btn navigate-btn" bindtap="navigateToStore" data-store="{{item}}" catchtap="stopPropagation">
            <text class="action-icon">🧭</text>
            <text class="action-text">导航</text>
          </button>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 门店详情弹窗 -->
  <view class="modal-overlay" wx:if="{{showStoreDetail}}" bindtap="hideStoreDetail">
    <view class="modal-content store-detail-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{selectedStore.name}}</text>
        <text class="modal-close" bindtap="hideStoreDetail">×</text>
      </view>
      <view class="modal-body">
        <view class="detail-section">
          <text class="detail-label">地址</text>
          <text class="detail-value">{{selectedStore.address}}</text>
        </view>
        <view class="detail-section">
          <text class="detail-label">电话</text>
          <text class="detail-value">{{selectedStore.phone}}</text>
        </view>
        <view class="detail-section">
          <text class="detail-label">营业时间</text>
          <text class="detail-value">{{selectedStore.businessHours}}</text>
        </view>
        <view class="detail-section">
          <text class="detail-label">服务项目</text>
          <view class="services-list">
            <text class="service-item" wx:for="{{selectedStore.services}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
        <view class="detail-section" wx:if="{{selectedStore.description}}">
          <text class="detail-label">门店介绍</text>
          <text class="detail-value">{{selectedStore.description}}</text>
        </view>
        <view class="detail-section">
          <text class="detail-label">门店图片</text>
          <scroll-view class="store-images" scroll-x="true">
            <image class="store-image" wx:for="{{selectedStore.images}}" wx:key="*this" 
                   src="{{item}}" mode="aspectFill" bindtap="previewImage" data-url="{{item}}"></image>
          </scroll-view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn btn-outline" bindtap="callStore" data-phone="{{selectedStore.phone}}">拨打电话</button>
        <button class="btn btn-primary" bindtap="navigateToStore" data-store="{{selectedStore}}">开始导航</button>
      </view>
    </view>
  </view>

  <!-- 筛选弹窗 -->
  <view class="modal-overlay" wx:if="{{showFilterModal}}" bindtap="hideFilterModal">
    <view class="modal-content filter-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">筛选门店</text>
        <text class="modal-close" bindtap="hideFilterModal">×</text>
      </view>
      <view class="modal-body">
        <view class="filter-group">
          <text class="filter-title">距离范围</text>
          <view class="filter-options">
            <view class="filter-option {{distanceFilter === item.value ? 'active' : ''}}" 
                  wx:for="{{distanceOptions}}" wx:key="value"
                  bindtap="selectDistanceFilter" data-value="{{item.value}}">
              {{item.label}}
            </view>
          </view>
        </view>
        <view class="filter-group">
          <text class="filter-title">营业状态</text>
          <view class="filter-options">
            <view class="filter-option {{statusFilter === 'all' ? 'active' : ''}}" bindtap="selectStatusFilter" data-value="all">全部</view>
            <view class="filter-option {{statusFilter === 'open' ? 'active' : ''}}" bindtap="selectStatusFilter" data-value="open">营业中</view>
          </view>
        </view>
        <view class="filter-group">
          <text class="filter-title">服务项目</text>
          <view class="filter-options">
            <view class="filter-option {{serviceFilter === item ? 'active' : ''}}" 
                  wx:for="{{serviceOptions}}" wx:key="*this"
                  bindtap="selectServiceFilter" data-value="{{item}}">
              {{item}}
            </view>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn btn-outline" bindtap="resetFilter">重置</button>
        <button class="btn btn-primary" bindtap="applyFilter">应用</button>
      </view>
    </view>
  </view>

  <!-- 浮动按钮 -->
  <view class="float-actions">
    <view class="float-btn filter-btn" bindtap="showFilterModal">
      <text class="float-icon">🔧</text>
    </view>
  </view>
</view>

// 中药材调试页面
Page({
  data: {
    debugInfo: [],
    medicineList: [],
    testResults: []
  },

  onLoad: function() {
    console.log('中药材调试页面加载');
    this.runDiagnostics();
  },

  // 运行诊断
  runDiagnostics: function() {
    var debugInfo = [];
    var that = this;

    // 1. 检查页面数据
    debugInfo.push({
      type: 'info',
      title: '页面数据检查',
      content: '开始检查中药材页面数据...'
    });

    // 2. 模拟获取中药材数据
    try {
      var defaultMedicines = this.getDefaultMedicines();
      debugInfo.push({
        type: 'success',
        title: '数据获取成功',
        content: '成功获取 ' + defaultMedicines.length + ' 条中药材数据'
      });

      // 3. 检查图片路径
      var imageErrors = [];
      for (var i = 0; i < defaultMedicines.length; i++) {
        var medicine = defaultMedicines[i];
        if (!medicine.imageUrl || medicine.imageUrl.indexOf('🌿') >= 0 || medicine.imageUrl.indexOf('🍊') >= 0) {
          imageErrors.push(medicine.name + ' (ID: ' + medicine.id + ')');
        }
      }

      if (imageErrors.length > 0) {
        debugInfo.push({
          type: 'warning',
          title: '图片路径问题',
          content: '以下中药材仍使用emoji图标: ' + imageErrors.join(', ')
        });
      } else {
        debugInfo.push({
          type: 'success',
          title: '图片路径检查',
          content: '所有中药材都配置了正确的图片路径'
        });
      }

      // 4. 检查ID重复
      var ids = defaultMedicines.map(function(item) { return item.id; });
      var duplicateIds = ids.filter(function(id, index) { return ids.indexOf(id) !== index; });
      
      if (duplicateIds.length > 0) {
        debugInfo.push({
          type: 'error',
          title: 'ID重复问题',
          content: '发现重复的ID: ' + duplicateIds.join(', ')
        });
      } else {
        debugInfo.push({
          type: 'success',
          title: 'ID唯一性检查',
          content: '所有中药材ID都是唯一的'
        });
      }

      this.setData({
        debugInfo: debugInfo,
        medicineList: defaultMedicines
      });

    } catch (error) {
      debugInfo.push({
        type: 'error',
        title: '数据获取失败',
        content: '错误信息: ' + error.message
      });
      
      this.setData({
        debugInfo: debugInfo
      });
    }
  },

  // 获取默认中药材数据（复制自medicines.js）
  getDefaultMedicines: function() {
    return [
      {
        id: '1',
        name: '人参',
        category: '补气药',
        imageUrl: '/images/中药材/人参.jpg'
      },
      {
        id: '2',
        name: '黄芪',
        category: '补气药',
        imageUrl: '/images/中药材/黄芪.jpg'
      },
      {
        id: '3',
        name: '甘草',
        category: '补气药',
        imageUrl: '/images/中药材/甘草.jpg'
      },
      {
        id: '11',
        name: '金银花',
        category: '清热药',
        imageUrl: '/images/中药材/金银花.jpg'
      }
      // 简化版本，只测试几个关键的
    ];
  },

  // 测试图片加载
  testImageLoad: function() {
    var that = this;
    var testResults = [];
    var medicineList = this.data.medicineList;

    for (var i = 0; i < medicineList.length; i++) {
      testResults.push({
        id: medicineList[i].id,
        name: medicineList[i].name,
        imageUrl: medicineList[i].imageUrl,
        status: 'testing'
      });
    }

    this.setData({
      testResults: testResults
    });
  },

  // 图片加载成功
  onImageLoad: function(e) {
    var id = e.currentTarget.dataset.id;
    var testResults = this.data.testResults;
    
    for (var i = 0; i < testResults.length; i++) {
      if (testResults[i].id === id) {
        testResults[i].status = 'success';
        break;
      }
    }
    
    this.setData({ testResults: testResults });
    console.log('图片加载成功:', id);
  },

  // 图片加载失败
  onImageError: function(e) {
    var id = e.currentTarget.dataset.id;
    var testResults = this.data.testResults;
    
    for (var i = 0; i < testResults.length; i++) {
      if (testResults[i].id === id) {
        testResults[i].status = 'error';
        break;
      }
    }
    
    this.setData({ testResults: testResults });
    console.log('图片加载失败:', id);
  },

  // 跳转到中药材页面
  goToMedicines: function() {
    wx.switchTab({
      url: '/pages/medicines/medicines'
    });
  },

  // 返回首页
  goHome: function() {
    wx.navigateBack();
  }
});

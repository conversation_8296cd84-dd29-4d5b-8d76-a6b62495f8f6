# 云数据库 JSON 数据导入指南

## 📋 文件说明

已将 `database-init.js` 中的所有数据转换为 **JSON Lines** 格式，可直接导入微信云数据库。

### 🔍 格式说明
- **JSON Lines 格式**：每行一个 JSON 对象，不是 JSON 数组
- **微信云数据库要求**：必须使用 JSON Lines 格式进行导入
- **文件编码**：UTF-8 编码，无 BOM

### 📁 文件列表

```
database-json/
├── users.json          # 用户数据 (2条记录)
├── medicines.json      # 中药材数据 (5条记录)
├── products.json       # 文创产品数据 (6条记录)
├── articles.json       # 养生文章数据 (4条记录)
└── orders.json         # 订单数据 (3条记录)
```

## 🚀 导入步骤

### 方法1：云开发控制台导入（推荐）

#### 1. 打开云开发控制台
- 在微信开发者工具中点击"云开发"
- 或访问 https://console.cloud.tencent.com/tcb

#### 2. 创建数据库集合
依次创建以下集合：
- `users` - 用户表
- `medicines` - 中药材表  
- `products` - 文创产品表
- `articles` - 养生文章表
- `orders` - 订单表

#### 3. 导入数据
对每个集合执行以下操作：

1. **进入集合页面**
2. **点击"导入"按钮**
3. **选择对应的 JSON 文件**
4. **确认导入设置**：
   ```
   文件格式：JSON Lines
   冲突处理：跳过
   编码：UTF-8
   ```
5. **点击"确定"开始导入**

### 方法2：使用云函数批量导入

#### 1. 创建导入云函数
```javascript
// 在云函数中使用
const db = cloud.database();

// 导入用户数据
const users = require('./users.json');
for (const user of users) {
  await db.collection('users').add({ data: user });
}
```

#### 2. 调用云函数执行导入

### 方法3：使用小程序数据导入工具

1. **使用系统诊断工具**
2. **点击"强制初始化用户"**
3. **使用数据导入工具页面**

## 📊 数据详情

### users.json - 用户数据
```json
{
  "username": "admin",           // 用户名
  "password": "admin123",        // 密码
  "nickName": "系统管理员",       // 昵称
  "avatarUrl": "/images/default-avatar.png", // 头像
  "role": "admin",              // 角色 (admin/user)
  "createTime": "2024-03-15T10:00:00.000Z", // 创建时间
  "lastLoginTime": "2024-03-15T10:00:00.000Z" // 最后登录时间
}
```

**包含账号**：
- 管理员：`admin` / `admin123`
- 测试用户：`test` / `test123`

### medicines.json - 中药材数据
```json
{
  "name": "人参",               // 药材名称
  "category": "补气药",         // 分类
  "effect": "大补元气...",      // 功效
  "description": "人参为...",   // 详细描述
  "price": 12.5,               // 价格(每克)
  "stock": 500,                // 库存(克)
  "imageUrl": "/images/medicine1.jpg", // 图片
  "viewCount": 1250,           // 浏览次数
  "createTime": "2024-03-15T10:00:00.000Z", // 创建时间
  "updateTime": "2024-03-15T10:00:00.000Z"  // 更新时间
}
```

**包含药材**：人参、枸杞子、当归、黄芪、甘草

### products.json - 文创产品数据
```json
{
  "name": "中医养生茶具套装",   // 产品名称
  "category": "茶具",          // 分类
  "description": "精美的...",  // 产品描述
  "price": 168,               // 价格
  "stock": 50,                // 库存
  "sales": 125,               // 销量
  "isHot": true,              // 是否热门
  "imageUrl": "/images/product1.jpg", // 图片
  "createTime": "2024-03-15T10:00:00.000Z", // 创建时间
  "updateTime": "2024-03-15T10:00:00.000Z"  // 更新时间
}
```

**包含产品**：茶具套装、本草纲目、艾灸套装、香薰炉、穴位图、药膳食谱

### articles.json - 养生文章数据
```json
{
  "title": "春季养生：顺应自然，调养身心", // 文章标题
  "category": "春季养生",      // 分类
  "author": "张医师",          // 作者
  "summary": "春季是万物...", // 摘要
  "content": "春季养生的...", // 正文内容
  "imageUrl": "/images/article1.jpg", // 封面图
  "status": "published",      // 状态
  "views": 1250,             // 阅读量
  "createTime": "2024-03-15T00:00:00.000Z", // 创建时间
  "updateTime": "2024-03-15T10:00:00.000Z"  // 更新时间
}
```

**包含文章**：春季养生、食疗智慧、四季茶饮、五脏调理

### orders.json - 订单数据
```json
{
  "userId": "user_test_001",   // 用户ID
  "status": "pending_payment", // 订单状态
  "items": [...],             // 商品列表
  "address": {...},           // 收货地址
  "delivery": {...},          // 配送方式
  "payment": {...},           // 支付方式
  "totalAmount": "25.00",     // 总金额
  "createTime": "2024-03-15T14:30:25.000Z" // 创建时间
}
```

**包含订单状态**：待付款、已付款、已发货

## ⚠️ 注意事项

### 1. 数据格式
- 所有时间字段使用 ISO 8601 格式
- 价格字段使用数字类型
- 布尔值使用 true/false

### 2. 字段说明
- `_id` 字段会由云数据库自动生成
- 时间字段可以根据需要调整为当前时间
- 图片路径需要替换为实际的图片地址

### 3. 权限设置
导入前确保数据库权限设置正确：
```json
{
  "read": true,
  "write": true
}
```

### 4. 索引优化
建议为以下字段创建索引：
- `users.username`
- `medicines.name`
- `products.name`
- `articles.title`
- `orders.userId`

## 🔧 导入验证

### 检查导入结果
```javascript
// 在云函数或小程序中验证
const usersCount = await db.collection('users').count();
const medicinesCount = await db.collection('medicines').count();
const productsCount = await db.collection('products').count();
const articlesCount = await db.collection('articles').count();
const ordersCount = await db.collection('orders').count();

console.log('导入结果:', {
  users: usersCount.total,      // 应该是 2
  medicines: medicinesCount.total, // 应该是 5
  products: productsCount.total,   // 应该是 6
  articles: articlesCount.total,   // 应该是 4
  orders: ordersCount.total        // 应该是 3
});
```

### 测试功能
导入完成后测试以下功能：
- [ ] 管理员登录 (admin/admin123)
- [ ] 普通用户登录 (test/test123)
- [ ] 中药材列表显示
- [ ] 文创产品列表显示
- [ ] 养生文章列表显示
- [ ] 订单列表显示

## 🎯 快速导入脚本

### 一键导入所有数据
```bash
# 在云开发控制台依次执行
1. 创建 users 集合 → 导入 users.json
2. 创建 medicines 集合 → 导入 medicines.json  
3. 创建 products 集合 → 导入 products.json
4. 创建 articles 集合 → 导入 articles.json
5. 创建 orders 集合 → 导入 orders.json
```

## 📞 技术支持

如果导入过程中遇到问题：

1. **检查 JSON 格式**：确保文件格式正确
2. **检查权限设置**：确保有写入权限
3. **查看错误日志**：在控制台查看详细错误信息
4. **使用诊断工具**：使用小程序中的系统诊断工具

---

**提示**：建议先在测试环境中导入验证，确认无误后再在生产环境中使用。

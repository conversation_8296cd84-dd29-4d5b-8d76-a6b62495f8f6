# 简化版云函数使用指南

## 🎯 概述

`simpleFunction` 是专为"慧心制药"小程序设计的简化版云函数，提供核心功能和测试数据导入能力。

## ✨ 主要功能

### 1. 用户管理
- **用户登录验证**
- **管理员登录验证**
- **用户信息获取**

### 2. 数据库管理
- **数据库状态检查**
- **测试用户数据初始化**
- **中药材数据导入**

### 3. 系统工具
- **一键数据导入**
- **数据库连接测试**
- **错误日志记录**

## 🚀 快速开始

### 1. 部署云函数
```bash
# 在微信开发者工具中
右键 cloudfunctions/simpleFunction
选择 "上传并部署：云端安装依赖"
```

### 2. 初始化数据
```javascript
// 调用云函数初始化测试用户
wx.cloud.callFunction({
  name: 'simpleFunction',
  data: {
    type: 'initTestUsers'
  }
})
```

### 3. 验证功能
```javascript
// 测试管理员登录
wx.cloud.callFunction({
  name: 'simpleFunction',
  data: {
    type: 'adminLogin',
    username: 'admin',
    password: 'admin123'
  }
})
```

## 📋 API 接口

### initTestUsers - 初始化测试用户
**功能**：创建管理员和测试用户账号

**调用方式**：
```javascript
wx.cloud.callFunction({
  name: 'simpleFunction',
  data: {
    type: 'initTestUsers'
  }
})
```

**返回结果**：
```javascript
{
  success: true,
  message: '用户数据初始化完成',
  data: {
    admin: '用户名: admin, 密码: admin123',
    test: '用户名: test, 密码: test123'
  }
}
```

### userLogin - 用户登录
**功能**：验证普通用户登录

**调用方式**：
```javascript
wx.cloud.callFunction({
  name: 'simpleFunction',
  data: {
    type: 'userLogin',
    username: 'test',
    password: 'test123'
  }
})
```

**返回结果**：
```javascript
{
  success: true,
  data: {
    id: 'user_id',
    username: 'test',
    nickName: '测试用户',
    avatarUrl: '/images/default-avatar.png',
    role: 'user'
  }
}
```

### adminLogin - 管理员登录
**功能**：验证管理员登录

**调用方式**：
```javascript
wx.cloud.callFunction({
  name: 'simpleFunction',
  data: {
    type: 'adminLogin',
    username: 'admin',
    password: 'admin123'
  }
})
```

**返回结果**：
```javascript
{
  success: true,
  data: {
    id: 'admin_id',
    username: 'admin',
    nickName: '系统管理员',
    avatarUrl: '/images/default-avatar.png',
    role: 'admin'
  }
}
```

### checkDatabase - 检查数据库状态
**功能**：检查数据库连接和数据状态

**调用方式**：
```javascript
wx.cloud.callFunction({
  name: 'simpleFunction',
  data: {
    type: 'checkDatabase'
  }
})
```

**返回结果**：
```javascript
{
  success: true,
  data: {
    hasUsers: true,
    hasAdmin: true,
    totalUsers: 2,
    message: '数据库连接正常'
  }
}
```

### initMedicinesData - 初始化中药材数据
**功能**：导入测试中药材数据

**调用方式**：
```javascript
wx.cloud.callFunction({
  name: 'simpleFunction',
  data: {
    type: 'initMedicinesData'
  }
})
```

**返回结果**：
```javascript
{
  success: true,
  message: '中药材数据导入完成，新增 3 条记录',
  data: {
    importedCount: 3,
    totalCount: 3
  }
}
```

### getUserInfo - 获取用户信息
**功能**：根据用户ID获取详细信息

**调用方式**：
```javascript
wx.cloud.callFunction({
  name: 'simpleFunction',
  data: {
    type: 'getUserInfo',
    userId: 'user_id'
  }
})
```

**返回结果**：
```javascript
{
  success: true,
  data: {
    id: 'user_id',
    username: 'test',
    nickName: '测试用户',
    avatarUrl: '/images/default-avatar.png',
    role: 'user',
    createTime: '2024-03-15T10:00:00.000Z',
    lastLoginTime: '2024-03-15T10:30:00.000Z'
  }
}
```

## 🗄️ 数据结构

### 用户数据 (users 集合)
```javascript
{
  username: String,      // 用户名
  password: String,      // 密码
  nickName: String,      // 昵称
  avatarUrl: String,     // 头像URL
  role: String,          // 角色 (admin/user)
  createTime: Date,      // 创建时间
  lastLoginTime: Date    // 最后登录时间
}
```

### 中药材数据 (medicines 集合)
```javascript
{
  name: String,          // 药材名称
  category: String,      // 分类
  effect: String,        // 功效
  description: String,   // 详细描述
  price: Number,         // 价格(每克)
  stock: Number,         // 库存(克)
  imageUrl: String,      // 图片URL
  viewCount: Number,     // 浏览次数
  createTime: Date,      // 创建时间
  updateTime: Date       // 更新时间
}
```

## 🔧 使用示例

### 完整的登录流程
```javascript
// 1. 检查数据库状态
const checkResult = await wx.cloud.callFunction({
  name: 'simpleFunction',
  data: { type: 'checkDatabase' }
});

// 2. 如果没有数据，初始化
if (!checkResult.result.data.hasUsers) {
  await wx.cloud.callFunction({
    name: 'simpleFunction',
    data: { type: 'initTestUsers' }
  });
}

// 3. 管理员登录
const loginResult = await wx.cloud.callFunction({
  name: 'simpleFunction',
  data: {
    type: 'adminLogin',
    username: 'admin',
    password: 'admin123'
  }
});

if (loginResult.result.success) {
  console.log('登录成功:', loginResult.result.data);
}
```

### 数据导入流程
```javascript
// 1. 初始化用户数据
await wx.cloud.callFunction({
  name: 'simpleFunction',
  data: { type: 'initTestUsers' }
});

// 2. 导入中药材数据
await wx.cloud.callFunction({
  name: 'simpleFunction',
  data: { type: 'initMedicinesData' }
});

console.log('数据导入完成');
```

## ⚠️ 注意事项

### 1. 安全性
- 密码未加密存储（仅用于测试）
- 生产环境需要实现密码加密
- 建议添加登录频率限制

### 2. 性能
- 适用于小规模测试数据
- 大量数据需要分批处理
- 建议添加数据缓存机制

### 3. 扩展性
- 可以轻松添加新的数据类型
- 支持自定义初始化逻辑
- 便于集成到现有系统

## 🐛 错误处理

### 常见错误码
- `数据库连接失败`: 检查云开发环境配置
- `用户名或密码错误`: 确认账号信息
- `权限不足`: 检查用户角色
- `数据已存在`: 重复导入时的正常提示

### 调试建议
1. 查看云函数日志
2. 检查数据库权限设置
3. 确认网络连接状态
4. 验证参数格式正确性

## 📈 性能优化

### 1. 数据库优化
- 为常用查询字段创建索引
- 使用合适的数据类型
- 避免全表扫描

### 2. 云函数优化
- 减少不必要的数据库连接
- 使用连接池
- 实现结果缓存

### 3. 前端优化
- 合并多个请求
- 实现本地缓存
- 添加加载状态提示

---

**提示**：这个简化版云函数专为快速开发和测试设计，生产环境使用时请根据实际需求进行安全性和性能优化。

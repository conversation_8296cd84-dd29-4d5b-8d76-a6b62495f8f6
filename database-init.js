// 数据库初始化脚本
// 在云开发控制台的数据库页面，创建以下集合并导入示例数据

// 1. 创建 users 集合
const users = [
  {
    username: "admin",
    password: "admin123", // 实际项目中应该加密
    nickName: "系统管理员",
    avatarUrl: "/images/default-avatar.png",
    role: "admin",
    createTime: new Date(),
    lastLoginTime: new Date()
  },
  {
    username: "test",
    password: "test123",
    nickName: "测试用户",
    avatarUrl: "/images/default-avatar.png",
    role: "user",
    createTime: new Date(),
    lastLoginTime: new Date()
  }
];

// 2. 创建 medicines 集合
const medicines = [
  {
    name: "人参",
    category: "补气药",
    effect: "大补元气，复脉固脱，补脾益肺，生津止渴，安神益智",
    description: "人参为五加科植物人参的干燥根和根茎。具有大补元气，复脉固脱，补脾益肺，生津止渴，安神益智的功效。主治体虚欲脱，肢冷脉微，脾虚食少，肺虚喘咳，津伤口渴，内热消渴，气血亏虚，久病虚羸，惊悸失眠，阳痿宫冷。",
    price: 12.5,
    stock: 500,
    imageUrl: "/images/medicine1.jpg",
    viewCount: 1250,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: "枸杞子",
    category: "补血药",
    effect: "滋补肝肾，益精明目",
    description: "枸杞子为茄科植物宁夏枸杞的干燥成熟果实。具有滋补肝肾，益精明目的功效。主治肝肾阴亏，腰膝酸软，头晕，目眩，目昏多泪，虚劳咳嗽，消渴，遗精。",
    price: 8.8,
    stock: 1000,
    imageUrl: "/images/medicine2.jpg",
    viewCount: 980,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: "当归",
    category: "补血药",
    effect: "补血活血，调经止痛，润肠通便",
    description: "当归为伞形科植物当归的干燥根。具有补血活血，调经止痛，润肠通便的功效。主治血虚萎黄，眩晕心悸，月经不调，经闭痛经，虚寒腹痛，风湿痹痛，跌扑损伤，痈疽疮疡，肠燥便秘。",
    price: 15.6,
    stock: 300,
    imageUrl: "/images/medicine3.jpg",
    viewCount: 756,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: "黄芪",
    category: "补气药",
    effect: "补气固表，利尿托毒，排脓，敛疮生肌",
    description: "黄芪为豆科植物蒙古黄芪或膜荚黄芪的干燥根。具有补气固表，利尿托毒，排脓，敛疮生肌的功效。",
    price: 9.2,
    stock: 800,
    imageUrl: "/images/medicine4.jpg",
    viewCount: 654,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: "甘草",
    category: "补气药",
    effect: "补脾益气，清热解毒，祛痰止咳，缓急止痛，调和诸药",
    description: "甘草为豆科植物甘草、胀果甘草或光果甘草的干燥根和根茎。具有补脾益气，清热解毒，祛痰止咳，缓急止痛，调和诸药的功效。",
    price: 6.5,
    stock: 1200,
    imageUrl: "/images/medicine5.jpg",
    viewCount: 892,
    createTime: new Date(),
    updateTime: new Date()
  }
];

// 3. 创建 products 集合
const products = [
  {
    name: "中医养生茶具套装",
    category: "茶具",
    description: "精美的紫砂茶具，适合泡制各种养生茶。包含茶壶、茶杯、茶盘等全套用具，工艺精湛，实用美观。",
    price: 168,
    stock: 50,
    sales: 125,
    isHot: true,
    imageUrl: "/images/product1.jpg",
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: "本草纲目典藏版",
    category: "书籍",
    description: "李时珍经典著作，中医药学必读书籍。精装版本，印刷精美，内容详实，是学习中医药知识的权威参考。",
    price: 58,
    stock: 100,
    sales: 89,
    isHot: false,
    imageUrl: "/images/product2.jpg",
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: "艾灸养生套装",
    category: "养生用品",
    description: "传统艾灸工具，居家养生必备。包含艾条、艾灸盒、艾灸垫等，操作简单，效果显著。",
    price: 128,
    stock: 30,
    sales: 67,
    isHot: true,
    imageUrl: "/images/product3.jpg",
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: "中药香薰炉",
    category: "香薰用品",
    description: "精美陶瓷香薰炉，可用于熏香养生。造型古朴典雅，功能实用，是居家养生的好帮手。",
    price: 88,
    stock: 25,
    sales: 43,
    isHot: false,
    imageUrl: "/images/product4.jpg",
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: "中医经络穴位图",
    category: "装饰品",
    description: "精美的中医经络穴位挂图，既可作为学习参考，也可作为装饰品。印刷清晰，内容准确。",
    price: 35,
    stock: 80,
    sales: 156,
    isHot: false,
    imageUrl: "/images/product5.jpg",
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: "养生药膳食谱",
    category: "书籍",
    description: "精选100道养生药膳食谱，图文并茂，制作简单。让您在享受美食的同时，获得健康养生的效果。",
    price: 42,
    stock: 60,
    sales: 78,
    isHot: false,
    imageUrl: "/images/product6.jpg",
    createTime: new Date(),
    updateTime: new Date()
  }
];

// 4. 创建 articles 集合
const articles = [
  {
    title: "春季养生：顺应自然，调养身心",
    category: "春季养生",
    author: "张医师",
    summary: "春季是万物复苏的季节，人体阳气开始升发，此时养生应顺应自然规律，注重调养肝脏，保持心情舒畅，适当运动，合理饮食。",
    content: "春季养生的核心在于顺应自然规律，调养肝脏。春天肝气旺盛，容易出现肝火上升的情况，因此要注意情志调节，保持心情舒畅。在饮食方面，应多食用绿色蔬菜，如菠菜、韭菜、芹菜等，这些食物有助于疏肝理气。同时，春季也是运动的好时节，可以选择散步、太极拳、瑜伽等温和的运动方式，有助于疏通经络，增强体质。此外，春季昼夜温差较大，要注意适时增减衣物，预防感冒。",
    imageUrl: "/images/article1.jpg",
    status: "published",
    views: 1250,
    createTime: "2024-03-15",
    updateTime: new Date()
  },
  {
    title: "中医食疗：药食同源的智慧",
    category: "食疗养生",
    author: "李医师",
    summary: "中医认为'药食同源'，许多食物既是美味佳肴，又具有药用价值。合理搭配食材，可以达到养生保健的效果，这是中医食疗的精髓所在。",
    content: "药食同源是中医学的重要理论之一，认为食物和药物都来源于自然，具有相同的性味归经理论。常见的药食同源食材有：山药健脾益肾、红枣补血养心、枸杞滋肝明目、莲子清心安神等。在日常饮食中，我们可以根据个人体质和季节特点，选择合适的食材进行搭配。比如体质偏寒的人可以多食用温性食物如生姜、桂圆等；体质偏热的人则适合食用凉性食物如绿豆、冬瓜等。通过合理的食疗搭配，不仅能满足营养需求，还能起到调理身体的作用。",
    imageUrl: "/images/article2.jpg",
    status: "published",
    views: 980,
    createTime: "2024-03-12",
    updateTime: new Date()
  },
  {
    title: "四季养生茶饮推荐",
    category: "药膳食谱",
    author: "王医师",
    summary: "不同季节适合饮用不同的养生茶，春饮花茶，夏饮绿茶，秋饮乌龙，冬饮红茶，每种茶都有其独特的养生功效，选对茶饮，健康一整年。",
    content: "茶文化是中华文化的重要组成部分，不同季节饮用不同的茶，既能品味茶香，又能养生保健。春季推荐花茶，如茉莉花茶、玫瑰花茶，有疏肝解郁的功效；夏季适合绿茶，如龙井、碧螺春，能清热解暑；秋季宜饮乌龙茶，如铁观音、大红袍，有润燥生津的作用；冬季则适合红茶，如正山小种、祁门红茶，能温阳散寒。此外，还可以根据个人体质选择不同的养生茶饮，如气虚者可饮黄芪茶，血虚者可饮当归茶，阴虚者可饮枸杞茶等。",
    imageUrl: "/images/article3.jpg",
    status: "published",
    views: 756,
    createTime: "2024-03-10",
    updateTime: new Date()
  },
  {
    title: "中医养生的五脏调理法",
    category: "中医理论",
    author: "赵医师",
    summary: "中医认为五脏六腑是人体的核心，通过调理五脏，可以达到整体健康的目的。本文详细介绍心、肝、脾、肺、肾五脏的调理方法。",
    content: "中医五脏调理是养生的重要方法。心主血脉，调理心脏要保持心情愉悦，可食用红色食物如红枣、山楂等；肝主疏泄，调理肝脏要避免情绪波动，多食绿色食物如菠菜、青椒等；脾主运化，调理脾胃要规律饮食，多食黄色食物如小米、南瓜等；肺主气，调理肺脏要注意呼吸锻炼，多食白色食物如百合、银耳等；肾主藏精，调理肾脏要避免过度劳累，多食黑色食物如黑豆、黑芝麻等。通过五脏调理，可以达到阴阳平衡，气血调和的养生目标。",
    imageUrl: "/images/article4.jpg",
    status: "published",
    views: 892,
    createTime: "2024-03-08",
    updateTime: new Date()
  }
];

// 5. 创建 orders 集合
const orders = [
  {
    id: 'order_1',
    userId: 'user_1',
    status: 'pending_payment',
    items: [
      {
        id: 'item_1',
        productId: 'med_1',
        name: '人参',
        spec: '50g',
        price: 12.5,
        quantity: 2,
        imageUrl: '/images/medicine1.jpg',
        type: 'medicine',
        category: '补气药',
        unit: '/克',
        totalPrice: '25.00'
      }
    ],
    address: {
      id: 'addr_1',
      name: '张三',
      phone: '138****8888',
      province: '北京市',
      city: '北京市',
      district: '朝阳区',
      detail: '三里屯街道某某小区1号楼101室',
      isDefault: true
    },
    delivery: {
      id: 'standard',
      name: '标准配送',
      description: '5-7个工作日送达',
      price: 0
    },
    payment: {
      id: 'wechat',
      name: '微信支付'
    },
    coupon: null,
    remark: '请尽快发货，谢谢！',
    goodsTotal: '25.00',
    deliveryFee: 0,
    couponDiscount: 0,
    totalAmount: '25.00',
    createTime: new Date('2024-03-15 14:30:25'),
    updateTime: new Date(),
    payTime: null,
    shipTime: null,
    completeTime: null,
    cancelTime: null,
    cancelReason: null
  }
];

// 使用说明：
// 1. 在微信开发者工具中打开云开发控制台
// 2. 进入数据库页面
// 3. 分别创建 users、medicines、products、articles、orders 五个集合
// 4. 将上述数据导入对应的集合中
// 5. 注意：实际使用时，图片URL需要替换为真实的图片地址

console.log('数据库初始化脚本');
console.log('请在云开发控制台中创建集合并导入数据');

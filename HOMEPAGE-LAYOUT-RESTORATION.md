# 首页布局恢复指南

## 🎯 目标

恢复首页的完整布局情况，并将图片文件命名格式转化为中文含义，提升项目的可维护性和用户体验。

## 📋 当前首页布局结构

### 1. 顶部区域
```xml
<!-- 头部导航 -->
<view class="header">
  <image class="logo" src="/images/品牌标识/慧心制药-logo.png"></image>
  <view class="search-bar">
    <input placeholder="搜索中药材、产品..." />
    <image class="search-icon" src="/images/界面图标/图标-搜索.png"></image>
  </view>
</view>
```

### 2. 轮播图区域
```xml
<!-- 轮播图 -->
<swiper class="banner-swiper">
  <swiper-item wx:for="{{bannerList}}" wx:key="id">
    <image src="{{item.imageUrl}}" mode="aspectFill"></image>
    <view class="banner-content">
      <text class="banner-title">{{item.title}}</text>
      <text class="banner-desc">{{item.description}}</text>
    </view>
  </swiper-item>
</swiper>
```

### 3. 快捷导航区域
```xml
<!-- 快捷导航 -->
<view class="nav-grid">
  <view class="nav-item" bindtap="goToMedicines">
    <image src="/images/界面图标/图标-中药材.png"></image>
    <text>中药材</text>
  </view>
  <view class="nav-item" bindtap="goToProducts">
    <image src="/images/界面图标/图标-文创产品.png"></image>
    <text>文创产品</text>
  </view>
  <view class="nav-item" bindtap="goToArticles">
    <image src="/images/界面图标/图标-养生文章.png"></image>
    <text>养生文章</text>
  </view>
  <view class="nav-item" bindtap="goToCart">
    <image src="/images/界面图标/图标-购物车.png"></image>
    <text>购物车</text>
  </view>
</view>
```

### 4. 统计数据区域
```xml
<!-- 数据统计 -->
<view class="stats-section">
  <view class="stats-item">
    <text class="stats-number">{{stats.medicineCount}}</text>
    <text class="stats-label">中药材品种</text>
  </view>
  <view class="stats-item">
    <text class="stats-number">{{stats.productCount}}</text>
    <text class="stats-label">文创产品</text>
  </view>
  <view class="stats-item">
    <text class="stats-number">{{stats.articleCount}}</text>
    <text class="stats-label">养生文章</text>
  </view>
  <view class="stats-item">
    <text class="stats-number">{{stats.userCount}}</text>
    <text class="stats-label">注册用户</text>
  </view>
</view>
```

### 5. 推荐内容区域
```xml
<!-- 推荐中药材 -->
<view class="recommend-section">
  <view class="section-header">
    <text class="section-title">推荐中药材</text>
    <text class="section-more" bindtap="goToMedicines">查看更多</text>
  </view>
  <scroll-view class="recommend-scroll" scroll-x>
    <view class="recommend-item" wx:for="{{recommendMedicines}}" wx:key="id">
      <image src="{{item.imageUrl}}" mode="aspectFill"></image>
      <text class="item-name">{{item.name}}</text>
      <text class="item-price">¥{{item.price}}/克</text>
    </view>
  </scroll-view>
</view>

<!-- 推荐文创产品 -->
<view class="recommend-section">
  <view class="section-header">
    <text class="section-title">文创精品</text>
    <text class="section-more" bindtap="goToProducts">查看更多</text>
  </view>
  <scroll-view class="recommend-scroll" scroll-x>
    <view class="recommend-item" wx:for="{{recommendProducts}}" wx:key="id">
      <image src="{{item.imageUrl}}" mode="aspectFill"></image>
      <text class="item-name">{{item.name}}</text>
      <text class="item-price">¥{{item.price}}</text>
    </view>
  </scroll-view>
</view>

<!-- 推荐养生文章 -->
<view class="recommend-section">
  <view class="section-header">
    <text class="section-title">养生资讯</text>
    <text class="section-more" bindtap="goToArticles">查看更多</text>
  </view>
  <view class="article-list">
    <view class="article-item" wx:for="{{recommendArticles}}" wx:key="id">
      <image src="{{item.imageUrl}}" mode="aspectFill"></image>
      <view class="article-content">
        <text class="article-title">{{item.title}}</text>
        <text class="article-summary">{{item.summary}}</text>
        <view class="article-meta">
          <text class="article-author">{{item.author}}</text>
          <text class="article-views">{{item.views}}阅读</text>
        </view>
      </view>
    </view>
  </view>
</view>
```

## 🖼️ 图片文件命名转换

### 已完成的转换

#### 数据库图片路径更新
```javascript
// 中药材图片路径
"/images/中药材/中药材-人参.jpg"
"/images/中药材/中药材-枸杞子.jpg"
"/images/中药材/中药材-当归.jpg"
"/images/中药材/中药材-黄芪.jpg"
"/images/中药材/中药材-甘草.jpg"

// 文创产品图片路径
"/images/文创产品/文创-茶具套装.jpg"
"/images/文创产品/文创-本草纲目.jpg"
"/images/文创产品/文创-艾灸套装.jpg"
"/images/文创产品/文创-香薰炉.jpg"
"/images/文创产品/文创-穴位图.jpg"
"/images/文创产品/文创-药膳食谱.jpg"

// 养生文章图片路径
"/images/养生文章/文章-春季养生.jpg"
"/images/养生文章/文章-食疗智慧.jpg"
"/images/养生文章/文章-茶饮推荐.jpg"
"/images/养生文章/文章-五脏调理.jpg"
```

#### 图片占位符工具更新
```javascript
// 支持中文路径检测
const isLocalImage = (url) => {
  return url && (
    url.startsWith('/images/') || 
    url.includes('/images/中药材/') ||
    url.includes('/images/文创产品/') ||
    url.includes('/images/养生文章/') ||
    url.includes('/images/界面图标/') ||
    url.includes('/images/轮播图/')
  );
};
```

## 📁 需要创建的图片目录结构

```
miniprogram/images/
├── 品牌标识/
│   ├── 慧心制药-logo.png              # 主logo (200x60px)
│   ├── 慧心制药-logo-白色.png          # 白色版本logo
│   └── 慧心制药-图标.png              # 小图标 (64x64px)
├── 轮播图/
│   ├── 轮播图-春季养生.jpg            # 750x300px
│   ├── 轮播图-中医文化.jpg            # 750x300px
│   ├── 轮播图-产品推荐.jpg            # 750x300px
│   └── 轮播图-健康生活.jpg            # 750x300px
├── 中药材/
│   ├── 中药材-人参.jpg               # 400x400px
│   ├── 中药材-枸杞子.jpg             # 400x400px
│   ├── 中药材-当归.jpg               # 400x400px
│   ├── 中药材-黄芪.jpg               # 400x400px
│   ├── 中药材-甘草.jpg               # 400x400px
│   └── 中药材-占位符.png             # 通用占位符
├── 文创产品/
│   ├── 文创-茶具套装.jpg             # 400x400px
│   ├── 文创-本草纲目.jpg             # 400x400px
│   ├── 文创-艾灸套装.jpg             # 400x400px
│   ├── 文创-香薰炉.jpg               # 400x400px
│   ├── 文创-穴位图.jpg               # 400x400px
│   ├── 文创-药膳食谱.jpg             # 400x400px
│   └── 文创-占位符.png               # 通用占位符
├── 养生文章/
│   ├── 文章-春季养生.jpg             # 600x400px
│   ├── 文章-食疗智慧.jpg             # 600x400px
│   ├── 文章-茶饮推荐.jpg             # 600x400px
│   ├── 文章-五脏调理.jpg             # 600x400px
│   └── 文章-占位符.png               # 通用占位符
├── 界面图标/
│   ├── 图标-中药材.png               # 64x64px
│   ├── 图标-文创产品.png             # 64x64px
│   ├── 图标-养生文章.png             # 64x64px
│   ├── 图标-购物车.png               # 64x64px
│   ├── 图标-个人中心.png             # 64x64px
│   ├── 图标-设置.png                 # 64x64px
│   ├── 图标-搜索.png                 # 32x32px
│   ├── 图标-收藏.png                 # 32x32px
│   ├── 图标-分享.png                 # 32x32px
│   └── 图标-返回.png                 # 32x32px
├── 空状态图片/
│   ├── 空状态-中药材.png             # 200x200px
│   ├── 空状态-文创产品.png           # 200x200px
│   ├── 空状态-养生文章.png           # 200x200px
│   ├── 空状态-购物车.png             # 200x200px
│   ├── 空状态-搜索结果.png           # 200x200px
│   └── 空状态-网络错误.png           # 200x200px
├── 用户头像/
│   ├── 头像-默认.png                 # 80x80px
│   ├── 头像-管理员.png               # 80x80px
│   └── 头像-占位符.png               # 80x80px
└── 背景图片/
    ├── 背景-登录页面.jpg             # 750x1334px
    ├── 背景-中医风格.jpg             # 750x1334px
    └── 背景-纹理.jpg                 # 重复纹理
```

## 🛠️ 实施步骤

### 第一步：准备图片资源
1. **收集或设计图片**：
   - 中药材实物图片
   - 文创产品展示图
   - 养生文章配图
   - 界面图标设计

2. **图片规格要求**：
   - 格式：JPG（照片）、PNG（图标、透明背景）
   - 质量：高清，适合移动端显示
   - 尺寸：按照上述规格准备

### 第二步：创建目录结构
```bash
# 在项目根目录执行
mkdir -p miniprogram/images/品牌标识
mkdir -p miniprogram/images/轮播图
mkdir -p miniprogram/images/中药材
mkdir -p miniprogram/images/文创产品
mkdir -p miniprogram/images/养生文章
mkdir -p miniprogram/images/界面图标
mkdir -p miniprogram/images/空状态图片
mkdir -p miniprogram/images/用户头像
mkdir -p miniprogram/images/背景图片
```

### 第三步：放置图片文件
按照命名规范将图片文件放置到对应目录中。

### 第四步：重新导入数据库
1. **删除现有数据**：
   ```bash
   云开发控制台 → 数据库 → 删除集合中的记录
   ```

2. **导入新数据**：
   ```bash
   使用更新后的 JSON Lines 文件
   medicines.json, products.json, articles.json
   ```

### 第五步：测试验证
1. **重新编译小程序**
2. **检查首页布局**：
   - 轮播图显示正常
   - 推荐内容图片正常
   - 导航图标正常
3. **验证图片加载**：
   - 真实图片正常显示
   - 缺失图片显示占位符

## 🎨 设计建议

### 中医风格设计元素
- **色彩搭配**：棕色、绿色、金色为主
- **字体选择**：优雅的中文字体
- **图案元素**：传统中医图案、植物纹样
- **布局风格**：简洁大方，突出内容

### 图片设计要求
- **中药材图片**：清晰的产品图，统一背景
- **文创产品图片**：展示使用场景，突出质感
- **文章配图**：温馨的生活场景，符合主题
- **图标设计**：简洁明了，风格统一

## 📱 响应式适配

### 不同屏幕尺寸适配
```css
/* 小屏幕适配 */
@media (max-width: 375px) {
  .recommend-item {
    width: 120px;
  }
}

/* 大屏幕适配 */
@media (min-width: 414px) {
  .recommend-item {
    width: 150px;
  }
}
```

### 图片加载优化
```javascript
// 图片懒加载
<image 
  src="{{item.imageUrl}}" 
  mode="aspectFill"
  lazy-load="{{true}}"
  bindload="onImageLoad"
  binderror="onImageError"
></image>
```

## ✅ 完成检查清单

### 图片资源
- [ ] 品牌logo已准备
- [ ] 轮播图已设计
- [ ] 中药材图片已收集
- [ ] 文创产品图片已准备
- [ ] 养生文章配图已设计
- [ ] 界面图标已制作
- [ ] 空状态图片已准备

### 技术实现
- [ ] 目录结构已创建
- [ ] 图片文件已放置
- [ ] 数据库路径已更新
- [ ] 占位符工具已优化
- [ ] 首页代码已适配

### 功能验证
- [ ] 首页布局正常
- [ ] 图片加载正常
- [ ] 占位符机制正常
- [ ] 响应式适配正常
- [ ] 用户体验良好

---

**优势**: 中文命名更直观，便于团队协作和项目维护
**效果**: 提升用户体验，增强品牌形象，便于后期扩展

# 慧心制药 - 微信小程序

一个基于微信云开发的中医药文化传播小程序，集中药材介绍、文创产品展示、养生科普于一体。

## 项目特色

- 🌿 **中药主题设计** - 采用中药棕色、绿色为主的配色方案
- 📱 **响应式布局** - 适配不同尺寸的移动设备
- 👨‍💼 **角色权限管理** - 支持管理员和普通用户不同权限
- 🔄 **完整CRUD功能** - 中药材、文创产品、养生文章的增删查改
- 📊 **数据可视化** - 首页展示统计图表和数据概览
- 🖼️ **图片上传** - 支持云存储图片上传功能
- 🔍 **搜索筛选** - 多维度搜索和分类筛选

## 技术栈

- **前端框架**: 微信小程序原生开发
- **后端服务**: 微信云开发
- **数据库**: 云数据库
- **存储**: 云存储
- **云函数**: Node.js

## 项目结构

```
├── cloudfunctions/          # 云函数
│   └── quickstartFunctions/ # 主要业务云函数
├── miniprogram/            # 小程序前端代码
│   ├── pages/              # 页面文件
│   │   ├── index/          # 首页
│   │   ├── login/          # 登录页
│   │   ├── admin/          # 管理后台
│   │   ├── medicines/      # 中药材页面
│   │   ├── products/       # 文创产品页面
│   │   ├── articles/       # 养生文章页面
│   │   └── profile/        # 个人中心
│   ├── images/             # 图片资源
│   ├── app.js              # 小程序入口文件
│   ├── app.json            # 小程序配置文件
│   └── app.wxss            # 全局样式文件
└── project.config.json     # 项目配置文件
```

## 功能模块

### 1. 用户系统
- 微信登录
- 游客模式
- 管理员登录
- 用户权限管理

### 2. 首页展示
- 轮播图展示
- 数据统计概览
- 推荐内容展示
- 图表数据可视化

### 3. 中药材管理
- 中药材列表展示
- 分类筛选
- 搜索功能
- 管理员增删改功能

### 4. 文创产品管理
- 产品网格展示
- 分类筛选
- 热销标识
- 管理员增删改功能

### 5. 养生文章管理
- 文章列表展示
- 分类筛选
- 发布状态管理
- 管理员增删改功能

### 6. 购物车系统
- 商品加入购物车
- 购物车商品管理
- 数量调整和删除
- 规格选择
- 推荐商品

### 7. 订单系统
- 订单结算
- 收货地址管理
- 配送方式选择
- 支付方式选择
- 优惠券使用

### 8. 订单管理
- 订单状态跟踪
- 订单详情查看
- 订单操作（取消、确认收货等）
- 物流信息查询
- 订单历史记录

### 9. 管理后台
- 数据统计
- 批量操作
- 系统工具
- 快速操作

## 数据库设计

### users 用户表
```javascript
{
  _id: String,           // 用户ID
  openid: String,        // 微信openid
  username: String,      // 用户名
  password: String,      // 密码
  nickName: String,      // 昵称
  avatarUrl: String,     // 头像
  role: String,          // 角色 (admin/user/guest)
  createTime: Date,      // 创建时间
  lastLoginTime: Date    // 最后登录时间
}
```

### medicines 中药材表
```javascript
{
  _id: String,           // 药材ID
  name: String,          // 药材名称
  category: String,      // 分类
  effect: String,        // 主要功效
  description: String,   // 详细描述
  price: Number,         // 价格(元/克)
  stock: Number,         // 库存(克)
  imageUrl: String,      // 图片URL
  viewCount: Number,     // 浏览次数
  createTime: Date,      // 创建时间
  updateTime: Date       // 更新时间
}
```

### products 文创产品表
```javascript
{
  _id: String,           // 产品ID
  name: String,          // 产品名称
  category: String,      // 分类
  description: String,   // 产品描述
  price: Number,         // 价格(元)
  stock: Number,         // 库存
  sales: Number,         // 销量
  isHot: Boolean,        // 是否热销
  imageUrl: String,      // 图片URL
  createTime: Date,      // 创建时间
  updateTime: Date       // 更新时间
}
```

### articles 养生文章表
```javascript
{
  _id: String,           // 文章ID
  title: String,         // 文章标题
  category: String,      // 分类
  author: String,        // 作者
  summary: String,       // 文章摘要
  content: String,       // 文章内容
  imageUrl: String,      // 封面图片
  status: String,        // 状态 (published/draft)
  views: Number,         // 阅读量
  createTime: String,    // 创建时间
  updateTime: Date       // 更新时间
}
```

### orders 订单表
```javascript
{
  _id: String,           // 订单ID
  userId: String,        // 用户ID
  status: String,        // 订单状态 (pending_payment/paid/shipped/completed/cancelled)
  items: Array,          // 商品列表
  address: Object,       // 收货地址
  delivery: Object,      // 配送方式
  payment: Object,       // 支付方式
  coupon: Object,        // 优惠券信息
  remark: String,        // 订单备注
  goodsTotal: String,    // 商品总价
  deliveryFee: Number,   // 运费
  couponDiscount: Number,// 优惠券折扣
  totalAmount: String,   // 实付金额
  createTime: Date,      // 创建时间
  payTime: Date,         // 付款时间
  shipTime: Date,        // 发货时间
  completeTime: Date,    // 完成时间
  cancelTime: Date,      // 取消时间
  cancelReason: String,  // 取消原因
  updateTime: Date       // 更新时间
}
```

## 安装和运行

### 1. 环境准备
- 安装微信开发者工具
- 注册微信小程序账号
- 开通微信云开发服务

### 2. 项目配置
1. 在微信开发者工具中导入项目
2. 修改 `project.config.json` 中的 `appid`
3. 在 `miniprogram/app.js` 中配置云开发环境ID

### 3. 云函数部署
1. 右键 `cloudfunctions/quickstartFunctions` 目录
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

### 4. 数据库初始化
1. 在云开发控制台创建以下集合：
   - users
   - medicines
   - products
   - articles
   - orders
2. 可选：导入示例数据

### 5. 运行项目
1. 点击"编译"按钮
2. 在模拟器中预览效果

## 默认账号

### 管理员账号
- 用户名: admin
- 密码: admin123

### 测试用户
- 用户名: test
- 密码: test123

## 开发说明

### 样式规范
- 主色调：#8B4513 (中药棕色)
- 辅助色：#228B22 (中药绿色)
- 背景色：#F5F2E8 (米色)
- 使用rpx单位适配不同屏幕

### 组件规范
- 统一使用card样式的卡片布局
- 按钮使用btn系列样式类
- 图片使用统一的圆角和阴影

### 数据交互
- 所有数据操作通过云函数进行
- 统一的错误处理和加载提示
- 支持分页加载和搜索筛选

## 扩展功能

可以考虑添加的功能：
- 用户收藏功能
- 浏览历史记录
- 评论和评分系统
- 在线购买功能
- 推送通知
- 分享功能
- 地图定位
- 音视频内容

## 联系方式

如有问题或建议，请联系开发团队。

## 许可证

本项目仅供学习和参考使用。


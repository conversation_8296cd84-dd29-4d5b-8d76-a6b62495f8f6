# 慧心制药小程序部署指南

本文档详细介绍如何部署"慧心制药"小程序的简化版云函数和导入测试数据。

## 🚀 快速部署步骤

### 1. 环境准备

#### 必需工具
- **微信开发者工具** (最新版本)
- **微信小程序账号** (已认证)
- **云开发环境** (已开通)

#### 检查清单
- [ ] 微信开发者工具已安装
- [ ] 小程序项目已创建
- [ ] 云开发已开通
- [ ] 项目代码已下载

### 2. 云函数部署

#### 部署简化版云函数
1. **打开微信开发者工具**
2. **导入项目代码**
3. **部署云函数**：
   ```
   右键点击 cloudfunctions/simpleFunction
   选择 "上传并部署：云端安装依赖"
   等待部署完成
   ```

#### 验证部署
- 在云开发控制台查看函数列表
- 确认 `simpleFunction` 状态为"部署成功"

### 3. 数据库初始化

#### 方法1：使用数据导入工具（推荐）
1. **管理员登录**：
   - 在小程序中点击"管理员登录"
   - 如果提示数据库未初始化，点击"确定"进行初始化
   - 使用账号：`admin` / `admin123`

2. **使用数据导入工具**：
   - 登录后进入个人中心
   - 点击"数据导入工具"
   - 点击"一键导入所有数据"
   - 等待导入完成

#### 方法2：手动创建（备用）
1. **打开云开发控制台**
2. **创建数据库集合**：
   - 创建 `users` 集合
   - 创建 `medicines` 集合
   - 创建 `products` 集合
   - 创建 `articles` 集合

3. **导入用户数据**：
   ```json
   // 在 users 集合中添加
   {
     "username": "admin",
     "password": "admin123",
     "nickName": "系统管理员",
     "avatarUrl": "/images/default-avatar.png",
     "role": "admin",
     "createTime": "2024-03-15T10:00:00.000Z",
     "lastLoginTime": "2024-03-15T10:00:00.000Z"
   }
   ```

### 4. 功能验证

#### 基础功能测试
- [ ] 用户登录功能
- [ ] 管理员登录功能
- [ ] 数据导入工具
- [ ] 购物车功能
- [ ] 订单系统

#### 测试账号
```
管理员账号：
用户名：admin
密码：admin123

测试用户：
用户名：test
密码：test123
```

## 📋 详细部署流程

### 步骤1：项目配置

#### 1.1 云开发环境配置
```javascript
// 在 app.js 中确认云开发环境ID
wx.cloud.init({
  env: 'cloud1-8gj5z3ju8fc199f3', // 替换为你的环境ID
  traceUser: true
});
```

#### 1.2 项目配置文件
确认 `project.config.json` 中的配置：
```json
{
  "cloudfunctionRoot": "cloudfunctions/",
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "preloadBackgroundData": false,
    "minified": true,
    "newFeature": false,
    "coverView": true,
    "nodeModules": false,
    "autoAudits": false,
    "showShadowRootInWxmlPanel": true,
    "scopeDataCheck": false,
    "uglifyFileName": false,
    "checkInvalidKey": true,
    "checkSiteMap": true,
    "uploadWithSourceMap": true,
    "compileHotReLoad": false,
    "lazyloadPlaceholderEnable": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "useApiHostProcess": true,
    "babelSetting": {
      "ignore": [],
      "disablePlugins": [],
      "outputPath": ""
    },
    "enableEngineNative": false,
    "useIsolateContext": true,
    "userConfirmedBundleSwitch": false,
    "packNpmManually": false,
    "packNpmRelationList": [],
    "minifyWXSS": true,
    "disableUseStrict": false,
    "minifyWXML": true,
    "showES6CompileOption": false,
    "useCompilerPlugins": false
  }
}
```

### 步骤2：云函数详细部署

#### 2.1 检查云函数代码
确认 `cloudfunctions/simpleFunction/index.js` 包含以下功能：
- 用户登录验证
- 管理员登录验证
- 数据库状态检查
- 测试数据初始化

#### 2.2 部署命令
```bash
# 在微信开发者工具中
右键 cloudfunctions/simpleFunction
选择 "上传并部署：云端安装依赖"
```

#### 2.3 验证部署
1. 查看云开发控制台
2. 确认函数状态为"部署成功"
3. 查看函数日志确认无错误

### 步骤3：数据库权限配置

#### 3.1 设置数据库权限
在云开发控制台的数据库权限设置中：
```json
{
  "read": true,
  "write": "auth != null"
}
```

#### 3.2 创建索引（可选）
为提高查询性能，可以创建以下索引：
- `users` 集合：`username` 字段
- `medicines` 集合：`name` 字段
- `products` 集合：`name` 字段

### 步骤4：测试和验证

#### 4.1 功能测试清单
- [ ] 小程序启动正常
- [ ] 云函数调用成功
- [ ] 数据库连接正常
- [ ] 用户登录功能
- [ ] 数据导入功能
- [ ] 购物车功能
- [ ] 订单管理功能

#### 4.2 性能测试
- 页面加载速度
- 云函数响应时间
- 数据库查询性能

## 🔧 常见问题解决

### 问题1：云函数部署失败
**解决方案**：
1. 检查网络连接
2. 确认云开发环境已开通
3. 重新上传并部署

### 问题2：数据库连接失败
**解决方案**：
1. 检查云开发环境ID
2. 确认数据库权限设置
3. 查看云函数日志

### 问题3：管理员登录失败
**解决方案**：
1. 使用数据导入工具初始化
2. 手动创建管理员账号
3. 检查用户名密码

### 问题4：数据导入失败
**解决方案**：
1. 检查云函数部署状态
2. 确认数据库权限
3. 查看详细错误日志

## 📊 部署检查清单

### 环境检查
- [ ] 微信开发者工具版本 ≥ 1.06.0
- [ ] 云开发环境已开通
- [ ] 项目AppID已配置

### 代码检查
- [ ] 云函数代码完整
- [ ] 环境ID配置正确
- [ ] 页面路径配置正确

### 功能检查
- [ ] 云函数部署成功
- [ ] 数据库集合已创建
- [ ] 管理员账号可登录
- [ ] 数据导入工具可用

### 性能检查
- [ ] 页面加载速度 < 3秒
- [ ] 云函数响应时间 < 2秒
- [ ] 数据库查询速度正常

## 🎯 部署后操作

### 1. 数据初始化
使用数据导入工具导入以下数据：
- 用户数据（管理员和测试用户）
- 中药材数据（3种常用中药材）
- 文创产品数据（6种产品）
- 养生文章数据（4篇文章）

### 2. 功能配置
- 配置支付功能（如需要）
- 设置消息推送（如需要）
- 配置分享功能

### 3. 性能优化
- 启用CDN加速
- 配置图片压缩
- 优化数据库查询

## 📞 技术支持

如果在部署过程中遇到问题，请提供以下信息：
1. 错误截图和日志
2. 微信开发者工具版本
3. 云开发环境信息
4. 具体操作步骤

---

**提示**：建议在正式部署前先在测试环境中完整测试所有功能。

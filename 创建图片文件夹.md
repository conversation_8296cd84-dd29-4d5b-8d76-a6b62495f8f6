# 创建图片文件夹结构指南

## 📁 在微信开发者工具中创建文件夹

### 1. 打开微信开发者工具
- 确保项目已打开
- 在左侧文件树中找到 `miniprogram/images/` 文件夹

### 2. 创建子文件夹
右键点击 `images` 文件夹，选择"新建文件夹"，按以下顺序创建：

```
images/
├── 轮播图/          # 新建文件夹
├── 中药材/          # 新建文件夹  
├── 文创产品/        # 新建文件夹
├── 养生文章/        # 新建文件夹
├── 用户头像/        # 新建文件夹
└── 系统图标/        # 新建文件夹
```

### 3. 添加图片文件
将准备好的图片文件拖拽到对应的文件夹中，或右键文件夹选择"导入文件"。

## 🖼️ 图片文件命名规范

### 轮播图文件夹
```
轮播图-传承中医.jpg
轮播图-精选药材.jpg  
轮播图-养生文化.jpg
```

### 中药材文件夹
```
中药材-人参.jpg
中药材-枸杞子.jpg
中药材-当归.jpg
中药材-黄芪.jpg
中药材-甘草.jpg
```

### 文创产品文件夹
```
文创-茶具套装.jpg
文创-本草纲目.jpg
文创-艾灸套装.jpg
文创-香囊.jpg
文创-养生书籍.jpg
```

### 养生文章文件夹
```
文章-春季养生.jpg
文章-食疗智慧.jpg
文章-茶饮推荐.jpg
文章-中药泡脚.jpg
```

### 用户头像文件夹
```
头像-管理员.png
头像-默认.png
头像-游客.png
```

### 系统图标文件夹
```
logo.png
empty-cart.png
wechat-icon.png
```

## 🎯 快速测试方法

### 1. 编译项目
在微信开发者工具中点击"编译"按钮

### 2. 以管理员身份登录
- 使用账号：admin
- 密码：admin123

### 3. 查看布局预览
- 登录后在首页会看到"查看布局预览"按钮
- 点击按钮查看详细的布局和图片路径说明

### 4. 检查图片显示
- 返回首页查看实际效果
- 如果图片不显示，检查文件路径和文件名是否正确

## ⚠️ 注意事项

### 文件命名
- 使用中文命名，便于识别
- 避免特殊字符和空格
- 保持命名的一致性

### 图片格式
- 轮播图、中药材、文创产品、文章：JPG格式
- Logo、图标、头像：PNG格式（支持透明背景）

### 图片尺寸
- 轮播图：750x400px
- 中药材/文创产品：300x300px  
- 文章配图：240x240px
- Logo：80x80px
- 头像：100x100px

### 文件大小
- 单个图片建议不超过500KB
- 可使用图片压缩工具优化文件大小

## 🔧 故障排除

### 图片不显示
1. 检查文件路径是否正确
2. 确认文件名拼写无误
3. 验证图片格式是否支持
4. 重新编译项目

### 图片模糊
1. 检查原图分辨率是否足够
2. 确认图片压缩质量设置
3. 考虑使用2倍图（@2x）

### 加载缓慢
1. 优化图片文件大小
2. 使用适当的图片格式
3. 考虑使用CDN加速

## 📱 预览效果

完成图片添加后，您可以：
1. 在模拟器中预览效果
2. 使用真机调试查看实际显示
3. 通过布局预览页面检查所有图片位置

## 🚀 下一步

图片添加完成后，建议：
1. 测试所有页面的图片显示
2. 检查不同设备上的显示效果  
3. 优化图片加载性能
4. 准备发布版本

// 简化版云函数 - 专门用于数据初始化和基本功能
const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

// 获取数据库引用
const db = cloud.database();

// 初始化测试用户数据
const initTestUsers = async () => {
  try {
    console.log('开始初始化测试用户数据...');

    // 直接尝试插入数据，如果集合不存在会自动创建
    console.log('尝试直接创建用户数据...');

    let adminCreated = false;
    let testCreated = false;

    // 直接尝试创建管理员账号（如果已存在会跳过）
    try {
      console.log('尝试创建管理员账号...');

      // 先尝试查询，如果失败说明集合不存在，直接创建
      let existingAdmin = { data: [] };
      try {
        existingAdmin = await db.collection('users').where({
          username: 'admin'
        }).get();
        console.log('查询到现有管理员账号:', existingAdmin.data.length, '个');
      } catch (queryError) {
        console.log('集合可能不存在，将创建新的管理员账号');
      }

      if (existingAdmin.data.length === 0) {
        // 创建管理员账号 - 这会自动创建 users 集合
        console.log('开始创建管理员账号...');
        const adminResult = await db.collection('users').add({
          data: {
            username: "admin",
            password: "admin123",
            nickName: "系统管理员",
            avatarUrl: "/images/用户头像/头像-管理员.png",
            role: "admin",
            createTime: new Date(),
            lastLoginTime: new Date()
          }
        });
        console.log('管理员账号创建成功:', adminResult._id);
        adminCreated = true;
      } else {
        console.log('管理员账号已存在，跳过创建');
      }
    } catch (adminError) {
      console.error('创建管理员账号失败:', adminError);
      // 不抛出错误，继续尝试其他方式
      console.log('将尝试其他方式创建管理员账号...');
    }

    // 检查是否已有测试用户
    try {
      let existingTest;
      try {
        existingTest = await db.collection('users').where({
          username: 'test'
        }).get();
        console.log('现有测试用户:', existingTest.data);
      } catch (queryError) {
        console.log('查询测试用户失败:', queryError.message);
        existingTest = { data: [] }; // 假设不存在
      }

      if (existingTest.data.length === 0) {
        // 创建测试用户
        const testResult = await db.collection('users').add({
          data: {
            username: "test",
            password: "test123",
            nickName: "测试用户",
            avatarUrl: "/images/用户头像/头像-默认.png",
            role: "user",
            createTime: new Date(),
            lastLoginTime: new Date()
          }
        });
        console.log('测试用户创建成功:', testResult);
        testCreated = true;
      } else {
        console.log('测试用户已存在');
      }
    } catch (testError) {
      console.error('处理测试用户时出错:', testError);
      // 测试用户创建失败不影响整体流程
    }

    // 验证创建结果
    let finalCheck;
    try {
      finalCheck = await db.collection('users').get();
      console.log('最终用户数据:', finalCheck.data);
    } catch (finalError) {
      console.log('最终验证失败，但初始化可能已成功:', finalError.message);
      finalCheck = { data: [] };
    }

    return {
      success: true,
      message: `用户数据初始化完成。管理员${adminCreated ? '已创建' : '已存在'}，测试用户${testCreated ? '已创建' : '已存在'}`,
      data: {
        admin: '用户名: admin, 密码: admin123',
        test: '用户名: test, 密码: test123',
        adminCreated,
        testCreated,
        totalUsers: finalCheck.data.length,
        instructions: '现在可以使用以下账号登录：\n1. 管理员：admin / admin123\n2. 测试用户：test / test123'
      }
    };

  } catch (error) {
    console.error('初始化用户数据失败:', error);
    return {
      success: false,
      message: '初始化失败: ' + error.message,
      error: error.toString(),
      stack: error.stack
    };
  }
};

// 用户登录验证
const userLogin = async (event) => {
  try {
    const { username, password } = event;
    
    // 查询用户
    const userResult = await db.collection('users').where({
      username: username,
      password: password
    }).get();
    
    if (userResult.data.length > 0) {
      const user = userResult.data[0];
      
      // 更新最后登录时间
      await db.collection('users').doc(user._id).update({
        data: {
          lastLoginTime: new Date()
        }
      });
      
      return {
        success: true,
        data: {
          id: user._id,
          username: user.username,
          nickName: user.nickName,
          avatarUrl: user.avatarUrl,
          role: user.role
        }
      };
    } else {
      return {
        success: false,
        message: '用户名或密码错误'
      };
    }
  } catch (error) {
    console.error('用户登录失败:', error);
    return {
      success: false,
      message: '登录失败: ' + error.message
    };
  }
};

// 管理员登录验证
const adminLogin = async (event) => {
  try {
    const { username, password } = event;
    
    // 查询管理员用户
    const adminResult = await db.collection('users').where({
      username: username,
      password: password,
      role: 'admin'
    }).get();
    
    if (adminResult.data.length > 0) {
      const admin = adminResult.data[0];
      
      // 更新最后登录时间
      await db.collection('users').doc(admin._id).update({
        data: {
          lastLoginTime: new Date()
        }
      });
      
      return {
        success: true,
        data: {
          id: admin._id,
          username: admin.username,
          nickName: admin.nickName,
          avatarUrl: admin.avatarUrl,
          role: 'admin'
        }
      };
    } else {
      return {
        success: false,
        message: '管理员账号或密码错误'
      };
    }
  } catch (error) {
    console.error('管理员登录失败:', error);
    return {
      success: false,
      message: '管理员登录失败: ' + error.message
    };
  }
};

// 检查数据库状态
const checkDatabase = async () => {
  try {
    console.log('开始检查数据库状态...');

    let hasUsersCollection = false;
    let collections = [];

    // 检查集合是否存在
    try {
      const collectionsResult = await db.listCollections();
      collections = collectionsResult.collections.map(col => col.name);
      console.log('现有集合:', collections);
      hasUsersCollection = collections.includes('users');
    } catch (listError) {
      console.log('无法获取集合列表，可能是权限问题:', listError.message);
      // 尝试直接查询 users 集合来判断是否存在
      try {
        await db.collection('users').limit(1).get();
        hasUsersCollection = true;
        console.log('通过查询确认 users 集合存在');
      } catch (queryError) {
        console.log('users 集合不存在或无法访问');
        hasUsersCollection = false;
      }
    }

    if (!hasUsersCollection) {
      console.log('users 集合不存在');
      return {
        success: true,
        data: {
          hasUsers: false,
          hasAdmin: false,
          totalUsers: 0,
          adminCount: 0,
          message: '数据库连接正常，但 users 集合不存在',
          collections: collections
        }
      };
    }

    // 检查 users 集合中的数据
    let usersResult, adminResult;
    try {
      usersResult = await db.collection('users').get();
      console.log('用户数据:', usersResult.data);

      // 检查管理员账号
      adminResult = await db.collection('users').where({
        role: 'admin'
      }).get();
      console.log('管理员数据:', adminResult.data);
    } catch (queryError) {
      console.error('查询用户数据失败:', queryError);
      return {
        success: false,
        message: '查询用户数据失败: ' + queryError.message,
        error: queryError.toString()
      };
    }

    return {
      success: true,
      data: {
        hasUsers: usersResult.data.length > 0,
        hasAdmin: adminResult.data.length > 0,
        totalUsers: usersResult.data.length,
        adminCount: adminResult.data.length,
        message: '数据库连接正常',
        collections: collections
      }
    };
  } catch (error) {
    console.error('检查数据库失败:', error);
    return {
      success: false,
      message: '数据库连接失败: ' + error.message,
      error: error.toString()
    };
  }
};

// 获取用户信息
const getUserInfo = async (event) => {
  try {
    const { userId } = event;
    
    const userResult = await db.collection('users').doc(userId).get();
    
    if (userResult.data) {
      return {
        success: true,
        data: {
          id: userResult.data._id,
          username: userResult.data.username,
          nickName: userResult.data.nickName,
          avatarUrl: userResult.data.avatarUrl,
          role: userResult.data.role,
          createTime: userResult.data.createTime,
          lastLoginTime: userResult.data.lastLoginTime
        }
      };
    } else {
      return {
        success: false,
        message: '用户不存在'
      };
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      success: false,
      message: '获取用户信息失败: ' + error.message
    };
  }
};

// 初始化中药材数据
const initMedicinesData = async () => {
  try {
    console.log('开始初始化中药材数据...');

    // 检查 medicines 集合是否存在
    const collections = await db.listCollections();
    const hasMedicinesCollection = collections.collections.some(col => col.name === 'medicines');

    if (!hasMedicinesCollection) {
      console.log('创建 medicines 集合...');
      await db.createCollection('medicines');
    }

    // 中药材测试数据
    const medicinesData = [
      {
        name: "人参",
        category: "补气药",
        effect: "大补元气，复脉固脱，补脾益肺，生津止渴，安神益智",
        description: "人参为五加科植物人参的干燥根和根茎。具有大补元气，复脉固脱，补脾益肺，生津止渴，安神益智的功效。",
        price: 12.5,
        stock: 500,
        imageUrl: "/images/medicine1.jpg",
        viewCount: 1250,
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        name: "枸杞子",
        category: "补血药",
        effect: "滋补肝肾，益精明目",
        description: "枸杞子为茄科植物宁夏枸杞的干燥成熟果实。具有滋补肝肾，益精明目的功效。",
        price: 8.8,
        stock: 1000,
        imageUrl: "/images/medicine2.jpg",
        viewCount: 980,
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        name: "当归",
        category: "补血药",
        effect: "补血活血，调经止痛，润肠通便",
        description: "当归为伞形科植物当归的干燥根。具有补血活血，调经止痛，润肠通便的功效。",
        price: 15.6,
        stock: 300,
        imageUrl: "/images/medicine3.jpg",
        viewCount: 756,
        createTime: new Date(),
        updateTime: new Date()
      }
    ];

    let importedCount = 0;
    for (const medicine of medicinesData) {
      // 检查是否已存在
      const existing = await db.collection('medicines').where({
        name: medicine.name
      }).get();

      if (existing.data.length === 0) {
        await db.collection('medicines').add({ data: medicine });
        importedCount++;
        console.log(`中药材 ${medicine.name} 导入成功`);
      } else {
        console.log(`中药材 ${medicine.name} 已存在，跳过`);
      }
    }

    return {
      success: true,
      message: `中药材数据导入完成，新增 ${importedCount} 条记录`,
      data: { importedCount, totalCount: medicinesData.length }
    };

  } catch (error) {
    console.error('初始化中药材数据失败:', error);
    return {
      success: false,
      message: '初始化中药材数据失败: ' + error.message
    };
  }
};

// 诊断函数 - 详细检查系统状态
const diagnoseSystem = async () => {
  try {
    console.log('开始系统诊断...');

    const diagnosis = {
      timestamp: new Date().toISOString(),
      cloudFunction: {
        name: 'simpleFunction',
        status: 'running'
      },
      database: {},
      collections: {},
      users: {},
      permissions: {}
    };

    // 1. 检查数据库连接
    try {
      const collections = await db.listCollections();
      diagnosis.database.status = 'connected';
      diagnosis.database.collections = collections.collections.map(col => col.name);
    } catch (dbError) {
      diagnosis.database.status = 'error';
      diagnosis.database.error = dbError.message;
    }

    // 2. 检查 users 集合
    try {
      const hasUsersCollection = diagnosis.database.collections.includes('users');
      diagnosis.collections.users = {
        exists: hasUsersCollection
      };

      if (hasUsersCollection) {
        const usersData = await db.collection('users').get();
        diagnosis.collections.users.count = usersData.data.length;
        diagnosis.collections.users.data = usersData.data.map(user => ({
          username: user.username,
          role: user.role,
          createTime: user.createTime
        }));
      }
    } catch (usersError) {
      diagnosis.collections.users.error = usersError.message;
    }

    // 3. 检查管理员账号
    try {
      if (diagnosis.collections.users.exists) {
        const adminData = await db.collection('users').where({
          role: 'admin'
        }).get();

        diagnosis.users.admin = {
          exists: adminData.data.length > 0,
          count: adminData.data.length
        };

        if (adminData.data.length > 0) {
          diagnosis.users.admin.usernames = adminData.data.map(admin => admin.username);
        }
      }
    } catch (adminError) {
      diagnosis.users.admin = { error: adminError.message };
    }

    // 4. 测试权限
    try {
      // 尝试创建一个测试记录
      const testResult = await db.collection('test_permissions').add({
        data: { test: true, timestamp: new Date() }
      });

      // 立即删除测试记录
      await db.collection('test_permissions').doc(testResult._id).remove();

      diagnosis.permissions.write = true;
      diagnosis.permissions.read = true;
    } catch (permError) {
      diagnosis.permissions.error = permError.message;
      diagnosis.permissions.write = false;
    }

    return {
      success: true,
      message: '系统诊断完成',
      data: diagnosis
    };

  } catch (error) {
    console.error('系统诊断失败:', error);
    return {
      success: false,
      message: '系统诊断失败: ' + error.message,
      error: error.toString()
    };
  }
};

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('云函数调用:', event);
  console.log('调用上下文:', context);

  try {
    switch (event.type) {
      case "initTestUsers":
        return await initTestUsers();

      case "initMedicinesData":
        return await initMedicinesData();

      case "userLogin":
        return await userLogin(event);

      case "adminLogin":
        return await adminLogin(event);

      case "checkDatabase":
        return await checkDatabase();

      case "getUserInfo":
        return await getUserInfo(event);

      case "diagnoseSystem":
        return await diagnoseSystem();

      default:
        return {
          success: false,
          message: '未知的操作类型: ' + event.type,
          availableTypes: [
            'initTestUsers',
            'initMedicinesData',
            'userLogin',
            'adminLogin',
            'checkDatabase',
            'getUserInfo',
            'diagnoseSystem'
          ]
        };
    }
  } catch (error) {
    console.error('云函数执行失败:', error);
    return {
      success: false,
      message: '云函数执行失败: ' + error.message,
      error: error.toString(),
      stack: error.stack
    };
  }
};

const cloud = require("wx-server-sdk");
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
});

const db = cloud.database();
// 获取openid
const getOpenId = async () => {
  // 获取基础信息
  const wxContext = cloud.getWXContext();
  return {
    openid: wxContext.OPENID,
    appid: wxContext.APPID,
    unionid: wxContext.UNIONID,
  };
};

// 获取小程序二维码
const getMiniProgramCode = async () => {
  // 获取小程序二维码的buffer
  const resp = await cloud.openapi.wxacode.get({
    path: "pages/index/index",
  });
  const { buffer } = resp;
  // 将图片上传云存储空间
  const upload = await cloud.uploadFile({
    cloudPath: "code.png",
    fileContent: buffer,
  });
  return upload.fileID;
};

// 创建集合
const createCollection = async () => {
  try {
    // 创建集合
    await db.createCollection("sales");
    await db.collection("sales").add({
      // data 字段表示需新增的 JSON 数据
      data: {
        region: "华东",
        city: "上海",
        sales: 11,
      },
    });
    await db.collection("sales").add({
      // data 字段表示需新增的 JSON 数据
      data: {
        region: "华东",
        city: "南京",
        sales: 11,
      },
    });
    await db.collection("sales").add({
      // data 字段表示需新增的 JSON 数据
      data: {
        region: "华南",
        city: "广州",
        sales: 22,
      },
    });
    await db.collection("sales").add({
      // data 字段表示需新增的 JSON 数据
      data: {
        region: "华南",
        city: "深圳",
        sales: 22,
      },
    });
    return {
      success: true,
    };
  } catch (e) {
    // 这里catch到的是该collection已经存在，从业务逻辑上来说是运行成功的，所以catch返回success给前端，避免工具在前端抛出异常
    return {
      success: true,
      data: "create collection success",
    };
  }
};

// 查询数据
const selectRecord = async () => {
  // 返回数据库查询结果
  return await db.collection("sales").get();
};

// 更新数据
const updateRecord = async (event) => {
  try {
    // 遍历修改数据库信息
    for (let i = 0; i < event.data.length; i++) {
      await db
        .collection("sales")
        .where({
          _id: event.data[i]._id,
        })
        .update({
          data: {
            sales: event.data[i].sales,
          },
        });
    }
    return {
      success: true,
      data: event.data,
    };
  } catch (e) {
    return {
      success: false,
      errMsg: e,
    };
  }
};

// 新增数据
const insertRecord = async (event) => {
  try {
    const insertRecord = event.data;
    // 插入数据
    await db.collection("sales").add({
      data: {
        region: insertRecord.region,
        city: insertRecord.city,
        sales: Number(insertRecord.sales),
      },
    });
    return {
      success: true,
      data: event.data,
    };
  } catch (e) {
    return {
      success: false,
      errMsg: e,
    };
  }
};

// 删除数据
const deleteRecord = async (event) => {
  try {
    await db
      .collection("sales")
      .where({
        _id: event.data._id,
      })
      .remove();
    return {
      success: true,
    };
  } catch (e) {
    return {
      success: false,
      errMsg: e,
    };
  }
};

// const getOpenId = require('./getOpenId/index');
// const getMiniProgramCode = require('./getMiniProgramCode/index');
// const createCollection = require('./createCollection/index');
// const selectRecord = require('./selectRecord/index');
// const updateRecord = require('./updateRecord/index');
// const sumRecord = require('./sumRecord/index');
// const fetchGoodsList = require('./fetchGoodsList/index');
// const genMpQrcode = require('./genMpQrcode/index');
// 用户登录
const userLogin = async (event) => {
  try {
    const { username, password } = event;

    // 查询用户
    const userResult = await db.collection('users').where({
      username: username,
      password: password // 实际项目中应该使用加密密码
    }).get();

    if (userResult.data.length > 0) {
      const user = userResult.data[0];
      return {
        success: true,
        data: {
          id: user._id,
          username: user.username,
          nickName: user.nickName,
          avatarUrl: user.avatarUrl,
          role: user.role || 'user'
        }
      };
    } else {
      return {
        success: false,
        message: '用户名或密码错误'
      };
    }
  } catch (error) {
    return {
      success: false,
      message: '登录失败'
    };
  }
};

// 微信登录
const wxLogin = async (event) => {
  try {
    const { code, userInfo } = event;
    const wxContext = cloud.getWXContext();

    // 检查用户是否已存在
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    let userData;
    if (userResult.data.length > 0) {
      // 用户已存在，更新信息
      userData = userResult.data[0];
      await db.collection('users').doc(userData._id).update({
        data: {
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          lastLoginTime: new Date()
        }
      });
    } else {
      // 新用户，创建记录
      const addResult = await db.collection('users').add({
        data: {
          openid: wxContext.OPENID,
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          role: 'user',
          createTime: new Date(),
          lastLoginTime: new Date()
        }
      });
      userData = {
        _id: addResult._id,
        openid: wxContext.OPENID,
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        role: 'user'
      };
    }

    return {
      success: true,
      data: {
        id: userData._id,
        nickName: userData.nickName,
        avatarUrl: userData.avatarUrl,
        role: userData.role || 'user'
      }
    };
  } catch (error) {
    return {
      success: false,
      message: '微信登录失败'
    };
  }
};

// 管理员登录
const adminLogin = async (event) => {
  try {
    const { username, password } = event;

    // 查询管理员用户
    const adminResult = await db.collection('users').where({
      username: username,
      password: password,
      role: 'admin'
    }).get();

    if (adminResult.data.length > 0) {
      const admin = adminResult.data[0];
      return {
        success: true,
        data: {
          id: admin._id,
          username: admin.username,
          nickName: admin.nickName,
          avatarUrl: admin.avatarUrl,
          role: 'admin'
        }
      };
    } else {
      return {
        success: false,
        message: '管理员账号或密码错误'
      };
    }
  } catch (error) {
    return {
      success: false,
      message: '管理员登录失败'
    };
  }
};

// 添加中药材
const addMedicine = async (event) => {
  try {
    const medicineData = event.data;
    const result = await db.collection('medicines').add({
      data: medicineData
    });
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      message: '添加中药材失败'
    };
  }
};

// 更新中药材
const updateMedicine = async (event) => {
  try {
    const { id, ...updateData } = event.data;
    await db.collection('medicines').doc(id).update({
      data: updateData
    });
    return {
      success: true
    };
  } catch (error) {
    return {
      success: false,
      message: '更新中药材失败'
    };
  }
};

// 删除中药材
const deleteMedicine = async (event) => {
  try {
    await db.collection('medicines').doc(event.id).remove();
    return {
      success: true
    };
  } catch (error) {
    return {
      success: false,
      message: '删除中药材失败'
    };
  }
};

// 添加文创产品
const addProduct = async (event) => {
  try {
    const productData = event.data;
    const result = await db.collection('products').add({
      data: productData
    });
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      message: '添加文创产品失败'
    };
  }
};

// 更新文创产品
const updateProduct = async (event) => {
  try {
    const { id, ...updateData } = event.data;
    await db.collection('products').doc(id).update({
      data: updateData
    });
    return {
      success: true
    };
  } catch (error) {
    return {
      success: false,
      message: '更新文创产品失败'
    };
  }
};

// 删除文创产品
const deleteProduct = async (event) => {
  try {
    await db.collection('products').doc(event.id).remove();
    return {
      success: true
    };
  } catch (error) {
    return {
      success: false,
      message: '删除文创产品失败'
    };
  }
};

// 添加养生文章
const addArticle = async (event) => {
  try {
    const articleData = event.data;
    const result = await db.collection('articles').add({
      data: articleData
    });
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      message: '添加养生文章失败'
    };
  }
};

// 更新养生文章
const updateArticle = async (event) => {
  try {
    const { id, ...updateData } = event.data;
    await db.collection('articles').doc(id).update({
      data: updateData
    });
    return {
      success: true
    };
  } catch (error) {
    return {
      success: false,
      message: '更新养生文章失败'
    };
  }
};

// 删除养生文章
const deleteArticle = async (event) => {
  try {
    await db.collection('articles').doc(event.id).remove();
    return {
      success: true
    };
  } catch (error) {
    return {
      success: false,
      message: '删除养生文章失败'
    };
  }
};

// 创建订单
const createOrder = async (event) => {
  try {
    const orderData = event.orderData;
    const result = await db.collection('orders').add({
      data: orderData
    });
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      message: '创建订单失败'
    };
  }
};

// 获取订单列表
const getOrders = async (event) => {
  try {
    const { userId, status, page = 1, pageSize = 10 } = event;

    let query = db.collection('orders').where({
      userId: userId
    });

    if (status) {
      query = query.where({
        status: status
      });
    }

    const result = await query
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      message: '获取订单列表失败'
    };
  }
};

// 获取订单详情
const getOrderDetail = async (event) => {
  try {
    const { orderId } = event;
    const result = await db.collection('orders').doc(orderId).get();

    if (result.data) {
      return {
        success: true,
        data: result.data
      };
    } else {
      return {
        success: false,
        message: '订单不存在'
      };
    }
  } catch (error) {
    return {
      success: false,
      message: '获取订单详情失败'
    };
  }
};

// 取消订单
const cancelOrder = async (event) => {
  try {
    const { orderId, reason } = event;
    await db.collection('orders').doc(orderId).update({
      data: {
        status: 'cancelled',
        cancelReason: reason,
        cancelTime: new Date(),
        updateTime: new Date()
      }
    });

    return {
      success: true
    };
  } catch (error) {
    return {
      success: false,
      message: '取消订单失败'
    };
  }
};

// 确认收货
const confirmReceive = async (event) => {
  try {
    const { orderId } = event;
    await db.collection('orders').doc(orderId).update({
      data: {
        status: 'completed',
        completeTime: new Date(),
        updateTime: new Date()
      }
    });

    return {
      success: true
    };
  } catch (error) {
    return {
      success: false,
      message: '确认收货失败'
    };
  }
};

// 获取物流信息
const getLogisticsInfo = async (event) => {
  try {
    const { orderId } = event;

    // 这里应该调用真实的物流API
    // 暂时返回模拟数据
    const mockData = {
      logisticsInfo: {
        statusIcon: '/images/shipping-icon.png',
        statusText: '运输中',
        statusDesc: '您的包裹正在配送途中，请耐心等待',
        companyName: '顺丰速运',
        companyLogo: '/images/sf-logo.png',
        trackingNumber: 'SF1234567890123',
        servicePhone: '95338',
        estimatedTime: '今天 18:00 前送达'
      },
      addressInfo: {
        name: '张三',
        phone: '138****8888',
        fullAddress: '北京市朝阳区三里屯街道某某小区1号楼101室'
      },
      trackList: [
        {
          date: '03-16',
          time: '14:30',
          description: '快件已到达【北京朝阳三里屯营业点】，正在派送中',
          location: '北京朝阳三里屯营业点'
        },
        {
          date: '03-16',
          time: '09:15',
          description: '快件已到达【北京转运中心】',
          location: '北京转运中心'
        }
      ],
      orderGoods: [
        {
          id: 'item_1',
          name: '人参',
          imageUrl: '/images/medicine1.jpg'
        }
      ],
      canConfirmReceive: true
    };

    return {
      success: true,
      data: mockData
    };
  } catch (error) {
    return {
      success: false,
      message: '获取物流信息失败'
    };
  }
};

// 云函数入口函数
exports.main = async (event, context) => {
  switch (event.type) {
    case "getOpenId":
      return await getOpenId();
    case "getMiniProgramCode":
      return await getMiniProgramCode();
    case "createCollection":
      return await createCollection();
    case "selectRecord":
      return await selectRecord();
    case "updateRecord":
      return await updateRecord(event);
    case "insertRecord":
      return await insertRecord(event);
    case "deleteRecord":
      return await deleteRecord(event);

    // 用户相关
    case "userLogin":
      return await userLogin(event);
    case "wxLogin":
      return await wxLogin(event);
    case "adminLogin":
      return await adminLogin(event);

    // 中药材相关
    case "addMedicine":
      return await addMedicine(event);
    case "updateMedicine":
      return await updateMedicine(event);
    case "deleteMedicine":
      return await deleteMedicine(event);

    // 文创产品相关
    case "addProduct":
      return await addProduct(event);
    case "updateProduct":
      return await updateProduct(event);
    case "deleteProduct":
      return await deleteProduct(event);

    // 养生文章相关
    case "addArticle":
      return await addArticle(event);
    case "updateArticle":
      return await updateArticle(event);
    case "deleteArticle":
      return await deleteArticle(event);

    // 订单相关
    case "createOrder":
      return await createOrder(event);
    case "getOrders":
      return await getOrders(event);
    case "getOrderDetail":
      return await getOrderDetail(event);
    case "cancelOrder":
      return await cancelOrder(event);
    case "confirmReceive":
      return await confirmReceive(event);
    case "getLogisticsInfo":
      return await getLogisticsInfo(event);

    default:
      return {
        success: false,
        message: '未知的操作类型'
      };
  }
};
